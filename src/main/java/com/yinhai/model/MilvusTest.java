package com.yinhai.model;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import io.milvus.v2.client.ConnectConfig;
import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.common.DataType;
import io.milvus.v2.common.IndexParam;
import io.milvus.v2.service.collection.request.AddFieldReq;
import io.milvus.v2.service.collection.request.CreateCollectionReq;
import io.milvus.v2.service.collection.request.DropCollectionReq;
import io.milvus.v2.service.collection.request.LoadCollectionReq;
import io.milvus.v2.service.vector.request.InsertReq;
import io.milvus.v2.service.vector.response.InsertResp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class MilvusTest {


    public static void main(String[] args) {
        MilvusClientV2 milvusClient = new MilvusClientV2(
                ConnectConfig.builder()
                        .uri("http://172.20.23.119:19530")
                        .token("root:Milvus")
                        .dbName("default")
                        .build()
        );

        String demoCollection = "demo_collection";
        milvusClient.dropCollection(DropCollectionReq.builder().collectionName(demoCollection).build());
        milvusClient.dropCollection(DropCollectionReq.builder().collectionName("ta404_method_1").build());
        milvusClient.dropCollection(DropCollectionReq.builder().collectionName("ta404_class_1").build());

//        CreateCollectionReq.CollectionSchema demoSchema = milvusClient.createSchema();
//        demoSchema.addField(AddFieldReq.builder()
//                .fieldName("id")
//                .dataType(DataType.Int64)
//                .isPrimaryKey(Boolean.TRUE)
//                .autoID(Boolean.TRUE)
//                .description("Primary key")
//                .build());
//        demoSchema.addField(AddFieldReq.builder()
//                .fieldName("source_code")
//                .dataType(DataType.VarChar)
//                .description("Source code content")
//                .build());
//        demoSchema.addField(AddFieldReq.builder()
//                .fieldName("method_embeddings")
//                .dataType(DataType.FloatVector)
//                .dimension(5)
//                .description("Method embedding vectors")
//                .build());
//
//        IndexParam indexParam = IndexParam.builder()
//                .fieldName("method_embeddings")
//                .indexName("method_embeddings_idx")
//                .metricType(IndexParam.MetricType.COSINE)
//                .indexType(IndexParam.IndexType.HNSW)
//                .build();
//        List<IndexParam> indexParams = new ArrayList<>();
//        indexParams.add(indexParam);
//
//        // Create method collection
//        CreateCollectionReq methodCollectionReq = CreateCollectionReq.builder()
//                .collectionName(demoCollection)
//                .collectionSchema(demoSchema)
////                .indexParam(indexParam)
//                .indexParams(indexParams)
//                .build();
//        milvusClient.createCollection(methodCollectionReq);
//
//
//        List<JsonObject> data = new ArrayList<>();
//        Gson gson = new Gson();
//
//
//        JsonObject record = new JsonObject();
//        float[] embeddings = {0.1f, 0.2f, 0.3f, 0.4f, 0.5f};
//        record.add("method_embeddings", gson.toJsonTree(embeddings));
//        record.addProperty("source_code", "fwjeoifjweoifjwoie");
//        data.add(record);
//
//        milvusClient.insert(
//                InsertReq.builder()
//                        .collectionName(demoCollection)
//                        .data(data)
//                        .build());

    }
}
