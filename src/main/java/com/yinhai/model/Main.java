package com.yinhai.model;

import java.util.Arrays;
import java.util.List;

public class Main {
    public static void main(String[] args) {
        // 1. 配置模型路径(替换为您的实际路径)
//        String modelDir = "/Users/<USER>/IdeaProjects/ai/tabnine-intellij/Tabnine/src/main/resources/models/codebert-base";
        String modelDir = "/Users/<USER>/IdeaProjects/ai/bge-small-zh-v1.5";

        // 2. 创建并加载模型服务
        try (VectorModelService service = new VectorModelService()) {
            service.loadModel(modelDir);

            // 3. 单条文本向量化
            String text1 = "def hello_world():\n    print('Hello, World!')";
            float[] vector1 = service.getTextVector(text1);
            System.out.println("文本1向量维度: " + vector1.length);
            System.out.println("前5个值: " + Arrays.toString(Arrays.copyOf(vector1, 5)));

            // 4. 批量文本向量化
            List<String> texts = Arrays.asList(
                "深度学习需要大量数据",
                "Transformer模型改变了NLP领域",
                "ONNX格式便于模型部署"
            );
            List<float[]> vectors = service.batchGetVectors(texts);
            System.out.println("批量处理结果数量: " + vectors.size());

        } catch (Exception e) {
            System.err.println("处理出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
}