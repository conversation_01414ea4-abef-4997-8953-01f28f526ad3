package com.yinhai.model;

import ai.djl.huggingface.tokenizers.Encoding;
import ai.djl.huggingface.tokenizers.HuggingFaceTokenizer;
import ai.djl.ndarray.NDArray;
import ai.djl.ndarray.NDList;
import ai.djl.ndarray.NDManager;
import ai.djl.translate.Batchifier;
import ai.djl.translate.Translator;
import ai.djl.translate.TranslatorContext;

import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

public class BertVectorTranslator implements Translator<String, float[]> {

    private final HuggingFaceTokenizer tokenizer;

    public BertVectorTranslator(String modelDir) {
        try {
            this.tokenizer = HuggingFaceTokenizer.builder()
                    .optTokenizerPath(Paths.get(modelDir))
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to load tokenizer", e);
        }
    }


    @Override
    public NDList batchProcessInput(TranslatorContext ctx, List<String> inputs) throws Exception {
        NDManager manager = ctx.getNDManager();

        // 使用tokenizer的批量编码方法
        Encoding[] encodings = tokenizer.batchEncode(inputs);

        // 从编码中获取inputIds和attentionMask
        long[][] inputIdsArray = new long[encodings.length][];
        long[][] attentionMaskArray = new long[encodings.length][];

        for (int i = 0; i < encodings.length; i++) {
            Encoding encoding = encodings[i];
            inputIdsArray[i] = encoding.getIds();
            attentionMaskArray[i] = encoding.getAttentionMask();
        }

        // 创建批量NDArray
        NDArray inputIds = manager.create(inputIdsArray);
        NDArray attentionMask = manager.create(attentionMaskArray);

        return new NDList(inputIds, attentionMask);
    }

    @Override
    public List<float[]> batchProcessOutput(TranslatorContext ctx, NDList list) throws Exception {
        NDArray embeddings = list.get(0);
        int batchSize = (int) embeddings.getShape().get(0);

        // 提取每个样本的[CLS]向量
        List<float[]> results = Arrays.asList(new float[batchSize][]);
        for (int i = 0; i < batchSize; i++) {
            NDArray clsVector = embeddings.get(i).get(0); // 获取第i个样本的[CLS]向量
            results.set(i, clsVector.toFloatArray());
        }

        return results;
    }

    @Override
    public NDList processInput(TranslatorContext ctx, String input) {
        NDManager manager = ctx.getNDManager();

        Encoding encoding = tokenizer.encode(input);

        NDArray inputIds = manager.create(encoding.getIds());
        NDArray attentionMask = manager.create(encoding.getAttentionMask());

        // 不手动添加 batch 维度，交给 Predictor 或 Batchifier 处理
        return new NDList(inputIds, attentionMask);
    }

    @Override
    public float[] processOutput(TranslatorContext ctx, NDList list) {
        NDArray embeddings = list.get(0);
        NDArray clsVector = embeddings.get(0); // 提取 [CLS] token 的向量
        return clsVector.toFloatArray();
    }

    @Override
    public Batchifier getBatchifier() {
        return Batchifier.STACK; // 保持批量处理功能
    }
}