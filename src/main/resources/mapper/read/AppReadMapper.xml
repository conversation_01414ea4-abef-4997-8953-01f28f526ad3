<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinhai.ta404.demo.mapper.read.AppReadMapper">

    <!-- Result Map -->
    <resultMap type="com.yinhai.ta404.demo.entity.AppPo" id="AppMap">
        <id property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="clusterId" column="cluster_id" jdbcType="VARCHAR"/>
        <result property="namespaceId" column="namespace_id" jdbcType="VARCHAR"/>
        <result property="appName" column="app_name" jdbcType="VARCHAR"/>
        <result property="appType" column="app_type" jdbcType="VARCHAR"/>
        <result property="appIdentify" column="app_identify" jdbcType="VARCHAR"/>
        <result property="appDesc" column="app_desc" jdbcType="VARCHAR"/>
        <result property="appCreateUser" column="app_create_user" jdbcType="VARCHAR"/>
        <result property="appCreateTime" column="app_create_time" jdbcType="TIMESTAMP"/>
        <result property="appManager" column="app_manager" jdbcType="VARCHAR"/>
        <result property="appCpuLimit" column="app_cpu_limit" jdbcType="DOUBLE"/>
        <result property="appMemoryLimit" column="app_memory_limit" jdbcType="DOUBLE"/>
        <result property="startcommand" column="startcommand" jdbcType="VARCHAR"/>
        <result property="startArg" column="start_arg" jdbcType="VARCHAR"/>
        <result property="nodeSelector" column="node_selector" jdbcType="VARCHAR"/>
        <result property="totalinstancenum" column="totalinstancenum" jdbcType="INTEGER"/>
        <result property="podAntiAffinity" column="pod_anti_affinity" jdbcType="VARCHAR"/>
        <result property="appStatus" column="app_status" jdbcType="VARCHAR"/>
        <result property="imagePath" column="image_path" jdbcType="VARCHAR"/>
        <result property="appTags" column="app_tags" jdbcType="VARCHAR"/>
        <result property="hostAliases" column="host_aliases" jdbcType="VARCHAR"/>
        <result property="appPath" column="app_path" jdbcType="VARCHAR"/>
        <result property="applyClosedTime" column="apply_closed_time" jdbcType="INTEGER"/>
    </resultMap>

    <!-- SQL Statements -->
    <select id="selectAppById" resultMap="AppMap">
        SELECT *
        FROM cloud_app
        WHERE app_id = #{appId}
    </select>

    <select id="selectAppList" resultMap="AppMap" parameterType="com.yinhai.ta404.demo.vo.AppVo">
        SELECT *
        FROM cloud_app
        WHERE app_name = #{appName}
    </select>
</mapper>