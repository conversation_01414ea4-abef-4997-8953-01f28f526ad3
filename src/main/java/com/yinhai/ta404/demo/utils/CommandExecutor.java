package com.yinhai.ta404.demo.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

public class CommandExecutor {

    /**
     * 执行给定的命令并返回输出结果
     *
     * @param command 要执行的命令
     * @return 命令执行的输出结果
     * @throws IOException 如果执行命令时发生I/O错误
     */
    public static String executeCommand(String command) throws IOException {
        StringBuilder output = new StringBuilder();
        Process process = Runtime.getRuntime().exec(command);
        
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
        }

        return output.toString();
    }

    public static void main(String[] args) {
        try {
            String result = executeCommand("echo Hello, World!");
            System.out.println("Command Output: " + result);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}