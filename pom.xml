<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yinhai.ta404</groupId>
        <artifactId>ta404-parent</artifactId>
        <version>5.4.3-RELEASE</version>
    </parent>

    <groupId>com.yinhai</groupId>
    <artifactId>demo</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>${pom.package}</packaging>

    <name>demo</name>
    <description>Demo project for Ta+3 404</description>


    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>8</java.version>
    </properties>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.yinhai.ta404</groupId>
                <artifactId>ta404-component-parent</artifactId>
                <version>5.4.3-RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.yinhai.ta404</groupId>
            <artifactId>ta404-component-captcha-springboot-starter</artifactId>
            <version>5.4.3-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.yinhai.ta404</groupId>
            <artifactId>ta404-component-dictmg</artifactId>
            <version>5.4.3-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.yinhai.ta404</groupId>
            <artifactId>ta404-component-domain-core-orguserauth</artifactId>
            <version>5.4.3-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.yinhai.ta404</groupId>
            <artifactId>ta404-component-logmg</artifactId>
            <version>5.4.3-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.yinhai.ta404</groupId>
            <artifactId>ta404-component-security-domain</artifactId>
            <version>5.4.3-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.yinhai.ta404</groupId>
            <artifactId>ta404-component-security-session</artifactId>
            <version>5.4.3-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.yinhai.ta404</groupId>
            <artifactId>ta404-core</artifactId>
            <version>5.4.3-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.yinhai.ta404</groupId>
            <artifactId>ta404-module-cache-starter-ehcache</artifactId>
            <version>5.4.3-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.yinhai.ta404</groupId>
            <artifactId>ta404-module-datasource</artifactId>
            <version>5.4.3-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.yinhai.ta404</groupId>
            <artifactId>ta404-module-websecurity</artifactId>
            <version>5.4.3-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.30</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.3.8</version>
        </dependency>
        <!-- 默认固定依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>


        <dependency>
            <groupId>ai.djl</groupId>
            <artifactId>api</artifactId>
            <version>0.28.0</version>
        </dependency>
        <dependency>
            <groupId>ai.djl.onnxruntime</groupId>
            <artifactId>onnxruntime-engine</artifactId>
            <version>0.28.0</version>
        </dependency>
        <dependency>
            <groupId>ai.djl.huggingface</groupId>
            <artifactId>tokenizers</artifactId>
            <version>0.28.0</version>
        </dependency>

        <dependency>
            <groupId>com.openai</groupId>
            <artifactId>openai-java</artifactId>
            <version>0.43.0</version>
        </dependency>

        <dependency>
            <groupId>io.milvus</groupId>
            <artifactId>milvus-sdk-java</artifactId>
            <version>2.5.7</version>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>32.0.0-jre</version>
        </dependency>

    </dependencies>

    <profiles>
        <profile>
            <id>jar</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <pom.package>jar</pom.package>
            </properties>
        </profile>
        <profile>
            <id>war</id>
            <properties>
                <pom.package>war</pom.package>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <scope>provided</scope>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <!--热启动加载-->
                    <fork>true</fork>
                    <!-- boot 模式启动 -->
                    <mainClass>com.yinhai.DemoApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>
