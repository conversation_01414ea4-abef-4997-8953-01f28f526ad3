<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinhai.ta404.demo.mapper.write.AppWriteMapper">

    <!-- Result Map -->
    <resultMap type="com.yinhai.ta404.demo.entity.AppPo" id="AppMap">
        <id property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="clusterId" column="cluster_id" jdbcType="VARCHAR"/>
        <result property="namespaceId" column="namespace_id" jdbcType="VARCHAR"/>
        <result property="appName" column="app_name" jdbcType="VARCHAR"/>
        <result property="appType" column="app_type" jdbcType="VARCHAR"/>
        <result property="appIdentify" column="app_identify" jdbcType="VARCHAR"/>
        <result property="appDesc" column="app_desc" jdbcType="VARCHAR"/>
        <result property="appCreateUser" column="app_create_user" jdbcType="VARCHAR"/>
        <result property="appCreateTime" column="app_create_time" jdbcType="TIMESTAMP"/>
        <result property="appManager" column="app_manager" jdbcType="VARCHAR"/>
        <result property="appCpuLimit" column="app_cpu_limit" jdbcType="DOUBLE"/>
        <result property="appMemoryLimit" column="app_memory_limit" jdbcType="DOUBLE"/>
        <result property="startcommand" column="startcommand" jdbcType="VARCHAR"/>
        <result property="startArg" column="start_arg" jdbcType="VARCHAR"/>
        <result property="nodeSelector" column="node_selector" jdbcType="VARCHAR"/>
        <result property="totalinstancenum" column="totalinstancenum" jdbcType="INTEGER"/>
        <result property="podAntiAffinity" column="pod_anti_affinity" jdbcType="VARCHAR"/>
        <result property="appStatus" column="app_status" jdbcType="VARCHAR"/>
        <result property="imagePath" column="image_path" jdbcType="VARCHAR"/>
        <result property="appTags" column="app_tags" jdbcType="VARCHAR"/>
        <result property="hostAliases" column="host_aliases" jdbcType="VARCHAR"/>
        <result property="appPath" column="app_path" jdbcType="VARCHAR"/>
        <result property="applyClosedTime" column="apply_closed_time" jdbcType="INTEGER"/>
    </resultMap>

    <insert id="insertApp" parameterType="com.yinhai.ta404.demo.entity.AppPo">
        INSERT INTO cloud_app (app_id, cluster_id, namespace_id, app_name, app_type, app_identify, app_desc,
                               app_create_user, app_create_time, app_manager, app_cpu_limit, app_memory_limit, startcommand,
                               start_arg, node_selector, totalinstancenum, pod_anti_affinity, app_status, image_path,
                               app_tags, host_aliases, app_path, apply_closed_time)
        VALUES (#{appId,jdbcType=VARCHAR}, #{clusterId,jdbcType=VARCHAR}, #{namespaceId,jdbcType=VARCHAR},
                #{appName,jdbcType=VARCHAR}, #{appType,jdbcType=VARCHAR}, #{appIdentify,jdbcType=VARCHAR},
                #{appDesc,jdbcType=VARCHAR}, #{appCreateUser,jdbcType=VARCHAR}, #{appCreateTime,jdbcType=TIMESTAMP},
                #{appManager,jdbcType=VARCHAR}, #{appCpuLimit,jdbcType=DOUBLE}, #{appMemoryLimit,jdbcType=DOUBLE},
                #{startcommand,jdbcType=VARCHAR}, #{startArg,jdbcType=VARCHAR}, #{nodeSelector,jdbcType=VARCHAR},
                #{totalinstancenum,jdbcType=INTEGER}, #{podAntiAffinity,jdbcType=VARCHAR}, #{appStatus,jdbcType=VARCHAR},
                #{imagePath,jdbcType=VARCHAR}, #{appTags,jdbcType=VARCHAR}, #{hostAliases,jdbcType=VARCHAR},
                #{appPath,jdbcType=VARCHAR}, #{applyClosedTime,jdbcType=INTEGER})
    </insert>

    <update id="updateApp" parameterType="com.yinhai.ta404.demo.entity.AppPo">
        UPDATE cloud_app
        SET cluster_id        = #{clusterId,jdbcType=VARCHAR},
            namespace_id      = #{namespaceId,jdbcType=VARCHAR},
            app_name          = #{appName,jdbcType=VARCHAR},
            app_type          = #{appType,jdbcType=VARCHAR},
            app_identify      = #{appIdentify,jdbcType=VARCHAR},
            app_desc          = #{appDesc,jdbcType=VARCHAR},
            app_create_user   = #{appCreateUser,jdbcType=VARCHAR},
            app_create_time   = #{appCreateTime,jdbcType=TIMESTAMP},
            app_manager       = #{appManager,jdbcType=VARCHAR},
            app_cpu_limit     = #{appCpuLimit,jdbcType=DOUBLE},
            app_memory_limit  = #{appMemoryLimit,jdbcType=DOUBLE},
            startcommand      = #{startcommand,jdbcType=VARCHAR},
            start_arg         = #{startArg,jdbcType=VARCHAR},
            node_selector     = #{nodeSelector,jdbcType=VARCHAR},
            totalinstancenum  = #{totalinstancenum,jdbcType=INTEGER},
            pod_anti_affinity = #{podAntiAffinity,jdbcType=VARCHAR},
            app_status        = #{appStatus,jdbcType=VARCHAR},
            image_path        = #{imagePath,jdbcType=VARCHAR},
            app_tags          = #{appTags,jdbcType=VARCHAR},
            host_aliases      = #{hostAliases,jdbcType=VARCHAR},
            app_path          = #{appPath,jdbcType=VARCHAR},
            apply_closed_time = #{applyClosedTime,jdbcType=INTEGER}
        WHERE app_id = #{appId,jdbcType=VARCHAR}
    </update>

    <delete id="deleteAppById">
        DELETE
        FROM cloud_app
        WHERE app_id = #{appId,jdbcType=VARCHAR}
    </delete>

</mapper>