ta404:
  modules:
    cache:
      # SpringCache注解默认支持的缓存(后续需要启用primary对应的缓存配置eg: ehcache.active=true)
      primary: ehcache
      ehcache:
        # 是否启动ehcache(默认启用)
        active: true
        config-location: cache/ehcache.xml
      caffeine:
        # 是否启动caffeine(默认不启用)
        active: false
        # 初始的缓存空间大小(条数)
        initial-capacity: 1000
        # 最大缓存条数
        maximum-size: 10000
        # 最后一次写入后经过固定时间过期(单位:min)
        expire-after-write: 10
        # 最后一次访问后经过固定时间过期(单位:min)
        expire-after-access: 10
        # 是否开启缓存统计
        record-stats: false
        #单个缓存自定义参数
        define:
          taStatisticAnalysisCache:
            expire-after-access: 600
          taPermissionCache:
            expire-after-access: 600
#          taOrgCache:
#            expire-after-access: 600
#          taUserCache:
#            expire-after-access: 600
#          taResourceCache:
#            expire-after-access: 600
#          taRoleCache:
#            expire-after-access: 600

      redis:
        # 是否启动redis(默认不启用)
        active: false
        # redis缓存模式single、cluster
        type: single
        # SpringCache注解缓存失效时间(秒)，0为不失效
        expire: 0
        #单个缓存自定义参数
        define:
          taStatisticAnalysisCache:
            expire: 600
          taPermissionCache:
            expire: 600
#          taOrgCache:
#            expire: 600
#          taUserCache:
#            expire: 600
#          taResourceCache:
#            expire: 600
#          taRoleCache:
#            expire: 600
        # 单点模式
        single:
          host-name: 127.0.0.1
          port: 30010
          # 口令(可选)
          password: mypassword
          # 指定redis数据库（仅单节点可用，默认为0，集群模式不支持指定数据库）
          # 0开发环境，3kingbase,8dm,1mysql57
          db-index: 7
          #          db-index: 1
        # 集群cluster模式
        cluster:
          # 连接池最大连接数
          max-redirects: 5
          # 集群节点(redis模式仅支持集群模式)
          cluster-nodes[0]: 127.0.0.1:6379 #master
          cluster-nodes[1]: 127.0.0.1:6380 #master
          cluster-nodes[2]: 127.0.0.1:6381 #master
          cluster-nodes[3]: 127.0.0.1:6382 #slave
          cluster-nodes[4]: 127.0.0.1:6383 #slave
          cluster-nodes[5]: 127.0.0.1:6384 #slave
          # 口令(可选)
          password: test
        lettuce:
          io:
            # (可选)io操作线程数，空默认为cpu的availableProcessors，决定lettuce-eventExecutorLoop大小，最小值2
            ioThreadPoolSize:
            # (可选)决定lettuce-nioEventLoop大小，空默认为cpu的availableProcessors，最小值2
            computationThreadPoolSize:
          pool:
            # 连接池的最大数据库连接数，默认 8
            maxTotal: 100
            # 最大空闲连接数，默认 8
            maxIdle: 20
            # 最小空闲连接数，默认 0
            minIdle: 10
            # 连接耗尽时是否阻塞, false报异常,ture阻塞直到超时, 默认true
            blockWhenExhausted: true
            # 最大建立连接等待时间
            maxWaitMillis: 3000
            # 是否在从池中取出连接实例前进行检验,如果检验失败,则从池中去除连接并尝试取出另一个
            testOnBorrow: false
            # 是否在return连接实例给pool前进行检验
            testOnReturn: false
            # 在空闲时检查有效性, 校验失败会从pool中drop掉 默认false（这一项只有在timeBetweenEvictionRunsMillis大于0时才有意义）
            testWhileIdle: true
            # 逐出连接的最小空闲时间 默认1800000毫秒(30分钟) ；（这一项只有在timeBetweenEvictionRunsMillis大于0时才有意义）
            minEvictableIdleTimeMillis: 60000
            # 逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1
            timeBetweenEvictionRunsMillis: 30000
            # 每次逐出检查时 逐出的最大数目 如果为负数就是 : 1/abs(n), 默认3
            numTestsPerEvictionRun: -1

