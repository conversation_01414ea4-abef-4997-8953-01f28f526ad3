package com.yinhai.ta404.demo.service.read.impl;

import com.yinhai.ta404.demo.entity.AppPo;
import com.yinhai.ta404.demo.mapper.read.AppReadMapper;
import com.yinhai.ta404.demo.service.read.AppReadService;

import com.yinhai.ta404.demo.vo.AppVo;
import com.yinhai.ta404.core.transaction.annotation.NoTransactional;
import com.yinhai.ta404.core.utils.ObjectConversionUtil;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * (CloudApp)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-16 10:14:08
 */
@Service
@NoTransactional
public class AppReadServiceImpl implements AppReadService {
    @Resource
    private AppReadMapper appReadMapper;

    /**
     * 根据应用ID查询云应用信息
     *
     * @param appId 应用的唯一标识符
     * @return 返回对应的应用信息，如果未找到则返回null
     */
    public AppVo queryAppInfoById(String appId) {
        // 从数据库中根据应用ID获取应用持久化对象
        AppPo appPo = appReadMapper.selectAppById(appId);

        // 如果未找到应用持久化对象，则返回null
        if (appPo == null) {
            return null;
        }

        // 创建一个应用视图对象
        AppVo appVo = new AppVo();

        // 将持久化对象转换为视图对象
        ObjectConversionUtil.convert(appVo, appPo);

        // 返回转换后的应用视图对象
        return appVo;
    }





}
