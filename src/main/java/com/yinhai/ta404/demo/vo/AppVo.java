package com.yinhai.ta404.demo.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AppVo implements Serializable {

    private static final long serialVersionUID = 320964559939484482L;
    private String appId;
    private String clusterId;
    private String namespaceId;
    private String appName;
    private String appType;
    private String appIdentify;
    private String appDesc;
    private String appCreateUser;
    private Date appCreateTime;
    private String appManager;
    private Double appCpuLimit;
    private Double appMemoryLimit;
    private String startcommand;
    private String startArg;
    private String nodeSelector;
    private Integer totalinstancenum;
    private String podAntiAffinity;
    private String appStatus;
    private String imagePath;
    private String appTags;
    private String hostAliases;
    private String appPath;
    private Integer applyClosedTime;
    private Integer appPriority;

}
