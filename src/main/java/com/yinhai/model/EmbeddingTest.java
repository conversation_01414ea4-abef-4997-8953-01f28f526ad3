package com.yinhai.model;

import com.openai.client.OpenAIClient;
import com.openai.client.okhttp.OpenAIOkHttpClient;
import com.openai.core.JsonValue;
import com.openai.models.embeddings.CreateEmbeddingResponse;
import com.openai.models.embeddings.EmbeddingCreateParams;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class EmbeddingTest {


    public static void main(String[] args) {
        OpenAIClient openAIClient = OpenAIOkHttpClient.builder()
                .apiKey("pa-OqV0fIO8c5aciTVWLvSbmkVUW6Vs8ppLuuGs2QSUacE")
                .baseUrl("https://api.voyageai.com/v1")
                .build();

        List<String> clippedTexts = new ArrayList<>();
        clippedTexts.add("Example text for embedding.");
//        clippedTexts.add("This is another example text for embedding.");
//        clippedTexts.add("This is yet another example text for embedding.");

        EmbeddingCreateParams.Input input = EmbeddingCreateParams.Input.ofArrayOfStrings(clippedTexts);
        EmbeddingCreateParams createParams = EmbeddingCreateParams.builder()
                .input(input)
                .model("voyage-code-3")
                .putAdditionalBodyProperty("output_dimension", JsonValue.from(2048))
//                .dimensions(2048)
                .build();
        CreateEmbeddingResponse result = openAIClient.embeddings().create(createParams);

        List<float[]> collect = result.data().stream()
                .map(embedding -> {
                    List<Double> embeddingList = embedding.embedding();
                    float[] embeddingArray = new float[embeddingList.size()];
                    for (int i = 0; i < embeddingList.size(); i++) {
                        embeddingArray[i] = embeddingList.get(i).floatValue();
                    }
                    return embeddingArray;
                })
                .collect(Collectors.toList());
        System.out.println(collect.size() + "  " + collect.get(0).length);
    }
}
