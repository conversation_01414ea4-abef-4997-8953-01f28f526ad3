package com.yinhai.ta404.demo.mapper.read;

import com.yinhai.ta404.demo.entity.AppPo;
import com.yinhai.ta404.demo.vo.AppVo;
import com.yinhai.ta404.module.mybatis.mapper.Ta404SupportMapper;

import java.util.List;

/**
 * (CloudApp)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-16 10:14:08
 */
public interface AppReadMapper extends Ta404SupportMapper {


    /**
     * 通过ID查询单条数据
     *
     * @param appId 主键
     * @return 实例对象
     */
    AppPo selectAppById(String appId);


    /**
     * 根据条件查询应用列表
     *
     * @param appVo
     * @return 应用列表
     */
    List<AppPo> selectAppList(AppVo appVo);




}
