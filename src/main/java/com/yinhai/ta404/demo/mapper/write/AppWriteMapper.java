package com.yinhai.ta404.demo.mapper.write;

import com.yinhai.ta404.demo.entity.AppPo;

/**
* (CloudApp)表数据库访问层
*
* <AUTHOR>
* @since 2023-06-16 10:14:08
*/
public interface AppWriteMapper {
    /**
    * 新增数据
    *
    * @param cloudApp 实例对象
    * @return 影响行数
    */
    int insertApp(AppPo cloudApp);


    /**
     * 更新应用程序信息
     *
     * @param cloudApp 要更新的应用程序对象
     * @return 更新操作的结果，通常为受影响的行数
     */
    int updateApp(AppPo cloudApp);

    /**
     * 根据应用程序ID删除应用程序
     *
     * @param appId 应用程序的唯一标识符
     * @return 删除操作的结果，通常为受影响的行数
     */
    int deleteAppById(String appId);
}
