ta404:
  component:
    security:
      #开关
      mock: true
      #使用框架库
      use-ta-db: true
      custom:
        #模拟用户 相关配置
        mockuser:
          #是否自定义路径
          use-custom-url: false
          #模拟用户登录路径
          mock-user-url: /authentication/mockuser
          #配置人员帐号
          username: developer
          #配置人员id
          user-id: 1
          #配置组织id
          org-id: 1
          #角色id
          role-id: 1
  #其它配置(如果不使用框架库，需要配置关闭字典缓存)
  modules:
    #字典配置
    dict:
      #字典开关配置
      open-cache: false
