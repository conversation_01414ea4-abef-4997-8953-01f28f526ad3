package com.yinhai.ta404.demo.rest;

import com.yinhai.ta404.demo.service.read.AppReadService;
import com.yinhai.ta404.demo.service.write.AppWriteService;
import com.yinhai.ta404.demo.vo.AppVo;
import com.yinhai.ta404.core.restservice.BaseRestService;
import com.yinhai.ta404.core.restservice.annotation.RestService;
import org.springframework.web.bind.annotation.PostMapping;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;

/**
 * (CloudApp)表控制层
 *
 * <AUTHOR>
 * @since 2023-06-16 10:14:08
 */
@RestService("application")
public class AppRestService extends BaseRestService {
    /**
     * 服务对象
     */
    @Resource
    private AppReadService appReadService;
    @Resource
    private AppWriteService appWriteService;


    //新增一个app
    @PostMapping("addApp") // 映射HTTP POST请求到此方法
    public void addApp(AppVo appVo) { // 接收一个有效的AppVo对象


        //构造一个完整的appVo数据，所有字段都要构造
        appVo.setClusterId("defaultClusterId");
        appVo.setNamespaceId("defaultNamespaceId");
        appVo.setAppName("defaultAppName");
        appVo.setAppType("defaultAppType");
        appVo.setAppIdentify("defaultAppIdentify");
        appVo.setAppDesc("defaultAppDesc");
        appVo.setAppCreateUser("defaultUser");
        appVo.setAppCreateTime(new Date());
        appVo.setAppCpuLimit(1.0);
        appVo.setAppMemoryLimit(512.0);
        appVo.setStartcommand("defaultCommand");
        appVo.setStartArg("defaultArg");
        appVo.setNodeSelector("defaultNodeSelector");
        appVo.setTotalinstancenum(1);
        appVo.setPodAntiAffinity("defaultPodAntiAffinity");
        appVo.setAppStatus("active");
        appVo.setImagePath("defaultImagePath");
        appVo.setAppTags("defaultTags");
        appVo.setHostAliases("defaultHostAliases");
        appVo.setAppPath("defaultAppPath");
        appVo.setApplyClosedTime(30);


        appWriteService.addCloudAppInfo(null); // 新增应用信息
        setSuccess(true); // 设置请求响应成功标识
    }


    /**
     * 根据id查询app
     */
    @PostMapping("queryAppById") // 映射HTTP POST请求到此方法
    public void queryAppById(@Valid String appId) { // 接收一个有效的appId参数
        AppVo appVo = appReadService.queryAppInfoById(appId); // 根据appId查询应用信息
        setData("appInfo", appVo); // 将查询到的应用信息设置到响应数据中
    }



    /**
     * 更新应用信息
     *
     * @param appVo 一个有效的AppVo对象，包含应用的更新信息
     */
    @PostMapping("updateApp") // 映射HTTP POST请求到此方法
    public void updateApp(AppVo appVo) { // 接收一个有效的AppVo对象
        appWriteService.updateCloudAppInfo(appVo); // 更新应用信息
        setSuccess(true); // 设置请求响应成功标识
    }


}
