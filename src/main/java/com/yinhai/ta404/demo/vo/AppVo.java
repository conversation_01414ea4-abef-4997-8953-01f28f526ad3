package com.yinhai.ta404.demo.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (CloudApp)实体类
 *
 * <AUTHOR>
 * @since 2023-06-16 10:14:08
 */
@Data
public class AppVo implements Serializable {

    private static final long serialVersionUID = 320964559939484482L;
    //应用id
    private String appId;
    //集群id
    private String clusterId;
    //命名空间id
    private String namespaceId;
    //应用名称
    private String appName;
    //应用类型
    private String appType;
    //应用标识
    private String appIdentify;
    //应用描述
    private String appDesc;
    //创建人
    private String appCreateUser;
    //创建时间
    private Date appCreateTime;
    //应用管理人
    private String appManager;
    //应用状态
    //cpu上限
    private Double appCpuLimit;
    //内存上限
    private Double appMemoryLimit;
    //启动命令
    private String startcommand;
    //启动命令
    private String startArg;
    //节点选择
    private String nodeSelector;
    //实例数总数
    private Integer totalinstancenum;
    //亲和性
    private String podAntiAffinity;
    //应用运行状态
    private String appStatus;
    // 镜像地址
    private String imagePath;
    // 分组列表字符串
    private String appTags;
    // 域名映射
    private String hostAliases;
    // 应用路径
    private String appPath;
    // 应用关闭时间
    private Integer applyClosedTime;
    // 应用优先级
    private Integer appPriority;

}
