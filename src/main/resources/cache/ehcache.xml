<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'
        xmlns='http://www.ehcache.org/v3'
        xsi:schemaLocation="http://www.ehcache.org/v3 http://www.ehcache.org/schema/ehcache-core.xsd">

    <cache-template name="taCacheTemplate">
        <key-type>java.lang.String</key-type>
        <value-type>java.lang.Object</value-type>
        <expiry>
            <none></none>
        </expiry>
        <resources>
            <heap>200000</heap>
            <offheap unit="MB">30</offheap>
        </resources>
    </cache-template>
    <cache-template name="taCacheTemplateExpire600">
        <key-type>java.lang.String</key-type>
        <value-type>java.lang.Object</value-type>
        <expiry>
            <ttl>600</ttl>
        </expiry>
        <resources>
            <heap>200000</heap>
            <offheap unit="MB">30</offheap>
        </resources>
    </cache-template>
    <cache alias="appCodeCache" uses-template="taCacheTemplate"/>

    <!--组织缓存-->
    <cache alias="taOrgCache" uses-template="taCacheTemplate"/>

    <!--人员缓存-->
    <cache alias="taUserCache" uses-template="taCacheTemplate"/>

    <!--资源缓存-->
    <cache alias="taResourceCache" uses-template="taCacheTemplate"/>


    <!--角色缓存-->
    <cache alias="taRoleCache" uses-template="taCacheTemplate"/>

    <!--首页统计缓存-->
    <cache alias="taStatisticAnalysisCache" uses-template="taCacheTemplateExpire600"/>

    <!--鉴权缓存-->
    <cache alias="taPermissionCache" uses-template="taCacheTemplateExpire600"/>

    <!--<cache alias="app" >-->
    <!--<key-type>java.lang.String</key-type>-->
    <!--<value-type>com.yinhai.ehcache.entity.EntityDemoBean</value-type>-->
    <!--<resources>-->
    <!--<heap>200000</heap>-->
    <!--<offheap unit="MB">100</offheap>-->
    <!--</resources>-->
    <!--</cache>-->
    <!--用户锁定缓存-->
    <cache alias="userAutoUnlockCache" uses-template="taCacheTemplate"/>
    <!--登录错误次数缓存-->
    <cache alias="passwordValidationErrorNumber" uses-template="taCacheTemplate"/>

    <cache alias="reportCache">
        <key-type>java.lang.String</key-type>
        <value-type>java.util.Collection</value-type>
        <resources>
            <heap>200000</heap>
            <offheap unit="MB">10</offheap>
        </resources>
    </cache>
    <cache alias="childReportCache">
        <key-type>java.lang.String</key-type>
        <value-type>java.util.Collection</value-type>
        <resources>
            <heap>200000</heap>
            <offheap unit="MB">10</offheap>
        </resources>
    </cache>
    <cache alias="menuReportCache">
        <key-type>java.lang.String</key-type>
        <value-type>java.util.Collection</value-type>
        <resources>
            <heap>200000</heap>
            <offheap unit="MB">10</offheap>
        </resources>
    </cache>

    <cache alias="progressCache">
        <key-type>java.lang.String</key-type>
        <value-type>java.lang.Object</value-type>
        <resources>
            <heap>200000</heap>
            <offheap unit="MB">10</offheap>
        </resources>
    </cache>
    <cache alias="elasticJobCache" uses-template="taCacheTemplate"/>

    <cache alias="namewarp" uses-template="taCacheTemplate"/>
</config>
