package com.yinhai.cloud.cluster.util;

import com.yinhai.ta404.core.CommonConstants;
import com.yinhai.ta404.core.exception.AppException;
import com.yinhai.ta404.core.utils.ServiceLocator;
import com.yinhai.yhcm.common.core.properties.CloudDockerProperties;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.bouncycastle.util.encoders.Hex;

import java.io.*;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.Security;
import java.security.Signature;
import java.util.Base64;

public class TenantIdQueryUtil {

    private static final CloudDockerProperties cloudDockerProperties = ServiceLocator.getService(CloudDockerProperties.class);
    private static final String PRIVATE_KEY_PATH = "pem/pri.pem";

    static {
        // 添加BouncyCastle作为安全提供者
        Security.addProvider(new BouncyCastleProvider());
    }

    public static String queryTenantIdByPaasTenantId(String paasTenantId) throws Exception {
        // 1. 准备请求参数
        String timestamp = String.valueOf(System.currentTimeMillis());
        String requestId = RandomStringUtils.randomAlphanumeric(10);

        // 2. 加载私钥
        PrivateKey privateKey = loadPrivateKey(PRIVATE_KEY_PATH);

        // 3. 生成签名
        String sign = generateSM2Signature(privateKey, requestId + timestamp);

        // 4. 构建请求URL
        URI uri = new URIBuilder("http://" + cloudDockerProperties.getDeepflowUiProxy() + ":" + cloudDockerProperties.getDeepflowUiPort() + cloudDockerProperties.getTenantIdQueryUrl())
                .addParameter("paasTenantId", paasTenantId)
                .build();

        // 5. 创建HTTP请求并设置头部
        HttpGet httpGet = new HttpGet(uri);
        httpGet.addHeader("X-Request-Id", requestId);
        httpGet.addHeader("X-Timestamp", timestamp);
        httpGet.addHeader("X-Sign", sign);

        // 6. 发送请求并获取响应
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpResponse response = httpClient.execute(httpGet);
            if (response.getStatusLine().getStatusCode() == 200) {
                return EntityUtils.toString(response.getEntity());
            } else {
                throw new RuntimeException("请求失败，状态码: " + response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            throw new AppException(e);
        }
    }

    private static PrivateKey loadPrivateKey(String filePath) throws IOException {
        try (InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(filePath)) {
            if (inputStream == null) {
                throw new IOException("无法找到资源文件: " + filePath);
            }

            String pemContent = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            try (PEMParser pemParser = new PEMParser(new StringReader(pemContent))) {
                Object object = pemParser.readObject();
                if (object instanceof PrivateKeyInfo) {
                    PrivateKeyInfo privateKeyInfo = (PrivateKeyInfo) object;
                    return new JcaPEMKeyConverter().getPrivateKey(privateKeyInfo);
                }
                throw new IOException("无法解析私钥文件");
            }
        }
    }

    private static String generateSM2Signature(PrivateKey privateKey, String data) throws Exception {
        // 获取SM3withSM2签名实例（使用BouncyCastle提供者）
        Signature signature = Signature.getInstance("SM3withSM2", "BC");

        // 使用私钥初始化签名对象
        signature.initSign(privateKey);

        // 明确指定UTF-8编码更新待签名数据
        signature.update(data.getBytes(StandardCharsets.UTF_8));

        // 生成签名
        byte[] signed = signature.sign();

        // 返回Base64编码的签名结果
        return Base64.getEncoder().encodeToString(signed);

        // 如果需要十六进制编码，可以使用以下代码替代
        // return Hex.toHexString(signed);
    }

    public static Boolean addInputFromPaas(String paasTenantId, String data, int connectTimeout, int socketTimeout, int requestTimeout) {
        // 1. 准备请求参数
        String timestamp = String.valueOf(System.currentTimeMillis());
        String requestId = RandomStringUtils.randomAlphanumeric(10);

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 2. 加载私钥
            PrivateKey privateKey = loadPrivateKey(PRIVATE_KEY_PATH);

            // 3. 生成签名
            String sign = generateSM2Signature(privateKey, requestId + timestamp);

            // 4. 构建请求URL
            URI uri = new URIBuilder("http://" + cloudDockerProperties.getDeepflowUiProxy() + ":" + cloudDockerProperties.getDeepflowUiPort() + cloudDockerProperties.getTenantIdQueryUrl())
                    .addParameter("paasTenantId", paasTenantId)
                    .addParameter("addConfig", data)
                    .build();

            // 5. 创建HTTP请求并设置头部
            HttpGet httpGet = new HttpGet(uri);
            httpGet.addHeader("X-Request-Id", requestId);
            httpGet.addHeader("X-Timestamp", timestamp);
            httpGet.addHeader("X-Sign", sign);

            // 设置请求超时参数
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(connectTimeout)
                    .setSocketTimeout(socketTimeout)
                    .setConnectionRequestTimeout(requestTimeout)
                    .build();
            httpGet.setConfig(requestConfig);

            // 6. 发送请求并获取响应
                HttpResponse response = httpClient.execute(httpGet);
                if (response.getStatusLine().getStatusCode() == 200) {
                    return true;
                } else {
                    throw new RuntimeException("请求失败，状态码: " + response.getStatusLine().getStatusCode());
                }
        }catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 添加配置数据到PaaS（使用默认超时设置）
     * @param paasTenantId PaaS租户ID
     * @param data 要添加的数据
     * @return 是否添加成功
     */
    public static Boolean addInputFromPaas(String paasTenantId, String data) {
        // 使用默认超时设置：连接超时5秒，socket超时10秒，请求超时3秒
        return addInputFromPaas(paasTenantId, data, 5000, 10000, 3000);
    }
}
