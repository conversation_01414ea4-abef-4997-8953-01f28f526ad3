package com.yinhai;

import ai.djl.huggingface.tokenizers.Encoding;
import ai.djl.huggingface.tokenizers.HuggingFaceTokenizer;
import ai.djl.inference.Predictor;
import ai.djl.ndarray.NDArray;
import ai.djl.ndarray.NDList;
import ai.djl.ndarray.NDManager;
import ai.djl.repository.zoo.Criteria;
import ai.djl.repository.zoo.ZooModel;
import ai.djl.training.util.ProgressBar;
import ai.djl.translate.TranslateException;

import java.nio.file.Paths;
import java.util.Arrays;

public class ModelTest {
    public static void main(String[] args) throws Exception {
        // 模型和tokenizer目录路径
//        String modelDir = "/Users/<USER>/IdeaProjects/ai/tabnine-intellij/Tabnine/src/main/resources/models/codebert-base";
        String modelDir = "/Users/<USER>/IdeaProjects/ai/bge-small-zh-v1.5";



        // 1. 加载tokenizer
        HuggingFaceTokenizer tokenizer = HuggingFaceTokenizer.builder()
                .optTokenizerPath(Paths.get(modelDir))
                .build();

        // 2. 创建模型加载条件
        Criteria<NDList, NDList> criteria = Criteria.builder()
                .setTypes(NDList.class, NDList.class)
                .optModelPath(Paths.get(modelDir, "codebert-base.onnx"))
                .optEngine("OnnxRuntime")
                .optProgress(new ProgressBar())
                .build();

        // 3. 加载模型
        try (ZooModel<NDList, NDList> model = criteria.loadModel();
             Predictor<NDList, NDList> predictor = model.newPredictor();
             NDManager manager = NDManager.newBaseManager()) {

            // 4. 准备测试代码
//            String code = "def hello_world():\n    print('Hello, World!')";
            String code = "please chek your code";

            // 5. 对代码进行tokenization
            Encoding encoding = tokenizer.encode(code);
            long[] inputIds = encoding.getIds();
            long[] attentionMask = encoding.getAttentionMask();

            // 6. 转换为NDArray
            NDArray inputIdsArray = manager.create(inputIds).expandDims(0);  // 添加batch维度
            NDArray attentionMaskArray = manager.create(attentionMask).expandDims(0);

            // 7. 创建输入NDList
            NDList input = new NDList(inputIdsArray, attentionMaskArray);

            // 8. 执行预测
            NDList output = predictor.predict(input);

            // 9. 获取最后一层隐藏状态 (batch_size, sequence_length, hidden_size)
            NDArray embeddings = output.get(0);

            // 10. 获取[CLS] token的embedding作为整个代码的表示 (768维)
            NDArray codeVector = embeddings.get(0).get(0);  // [CLS] token通常在序列开头

            // 11. 验证并打印结果
            System.out.println("Vector shape: " + Arrays.toString(codeVector.getShape().getShape()));
            System.out.println("Vector dimension: " + codeVector.getShape().get(0));
            float[] vectorArray = codeVector.toFloatArray();
            System.out.println("First 5 elements of vector: " +
                    Arrays.toString(Arrays.copyOfRange(vectorArray, 0, Math.min(5, vectorArray.length))));

        } catch (TranslateException e) {
            System.err.println("Error during prediction: " + e.getMessage());
            throw e;
        }
    }
}