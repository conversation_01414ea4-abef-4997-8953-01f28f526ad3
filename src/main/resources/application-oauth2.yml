ta404:
  modules:
    security:
      token:
        processor: cas-oauth2
  component:
    security:
      custom:
        #oauth2 相关配置
        oauth:
          #oauth2 模式
          grant-type: authorization_code
          #获取access_token 接口
          token-url: http://172.20.XX.XXX:8003/casserver/oauth2.0/accessToken
          #配置client_id
          clientId: 20210518
          #配置验证
          secret: 123456
          #重定向地址
          redirect-uri: http://localhost:8080/login.html
          #获取用户接口
          user-url: http://172.20.XX.XXX:8003/casserver/oauth2.0/profile
          #获取用户权限接口
          auth-url:
