package com.yinhai.ta404.demo.service.write;

import com.yinhai.ta404.demo.entity.AppPo;
import com.yinhai.ta404.demo.vo.AppVo;

/**
* (CloudApp)表服务接口
*
* <AUTHOR>
* @since 2023-06-16 10:14:08
*/
public interface AppWriteService {
    /**
    * 新增数据
    *
    * @param cloudAppInfoVo 实例对象
    * @return 实例对象
    */
    AppPo addCloudAppInfo(AppVo cloudAppInfoVo);


    /**
     * 更新数据
     *
     * @param cloudAppInfoVo 实例对象
     * @return 更新后的实例对象
     */
    AppPo updateCloudAppInfo(AppVo cloudAppInfoVo);


    /**
     * 删除app
     * @param appId
     */
    void deleteApp(String appId);



}
