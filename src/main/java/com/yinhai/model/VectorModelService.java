package com.yinhai.model;

import ai.djl.Model;
import ai.djl.inference.Predictor;
import ai.djl.repository.zoo.Criteria;
import ai.djl.repository.zoo.ModelZoo;
import ai.djl.repository.zoo.ZooModel;
import ai.djl.training.util.ProgressBar;
import ai.djl.translate.TranslateException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

public class VectorModelService implements AutoCloseable {

    private ZooModel<String, float[]> model;
    private Predictor<String, float[]> predictor;

    public void loadModel(String modelDir) throws Exception {
        // 1. 创建Translator
        BertVectorTranslator translator = new BertVectorTranslator(modelDir);

        // 2. 构建模型加载标准
        Criteria<String, float[]> criteria = Criteria.builder()
            .setTypes(String.class, float[].class)
            .optModelPath(Paths.get(modelDir, "codebert-base.onnx"))
            .optEngine("OnnxRuntime")  // 使用ONNX引擎
            .optTranslator(translator)
            .optProgress(new ProgressBar())
            .build();

        // 3. 加载模型
        this.model = ModelZoo.loadModel(criteria);
        this.predictor = model.newPredictor();
    }

    public float[] getTextVector(String text) throws TranslateException {
        return predictor.predict(text);
    }

    public List<float[]> batchGetVectors(List<String> texts) throws TranslateException {
        return predictor.batchPredict(texts);
    }

    @Override
    public void close() {
        if (predictor != null) {
            predictor.close();
        }
        if (model != null) {
            model.close();
        }
    }
}