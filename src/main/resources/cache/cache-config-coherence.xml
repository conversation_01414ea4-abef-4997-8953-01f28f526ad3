<?xml version="1.0"?>

<cache-config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
   xmlns="http://xmlns.oracle.com/coherence/coherence-cache-config"
   xsi:schemaLocation="http://xmlns.oracle.com/coherence/coherence-cache-config
   coherence-cache-config.xsd">
   <defaults>
      <serializer>java</serializer>
   </defaults>
   <caching-scheme-mapping>
      <cache-mapping>
         <cache-name>appCodeCache</cache-name>
         <scheme-name>distributed</scheme-name>
         <key-type>String</key-type>
         <value-type>String</value-type>
      </cache-mapping>
      <cache-mapping>
         <cache-name>loginCache</cache-name>
         <scheme-name>distributed</scheme-name>
         <key-type>String</key-type>
         <value-type>String</value-type>
      </cache-mapping>
       <cache-mapping>
           <cache-name>childReportCache</cache-name>
           <scheme-name>distributed</scheme-name>
           <key-type>String</key-type>
           <value-type>String</value-type>
       </cache-mapping>
       <cache-mapping>
           <cache-name>menuReportCache</cache-name>
           <scheme-name>distributed</scheme-name>
           <key-type>String</key-type>
           <value-type>String</value-type>
       </cache-mapping>
       <cache-mapping>
           <cache-name>reportCache</cache-name>
           <scheme-name>distributed</scheme-name>
           <key-type>String</key-type>
           <value-type>String</value-type>
       </cache-mapping>
       <cache-mapping>
           <cache-name>limitCache</cache-name>
           <scheme-name>distributed</scheme-name>
           <key-type>String</key-type>
           <value-type>String</value-type>
       </cache-mapping>
       <cache-mapping>
           <cache-name>taOrgCache</cache-name>
           <scheme-name>distributed</scheme-name>
           <key-type>String</key-type>
           <value-type>String</value-type>
       </cache-mapping>
       <cache-mapping>
           <cache-name>taUserOrgCache</cache-name>
           <scheme-name>distributed</scheme-name>
           <key-type>String</key-type>
           <value-type>String</value-type>
       </cache-mapping>
       <cache-mapping>
           <cache-name>progressCache</cache-name>
           <scheme-name>distributed</scheme-name>
           <key-type>String</key-type>
           <value-type>String</value-type>
       </cache-mapping>
       <cache-mapping>
           <cache-name>elasticJobCache</cache-name>
           <scheme-name>distributed</scheme-name>
           <key-type>String</key-type>
           <value-type>String</value-type>
       </cache-mapping>
   </caching-scheme-mapping>
   
   <caching-schemes>
      <distributed-scheme>
         <scheme-name>distributed</scheme-name>
         <service-name>DistributedCache</service-name>
         <backing-map-scheme>
            <local-scheme/>
         </backing-map-scheme>
         <autostart>true</autostart>
      </distributed-scheme>
   </caching-schemes>
</cache-config>