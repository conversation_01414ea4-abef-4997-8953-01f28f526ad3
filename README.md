# TA404 示例项目

这是一个基于TA404框架开发的示例项目，集成了向量模型服务和向量数据库功能。

## 项目概述

该项目是基于TA404框架（版本5.4.3-RELEASE）构建的Spring Boot应用程序，主要展示了如何在TA404框架中整合AI模型功能，特别是向量嵌入和向量数据库操作。

## 主要功能

- 基础的TA404框架功能（用户认证、权限管理等）
- 向量模型服务，支持文本向量化
- Milvus向量数据库集成
- DJL (Deep Java Library) 框架的使用示例
- ONNX模型运行时支持

## 技术栈

- **后端框架**：Spring Boot + TA404框架
- **数据库**：MySQL/PostgreSQL
- **AI框架**：
  - DJL (Deep Java Library) 0.28.0
  - ONNX Runtime
  - Hugging Face Tokenizers
- **向量数据库**：Milvus 2.5.7
- **其他工具**：
  - OpenAI Java SDK
  - Google Guava

## 快速开始

### 环境要求

- JDK 8
- Maven 3.x
- 关系型数据库 (MySQL/PostgreSQL)
- Milvus (可选，用于向量搜索)

### 构建与运行

1. 克隆项目到本地

2. 配置数据库连接
   修改 `src/main/resources/application-datasource.yml` 文件中的数据库连接信息

3. 构建项目
   ```bash
   ./mvnw clean package
