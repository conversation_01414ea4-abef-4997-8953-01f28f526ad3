ta404:
  application:
    name: ta404
    version: 5.4.3-RELEASE
spring:
  profiles:
    active: dev,datasource,websecurity,cache
  application:
    name: ${ta404.application.name}
  main:
    allow-bean-definition-overriding: true
  banner:
    location: banner.txt
  output:
    ansi:
      enabled: always
  servlet:
    multipart:
      max-file-size: 20MB
server:
  port: 8081
  servlet:
    session:
      timeout: 14400s
    context-path: /ta404

