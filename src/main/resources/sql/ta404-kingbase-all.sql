-- 兼容Oracle版
CREATE SEQUENCE HIBERNATE_SEQUENCE
minvalue 100000
maxvalue 1000000000
start with 110000
increment by 1;

CREATE TABLE freebusyjobmg(
    ID VARCHAR(36) NOT NULL,
    JOBNAME VARCHAR(60),
    FREEMOMENT VARCHAR(20),
    BUSYMOMENT VARCHAR(20),
    SERVERIPS VARCHAR(200),
    ZKID VARCHAR(36),
    PRIMARY KEY (ID)
);

COMMENT ON TABLE freebusyjobmg IS '闲忙任务管理';
COMMENT ON COLUMN freebusyjobmg.ID IS '闲忙任务ID';
COMMENT ON COLUMN freebusyjobmg.JOBNAME IS '任务名称';
COMMENT ON COLUMN freebusyjobmg.FREEMOMENT IS '空闲时刻';
COMMENT ON COLUMN freebusyjobmg.BUSYMOMENT IS '繁忙时刻';
COMMENT ON COLUMN freebusyjobmg.SERVERIPS IS '服务器IP地址';
COMMENT ON COLUMN freebusyjobmg.ZKID IS 'zk注册中心ID';

CREATE TABLE taaccessauthority(
    accessid VARCHAR(100) NOT NULL,
    urlid VARCHAR(100) NOT NULL,
    PRIMARY KEY (accessid,urlid)
);

COMMENT ON TABLE taaccessauthority IS '访问系统权限控制表';


CREATE INDEX TAACCESSAUTHORITY_ACCESSID_INDEX ON taaccessauthority(accessid);

CREATE TABLE taaccessconfig(
    accessid VARCHAR(36) NOT NULL,
    pubkey VARCHAR(512) NOT NULL,
    accessName VARCHAR(100) NOT NULL,
    crypto VARCHAR(100) NOT NULL,
    PRIMARY KEY (accessid)
);

COMMENT ON TABLE taaccessconfig IS '访问系统公钥配置表';
COMMENT ON COLUMN taaccessconfig.accessid IS '访问标识';
COMMENT ON COLUMN taaccessconfig.pubkey IS '公钥';
COMMENT ON COLUMN taaccessconfig.accessName IS '访问系统描述';
COMMENT ON COLUMN taaccessconfig.crypto IS '加密算法  sm2/rsa/ecdsa等';


CREATE TABLE taaccessdenylog(
    logid VARCHAR(36) NOT NULL,
    url VARCHAR(200),
    userid VARCHAR(36),
    accesstime DATETIME,
    clientip VARCHAR(200),
    serverip VARCHAR(50),
    clientsystem VARCHAR(50),
    clientbrowser VARCHAR(50),
    accessdenytype VARCHAR(2),
    clientscreensize VARCHAR(50),
    menuid VARCHAR(50),
    hashverity VARCHAR(200),
    PRIMARY KEY (logid)
);

COMMENT ON TABLE taaccessdenylog IS '访问限制日志表';
COMMENT ON COLUMN taaccessdenylog.logid IS '日志ID';
COMMENT ON COLUMN taaccessdenylog.url IS '访问URL';
COMMENT ON COLUMN taaccessdenylog.userid IS '用户ID';
COMMENT ON COLUMN taaccessdenylog.accesstime IS '访问时间';
COMMENT ON COLUMN taaccessdenylog.clientip IS '客户端IP';
COMMENT ON COLUMN taaccessdenylog.serverip IS '服务端IP';
COMMENT ON COLUMN taaccessdenylog.clientsystem IS '客户端系统';
COMMENT ON COLUMN taaccessdenylog.clientbrowser IS '客户端浏览器';
COMMENT ON COLUMN taaccessdenylog.accessdenytype IS '限制类型 01: 未登录(TOKEN 失效) 02：无权限 03：跨域';
COMMENT ON COLUMN taaccessdenylog.clientscreensize IS '客户端分辨率';
COMMENT ON COLUMN taaccessdenylog.menuid IS '菜单id';
COMMENT ON COLUMN taaccessdenylog.hashverity IS 'hash 验证';

CREATE INDEX TAACCESSDENYLOG_TIME_INDEX ON taaccessdenylog(accesstime);

CREATE TABLE taaccesssystem(
    ID VARCHAR(36) NOT NULL,
    SYSCODE VARCHAR(20) NOT NULL,
    SYSNAME VARCHAR(100) NOT NULL,
    SPELL VARCHAR(30),
    PROTOCOL VARCHAR(10),
    DOMAIN VARCHAR(100),
    PORT VARCHAR(5),
    CONTEXTPATH VARCHAR(30),
    PORTALSYSTEM VARCHAR(1),
    MENUSERVICE VARCHAR(200),
    PROVIDER VARCHAR(50),
    EFFECTIVE VARCHAR(1) NOT NULL,
    REGTIME DATETIME,
    MODIFYTIME DATETIME,
    BACKGROUNDADDRESS VARCHAR(100),
    FIELD01 VARCHAR(1000),
    FIELD02 VARCHAR(1000),
    FIELD03 VARCHAR(1000),
    FIELD04 VARCHAR(1000),
    FIELD05 VARCHAR(1000),
    FIELD06 VARCHAR(1000),
    FIELD07 VARCHAR(1000),
    FIELD08 VARCHAR(1000),
    FIELD09 VARCHAR(1000),
    FIELD10 VARCHAR(1000),
    PRIMARY KEY (ID)
);

COMMENT ON TABLE taaccesssystem IS '接入系统表';
COMMENT ON COLUMN taaccesssystem.ID IS '接入系统id';
COMMENT ON COLUMN taaccesssystem.SYSCODE IS '系统标识';
COMMENT ON COLUMN taaccesssystem.SYSNAME IS '系统名称';
COMMENT ON COLUMN taaccesssystem.SPELL IS '拼音简写';
COMMENT ON COLUMN taaccesssystem.PROTOCOL IS '系统协议';
COMMENT ON COLUMN taaccesssystem.DOMAIN IS '域名或ip';
COMMENT ON COLUMN taaccesssystem.PORT IS '端口';
COMMENT ON COLUMN taaccesssystem.CONTEXTPATH IS '上下文';
COMMENT ON COLUMN taaccesssystem.PORTALSYSTEM IS '是否接入门户';
COMMENT ON COLUMN taaccesssystem.MENUSERVICE IS '菜单服务';
COMMENT ON COLUMN taaccesssystem.PROVIDER IS '系统提供方';
COMMENT ON COLUMN taaccesssystem.EFFECTIVE IS '有效标识';
COMMENT ON COLUMN taaccesssystem.REGTIME IS '注册时间';
COMMENT ON COLUMN taaccesssystem.MODIFYTIME IS '修改时间';
COMMENT ON COLUMN taaccesssystem.BACKGROUNDADDRESS IS '后台地址';
COMMENT ON COLUMN taaccesssystem.FIELD01 IS '扩展字段01';
COMMENT ON COLUMN taaccesssystem.FIELD02 IS '扩展字段02';
COMMENT ON COLUMN taaccesssystem.FIELD03 IS '扩展字段03';
COMMENT ON COLUMN taaccesssystem.FIELD04 IS '扩展字段04';
COMMENT ON COLUMN taaccesssystem.FIELD05 IS '扩展字段05';
COMMENT ON COLUMN taaccesssystem.FIELD06 IS '扩展字段06';
COMMENT ON COLUMN taaccesssystem.FIELD07 IS '扩展字段07';
COMMENT ON COLUMN taaccesssystem.FIELD08 IS '扩展字段08';
COMMENT ON COLUMN taaccesssystem.FIELD09 IS '扩展字段09';
COMMENT ON COLUMN taaccesssystem.FIELD10 IS '扩展字段10';

CREATE UNIQUE INDEX TAACCESSSYSTEM_SYSCODE_INDEX ON taaccesssystem(SYSCODE);
CREATE UNIQUE INDEX TAACCESSSYSTEM_SYSNAME_INDEX ON taaccesssystem(SYSNAME);
CREATE INDEX TAACCESSSYSTEM_SPELL_INDEX ON taaccesssystem(SPELL);

CREATE TABLE taagent(
    userid VARCHAR(100) NOT NULL,
    agentid VARCHAR(100) NOT NULL,
    roleid VARCHAR(100) NOT NULL,
    effectivetime DATETIME,
    PRIMARY KEY (userid,agentid,roleid)
);

COMMENT ON TABLE taagent IS '个人权限代理表';

CREATE INDEX TAAGENT_AGENTID_INDEX ON taagent(agentid);
CREATE INDEX TAAGENT_ROLEID_INDEX ON taagent(roleid);

CREATE TABLE taapply(
    APPLYID VARCHAR(36),
    APPLYTYPE VARCHAR(255),
    APPLYCONTENT TEXT,
    APPLYMESSAGE VARCHAR(255),
    APPLYUSER VARCHAR(255),
    APPLYTIME DATETIME,
    STATUS VARCHAR(1),
    MESSAGE VARCHAR(255),
    AUDITUSER VARCHAR(255),
    AUDITTIME DATETIME,
    ASSOCIATEDENTITYID VARCHAR(255),
    APPLYAUTHKEY VARCHAR(255),
    APPLYAUTHTYPE VARCHAR(255),
    PRIMARY KEY (APPLYID)
);

COMMENT ON TABLE taapply IS '申请审核表';
COMMENT ON COLUMN taapply.APPLYID IS '申请编号';
COMMENT ON COLUMN taapply.APPLYTYPE IS '申请类型;账号申请等';
COMMENT ON COLUMN taapply.APPLYCONTENT IS '申请内容;JSON';
COMMENT ON COLUMN taapply.APPLYMESSAGE IS '申请说明';
COMMENT ON COLUMN taapply.APPLYUSER IS '申请人信息;账号/身份证';
COMMENT ON COLUMN taapply.APPLYTIME IS '申请时间';
COMMENT ON COLUMN taapply.STATUS IS '审核状态;待提交1、待审核2、审核通过3、审核不通过4、作废5';
COMMENT ON COLUMN taapply.MESSAGE IS '审核说明;审核应是允许扩展的，如可引入流程引擎实现多级审核';
COMMENT ON COLUMN taapply.AUDITUSER IS '审核人信息';
COMMENT ON COLUMN taapply.AUDITTIME IS '审核时间';
COMMENT ON COLUMN taapply.ASSOCIATEDENTITYID IS '关联实体ID;账号ID等';
COMMENT ON COLUMN taapply.APPLYAUTHKEY IS '申请权限标识;比如：组织ID';
COMMENT ON COLUMN taapply.APPLYAUTHTYPE IS '申请权限类型;比如：组织';

CREATE INDEX TAAPPLY_APPLYAUTHKEY_INDEX ON taapply(applyauthkey);

CREATE TABLE taarea(
    AREAID VARCHAR(36) NOT NULL,
    PARENTID VARCHAR(36) NOT NULL,
    AREANAME VARCHAR(100) NOT NULL,
    AREACODE VARCHAR(20) NOT NULL,
    IDPATH VARCHAR(1024) NOT NULL,
    NAMEPATH TEXT NOT NULL,
    arealevel NUMERIC(2),
    SPELL VARCHAR(100),
    CREATEUSER VARCHAR(36) NOT NULL,
    areaorder NUMERIC(20),
    EFFECTIVE VARCHAR(1) NOT NULL,
    CREATETIME DATETIME NOT NULL,
    MODIFYTIME DATETIME NOT NULL,
    DESTORY VARCHAR(1),
    PRIMARY KEY (AREAID)
);

COMMENT ON TABLE taarea IS '行政区划表';
COMMENT ON COLUMN taarea.AREAID IS '行政区划ID';
COMMENT ON COLUMN taarea.PARENTID IS '父级ID';
COMMENT ON COLUMN taarea.AREANAME IS '行政区划名称';
COMMENT ON COLUMN taarea.AREACODE IS '行政区划编码';
COMMENT ON COLUMN taarea.IDPATH IS '行政区划ID全路径';
COMMENT ON COLUMN taarea.NAMEPATH IS '行政区划名称路径';
COMMENT ON COLUMN taarea.arealevel IS '行政区划层级';
COMMENT ON COLUMN taarea.SPELL IS '拼音简写';
COMMENT ON COLUMN taarea.CREATEUSER IS '创建人';
COMMENT ON COLUMN taarea.areaorder IS '排序号';
COMMENT ON COLUMN taarea.EFFECTIVE IS '有效标识';
COMMENT ON COLUMN taarea.CREATETIME IS '创建时间';
COMMENT ON COLUMN taarea.MODIFYTIME IS '更新时间';
COMMENT ON COLUMN taarea.DESTORY IS '销毁标识';

CREATE INDEX TAAREA_PARENTID_INDEX ON taarea(PARENTID);
CREATE INDEX TAAREA_AREANAME_INDEX ON taarea(AREANAME);
CREATE INDEX TAAREA_AREACODE_INDEX ON taarea(AREACODE);
CREATE INDEX TAAREA_SPELL_INDEX ON taarea(SPELL);
CREATE INDEX TAAREA_IDPATH_INDEX ON taarea(IDPATH);

CREATE TABLE taaudit(
    auditid VARCHAR(36) NOT NULL,
    userid VARCHAR(36) NOT NULL,
    auditcontent VARCHAR(1024),
    auditdate DATETIME,
    excelname VARCHAR(64),
    aliasexcelname VARCHAR(64),
    audittype VARCHAR(1),
    storetype VARCHAR(1),
    hashverity VARCHAR(200),
    PRIMARY KEY (auditid)
);

COMMENT ON TABLE taaudit IS '审计记录表';
COMMENT ON COLUMN taaudit.auditid IS '审计ID';
COMMENT ON COLUMN taaudit.userid IS '用户ID';
COMMENT ON COLUMN taaudit.auditcontent IS '审计内容';
COMMENT ON COLUMN taaudit.auditdate IS '审计时间';
COMMENT ON COLUMN taaudit.excelname IS '文件名';
COMMENT ON COLUMN taaudit.aliasexcelname IS '文件别名';
COMMENT ON COLUMN taaudit.audittype IS '1：登录日志 2：在线日志 3：异常日志 4：操作日志 5：审核日志';
COMMENT ON COLUMN taaudit.storetype IS '存储类型(1:服务器 2:本地 3:文件服务器）';
COMMENT ON COLUMN taaudit.hashverity IS 'hash 验证';

CREATE INDEX TAAUDIT_USERID_INDEX ON taaudit(userid);
CREATE INDEX TAAUDIT_TIME_INDEX ON taaudit(auditdate);

CREATE TABLE taavatar(
    USERID VARCHAR(36) NOT NULL,
    AVATAR BLOB,
    UPDATETIME DATETIME NOT NULL,
    PRIMARY KEY (USERID,UPDATETIME)
);

COMMENT ON TABLE taavatar IS '用户头像表';
COMMENT ON COLUMN taavatar.USERID IS '用户id';
COMMENT ON COLUMN taavatar.AVATAR IS '头像base64';
COMMENT ON COLUMN taavatar.UPDATETIME IS '更新时间';


CREATE TABLE tacustomorg(
    CUSTOMORGID VARCHAR(36) NOT NULL,
    PARENTID VARCHAR(36) NOT NULL,
    CUSTOMCODE VARCHAR(255),
    CUSTOMORGTYPENAMEID VARCHAR(36) NOT NULL,
    EFFECTIVE VARCHAR(1) NOT NULL,
    orderno NUMERIC(10),
    CUSTOMORGNAME VARCHAR(255),
    CUSTOMORGPATHID VARCHAR(1024),
    CUSTOMORGPATH VARCHAR(1024),
    CREATEUSER VARCHAR(36),
    CREATETIME DATETIME NOT NULL,
    UPDATETIME DATETIME NOT NULL,
    DESTORY VARCHAR(1) NOT NULL,
    SPELL VARCHAR(255),
    ORGMANAGER VARCHAR(36),
    ORGCODE VARCHAR(18),
    CONTACTS VARCHAR(36),
    ADDRESS VARCHAR(450),
    TEL VARCHAR(20),
    FIELD01 VARCHAR(1000),
    FIELD02 VARCHAR(1000),
    FIELD03 VARCHAR(1000),
    FIELD04 VARCHAR(1000),
    FIELD05 VARCHAR(1000),
    FIELD06 VARCHAR(1000),
    FIELD07 VARCHAR(1000),
    FIELD08 VARCHAR(1000),
    FIELD09 VARCHAR(1000),
    FIELD10 VARCHAR(1000),
    ORGID VARCHAR(36),
    PRIMARY KEY (CUSTOMORGID)
);

COMMENT ON TABLE tacustomorg IS '自定义组织表';
COMMENT ON COLUMN tacustomorg.CUSTOMORGID IS '自定义组织ID';
COMMENT ON COLUMN tacustomorg.PARENTID IS '当前组织父级ID';
COMMENT ON COLUMN tacustomorg.CUSTOMCODE IS '自定义编码';
COMMENT ON COLUMN tacustomorg.CUSTOMORGTYPENAMEID IS '自定义组织类型ID';
COMMENT ON COLUMN tacustomorg.EFFECTIVE IS '有效标识';
COMMENT ON COLUMN tacustomorg.orderno IS '排序号';
COMMENT ON COLUMN tacustomorg.CUSTOMORGNAME IS '自定义组织名称';
COMMENT ON COLUMN tacustomorg.CUSTOMORGPATHID IS '自定义组织ID路径';
COMMENT ON COLUMN tacustomorg.CUSTOMORGPATH IS '自定义组织名称路径';
COMMENT ON COLUMN tacustomorg.CREATEUSER IS '创建人';
COMMENT ON COLUMN tacustomorg.CREATETIME IS '创建时间';
COMMENT ON COLUMN tacustomorg.UPDATETIME IS '更新时间';
COMMENT ON COLUMN tacustomorg.DESTORY IS '销毁标识';
COMMENT ON COLUMN tacustomorg.SPELL IS '拼音简写';
COMMENT ON COLUMN tacustomorg.ORGMANAGER IS '组织负责人';
COMMENT ON COLUMN tacustomorg.ORGCODE IS '组织代码';
COMMENT ON COLUMN tacustomorg.CONTACTS IS '单位联系人';
COMMENT ON COLUMN tacustomorg.ADDRESS IS '联系地址';
COMMENT ON COLUMN tacustomorg.TEL IS '联系电话';
COMMENT ON COLUMN tacustomorg.FIELD01 IS '扩展字段01';
COMMENT ON COLUMN tacustomorg.FIELD02 IS '扩展字段02';
COMMENT ON COLUMN tacustomorg.FIELD03 IS '扩展字段03';
COMMENT ON COLUMN tacustomorg.FIELD04 IS '扩展字段04';
COMMENT ON COLUMN tacustomorg.FIELD05 IS '扩展字段05';
COMMENT ON COLUMN tacustomorg.FIELD06 IS '扩展字段06';
COMMENT ON COLUMN tacustomorg.FIELD07 IS '扩展字段07';
COMMENT ON COLUMN tacustomorg.FIELD08 IS '扩展字段08';
COMMENT ON COLUMN tacustomorg.FIELD09 IS '扩展字段09';
COMMENT ON COLUMN tacustomorg.FIELD10 IS '扩展字段10';
COMMENT ON COLUMN tacustomorg.ORGID IS '组织机构树ID';

CREATE INDEX TACUSTOMORG_PARENTID_INDEX ON tacustomorg(PARENTID);
CREATE INDEX TACUSTOMORG_CUSTOMCODE_INDEX ON tacustomorg(CUSTOMCODE);
CREATE INDEX TACUSTOMORG_CUSTOMORGTYPENAMEID_INDEX ON tacustomorg(CUSTOMORGTYPENAMEID);
CREATE INDEX TACUSTOMORG_CUSTOMORGNAME_INDEX ON tacustomorg(CUSTOMORGNAME);
CREATE INDEX TACUSTOMORG_CUSTOMORGPATHID_INDEX ON tacustomorg(CUSTOMORGPATHID);
CREATE INDEX TACUSTOMORG_SPELL_INDEX ON tacustomorg(SPELL);

CREATE TABLE tacustomorgtypename(
    CUSTOMORGTYPENAMEID VARCHAR(36),
    CUSTOMORGTYPENAME VARCHAR(36) NOT NULL,
    EFFECTIVE VARCHAR(1),
    CREATEUSER VARCHAR(36),
    CREATETIME DATETIME,
    UPDATETIME DATETIME,
    DESTORY VARCHAR(1),
    CUSTOMORGTYPENAMECODE VARCHAR(255),
    CUSTOMORGTYPENAMEDESC VARCHAR(255),
    PRIMARY KEY (CUSTOMORGTYPENAMEID)
);

COMMENT ON TABLE tacustomorgtypename IS '自定义组织类型表';
COMMENT ON COLUMN tacustomorgtypename.CUSTOMORGTYPENAMEID IS '自定义组织类型ID';
COMMENT ON COLUMN tacustomorgtypename.CUSTOMORGTYPENAME IS '自定义组织类型名称';
COMMENT ON COLUMN tacustomorgtypename.EFFECTIVE IS '是否有效';
COMMENT ON COLUMN tacustomorgtypename.CREATEUSER IS '创建人';
COMMENT ON COLUMN tacustomorgtypename.CREATETIME IS '创建时间';
COMMENT ON COLUMN tacustomorgtypename.UPDATETIME IS '更新时间';
COMMENT ON COLUMN tacustomorgtypename.DESTORY IS '销毁状态';
COMMENT ON COLUMN tacustomorgtypename.CUSTOMORGTYPENAMECODE IS '自定义组织类型编码';
COMMENT ON COLUMN tacustomorgtypename.CUSTOMORGTYPENAMEDESC IS '自定义组织类型描述';

CREATE INDEX TACUSTOMORGTYPENAME_CUSTOMORGTYPENAME_INDEX ON tacustomorgtypename(CUSTOMORGTYPENAME);

CREATE TABLE tacustomorguser(
    CUSTOMORGID VARCHAR(36) NOT NULL,
    USERID VARCHAR(36) NOT NULL,
    PRIMARY KEY (CUSTOMORGID,USERID)
);

COMMENT ON TABLE tacustomorguser IS '自定义组织用户关系表';
COMMENT ON COLUMN tacustomorguser.CUSTOMORGID IS '自定义组织ID';
COMMENT ON COLUMN tacustomorguser.USERID IS '用户Id';

CREATE INDEX TACUSTOMORGUSER_USERID_INDEX ON tacustomorguser(USERID);

CREATE TABLE tacustomresource(
    CUSTOMRESOURCEID VARCHAR(36) NOT NULL,
    RESOURCENAME VARCHAR(450) NOT NULL,
    PARENTID VARCHAR(36) NOT NULL,
    CODE VARCHAR(100),
    RESOURCECONTENT VARCHAR(255),
    RESOURCECATEGORY VARCHAR(32),
    EFFECTIVE VARCHAR(1) NOT NULL,
    ADDTIME DATETIME,
    MODIFYTIME DATETIME,
    SYSTEM VARCHAR(36),
    PRIMARY KEY (CUSTOMRESOURCEID)
);

COMMENT ON TABLE tacustomresource IS '自定义资源表';
COMMENT ON COLUMN tacustomresource.CUSTOMRESOURCEID IS '自定义资源Id';
COMMENT ON COLUMN tacustomresource.RESOURCENAME IS '资源名称';
COMMENT ON COLUMN tacustomresource.PARENTID IS '父Id';
COMMENT ON COLUMN tacustomresource.CODE IS '资源编码';
COMMENT ON COLUMN tacustomresource.RESOURCECONTENT IS '自定义资源内容';
COMMENT ON COLUMN tacustomresource.RESOURCECATEGORY IS '资源所属类别';
COMMENT ON COLUMN tacustomresource.EFFECTIVE IS '有效状态';
COMMENT ON COLUMN tacustomresource.ADDTIME IS '创建事件';
COMMENT ON COLUMN tacustomresource.MODIFYTIME IS '修改时间';
COMMENT ON COLUMN tacustomresource.SYSTEM IS '资源所属系统';

CREATE INDEX TACUSTOMRESOURCE_RESOURCENAME_INDEX ON tacustomresource(RESOURCENAME);
CREATE INDEX TACUSTOMRESOURCE_PARENTID_INDEX ON tacustomresource(PARENTID);
CREATE INDEX TACUSTOMRESOURCE_SYSTEM_INDEX ON tacustomresource(SYSTEM);

CREATE TABLE tadict(
    NAME VARCHAR(50) NOT NULL,
    TYPE VARCHAR(50) NOT NULL,
    LABEL VARCHAR(50) NOT NULL,
    VALUE VARCHAR(50) NOT NULL,
    PARENTVALUE VARCHAR(50),
    sort NUMERIC(4),
    AUTHORITY VARCHAR(20) NOT NULL,
    cssclass VARCHAR(128),
    CSSSTYLE VARCHAR(128),
    REMARKS VARCHAR(256),
    CREATETIME DATETIME,
    CREATEUSER VARCHAR(36) NOT NULL,
    VERSION VARCHAR(10) NOT NULL,
    STATUS VARCHAR(2) NOT NULL,
    SYSTEM VARCHAR(2) NOT NULL,
    NEWTYPE VARCHAR(2) NOT NULL,
    FIELD01 VARCHAR(10),
    FIELD02 VARCHAR(10),
    FIELD03 VARCHAR(10),
    FIELD04 VARCHAR(10),
    FIELD05 VARCHAR(10),
    PRIMARY KEY (TYPE,VALUE)
);

COMMENT ON TABLE tadict IS '数据字典表';
COMMENT ON COLUMN tadict.NAME IS '字典名称';
COMMENT ON COLUMN tadict.TYPE IS '字典类型';
COMMENT ON COLUMN tadict.LABEL IS '字典标签';
COMMENT ON COLUMN tadict.VALUE IS '字典键值';
COMMENT ON COLUMN tadict.PARENTVALUE IS '父级字典值';
COMMENT ON COLUMN tadict.sort IS '顺序';
COMMENT ON COLUMN tadict.AUTHORITY IS '权限标识';
COMMENT ON COLUMN tadict.cssclass IS 'CSSCLASS';
COMMENT ON COLUMN tadict.CSSSTYLE IS 'css样式';
COMMENT ON COLUMN tadict.REMARKS IS '备注';
COMMENT ON COLUMN tadict.CREATETIME IS '创建时间';
COMMENT ON COLUMN tadict.CREATEUSER IS '创建人';
COMMENT ON COLUMN tadict.VERSION IS '版本';
COMMENT ON COLUMN tadict.STATUS IS '字典状态';
COMMENT ON COLUMN tadict.SYSTEM IS '系统字典';
COMMENT ON COLUMN tadict.NEWTYPE IS '新增类型标识';
COMMENT ON COLUMN tadict.FIELD01 IS '备用字段1';
COMMENT ON COLUMN tadict.FIELD02 IS '备用字段2';
COMMENT ON COLUMN tadict.FIELD03 IS '备用字段3';
COMMENT ON COLUMN tadict.FIELD04 IS '备用字段4';
COMMENT ON COLUMN tadict.FIELD05 IS '备用字段5';

CREATE INDEX TADICT_NAME_INDEX ON tadict(NAME);
CREATE INDEX TADICT_TYPE_LABEL_VALUE_INDEX ON tadict(TYPE,LABEL,VALUE);

CREATE TABLE tadynamicrest(
    id VARCHAR(36),
    sql VARCHAR(2048),
    dsname VARCHAR(128),
    restid VARCHAR(255),
    restname VARCHAR(128),
    effective VARCHAR(1),
    createtime DATETIME,
    createuser VARCHAR(36),
    PRIMARY KEY (id)
);

COMMENT ON TABLE tadynamicrest IS '动态服务表（弃用）';
COMMENT ON COLUMN tadynamicrest.id IS '唯一标识';
COMMENT ON COLUMN tadynamicrest.sql IS 'sql';
COMMENT ON COLUMN tadynamicrest.dsname IS '数据源名称';
COMMENT ON COLUMN tadynamicrest.restid IS '服务ID';
COMMENT ON COLUMN tadynamicrest.restname IS '有效标识';
COMMENT ON COLUMN tadynamicrest.effective IS '服务名称';
COMMENT ON COLUMN tadynamicrest.createtime IS '创建时间';
COMMENT ON COLUMN tadynamicrest.createuser IS '创建账户';


CREATE TABLE taexamine(
    id VARCHAR(36) NOT NULL,
    opuserid VARCHAR(50),
    checkstatus VARCHAR(1) NOT NULL,
    checkmsg VARCHAR(200),
    createtime DATETIME,
    updatetime DATETIME,
    opname VARCHAR(50),
    opusername VARCHAR(50),
    checkerid VARCHAR(36),
    checkername VARCHAR(36),
    checkeropinion VARCHAR(200),
    opobjecttype VARCHAR(36),
    PRIMARY KEY (id)
);

COMMENT ON TABLE taexamine IS '审核表';
COMMENT ON COLUMN taexamine.id IS '主键id';
COMMENT ON COLUMN taexamine.opuserid IS '操作人id';
COMMENT ON COLUMN taexamine.checkstatus IS '是否审核（0：未审核，1：已审核, 2: 拒审核)';
COMMENT ON COLUMN taexamine.checkmsg IS '审核信息';
COMMENT ON COLUMN taexamine.createtime IS '创建时间';
COMMENT ON COLUMN taexamine.updatetime IS '更新时间';
COMMENT ON COLUMN taexamine.opname IS '操作类型';
COMMENT ON COLUMN taexamine.opusername IS '操作人姓名';
COMMENT ON COLUMN taexamine.checkerid IS '审核人';
COMMENT ON COLUMN taexamine.checkername IS '审核人姓名';
COMMENT ON COLUMN taexamine.checkeropinion IS '审核意见';
COMMENT ON COLUMN taexamine.opobjecttype IS '操作对象类型';


CREATE TABLE taexaminechild(
    id VARCHAR(36) NOT NULL,
    examineid VARCHAR(36),
    beforedata BLOB,
    afterdata BLOB,
    dataclass VARCHAR(200),
    classname VARCHAR(200),
    methodname VARCHAR(200),
    param BLOB,
    PRIMARY KEY (id)
);

COMMENT ON TABLE taexaminechild IS '审核子表';
COMMENT ON COLUMN taexaminechild.id IS 'id';
COMMENT ON COLUMN taexaminechild.examineid IS '主表id';
COMMENT ON COLUMN taexaminechild.beforedata IS '操作前数据（序列化存储）';
COMMENT ON COLUMN taexaminechild.afterdata IS '操作后数据（序列化存储）';
COMMENT ON COLUMN taexaminechild.dataclass IS '数据模板全限定类名(预留)';
COMMENT ON COLUMN taexaminechild.classname IS '全限定类名';
COMMENT ON COLUMN taexaminechild.methodname IS '方法名';
COMMENT ON COLUMN taexaminechild.param IS '方法参数（序列化存储）';

CREATE UNIQUE INDEX TAEXAMINECHILD_EXAMINEID_INDEX ON taexaminechild(examineid);

CREATE TABLE taexaminelocked(
    id VARCHAR(36) NOT NULL,
    examineid VARCHAR(36),
    lockedtype VARCHAR(1),
    lockedkey VARCHAR(100),
    createtime DATETIME,
    opobjecttype VARCHAR(36),
    lockedmode VARCHAR(1),
    PRIMARY KEY (id)
);

COMMENT ON TABLE taexaminelocked IS '审核锁定表';
COMMENT ON COLUMN taexaminelocked.id IS '主键id';
COMMENT ON COLUMN taexaminelocked.examineid IS '主表id';
COMMENT ON COLUMN taexaminelocked.lockedtype IS '0:主键锁定 1：自定义锁定';
COMMENT ON COLUMN taexaminelocked.lockedkey IS '被锁定key';
COMMENT ON COLUMN taexaminelocked.createtime IS '创建时间';
COMMENT ON COLUMN taexaminelocked.opobjecttype IS '操作类型';
COMMENT ON COLUMN taexaminelocked.lockedmode IS '锁定方式';


CREATE TABLE taextendsetting(
    FIELDID VARCHAR(24) NOT NULL,
    TYPE VARCHAR(2) NOT NULL,
    EFFECTIVE VARCHAR(1) NOT NULL,
    HIDE VARCHAR(1),
    DISPLAYTEXT VARCHAR(150),
    TITEXT VARCHAR(1500),
    orderno NUMERIC(10),
    REQUIRED VARCHAR(1),
    UNCHANGEABLE VARCHAR(1),
    FORMTYPE VARCHAR(20),
    contentsize NUMERIC(16),
    VALIDREG VARCHAR(1000),
    CONNECTAA10 VARCHAR(30),
    PROTECTPRIVACY VARCHAR(1),
    PROTECTPRIVACYRULE VARCHAR(20),
    FORMID VARCHAR(24),
    MORE VARCHAR(1),
    PRIMARY KEY (FIELDID,TYPE)
);

COMMENT ON TABLE taextendsetting IS '管理字段配置表';
COMMENT ON COLUMN taextendsetting.FIELDID IS '字段名称';
COMMENT ON COLUMN taextendsetting.TYPE IS '账户类型';
COMMENT ON COLUMN taextendsetting.EFFECTIVE IS '是否有效';
COMMENT ON COLUMN taextendsetting.HIDE IS '是否隐藏';
COMMENT ON COLUMN taextendsetting.DISPLAYTEXT IS '标题文字';
COMMENT ON COLUMN taextendsetting.TITEXT IS '信息提示文字';
COMMENT ON COLUMN taextendsetting.orderno IS '显示顺序';
COMMENT ON COLUMN taextendsetting.REQUIRED IS '是否必填';
COMMENT ON COLUMN taextendsetting.UNCHANGEABLE IS '不可编辑';
COMMENT ON COLUMN taextendsetting.FORMTYPE IS '表单元素类型';
COMMENT ON COLUMN taextendsetting.contentsize IS '内容长度';
COMMENT ON COLUMN taextendsetting.VALIDREG IS '验证表达式';
COMMENT ON COLUMN taextendsetting.CONNECTAA10 IS '关联码表';
COMMENT ON COLUMN taextendsetting.PROTECTPRIVACY IS '隐私保护';
COMMENT ON COLUMN taextendsetting.PROTECTPRIVACYRULE IS '隐私保护规则';
COMMENT ON COLUMN taextendsetting.FORMID IS '表单标识';
COMMENT ON COLUMN taextendsetting.MORE IS '是否展示在更多菜单项';


CREATE TABLE tajobdatasourcemg(
    DATASOURCEID VARCHAR(36) NOT NULL,
    DATASOURCENAME VARCHAR(60),
    DRIVER VARCHAR(200),
    URL VARCHAR(200),
    USERNAME VARCHAR(450),
    PASSWORD VARCHAR(100),
    PRIMARY KEY (DATASOURCEID)
);

COMMENT ON TABLE tajobdatasourcemg IS '作业历史数据源管理';
COMMENT ON COLUMN tajobdatasourcemg.DATASOURCEID IS '数据源ID';
COMMENT ON COLUMN tajobdatasourcemg.DATASOURCENAME IS '数据源名称';
COMMENT ON COLUMN tajobdatasourcemg.DRIVER IS '数据源驱动';
COMMENT ON COLUMN tajobdatasourcemg.URL IS '数据源URL';
COMMENT ON COLUMN tajobdatasourcemg.USERNAME IS '数据源用户';
COMMENT ON COLUMN tajobdatasourcemg.PASSWORD IS '数据源密码';


CREATE TABLE taloginfaillog(
    logid VARCHAR(36) NOT NULL,
    loginid VARCHAR(36) NOT NULL,
    failpassword VARCHAR(50),
    failreason VARCHAR(200),
    logintime DATETIME,
    clientip VARCHAR(200),
    serverip VARCHAR(200),
    clientsystem VARCHAR(50),
    clientbrowser VARCHAR(50),
    clientscreensize VARCHAR(50),
    hashverity VARCHAR(200),
    syspath VARCHAR(50),
    PRIMARY KEY (logid)
);

COMMENT ON TABLE taloginfaillog IS '登录失败日志表';
COMMENT ON COLUMN taloginfaillog.logid IS '日志ID';
COMMENT ON COLUMN taloginfaillog.loginid IS '登录id';
COMMENT ON COLUMN taloginfaillog.failpassword IS '密码';
COMMENT ON COLUMN taloginfaillog.failreason IS '登录失败原因';
COMMENT ON COLUMN taloginfaillog.logintime IS '登录时间';
COMMENT ON COLUMN taloginfaillog.clientip IS '客户端IP';
COMMENT ON COLUMN taloginfaillog.serverip IS '服务端IP';
COMMENT ON COLUMN taloginfaillog.clientsystem IS '客户端系统';
COMMENT ON COLUMN taloginfaillog.clientbrowser IS '客户端浏览器';
COMMENT ON COLUMN taloginfaillog.clientscreensize IS '客户端分辨率';
COMMENT ON COLUMN taloginfaillog.hashverity IS 'hash验证';
COMMENT ON COLUMN taloginfaillog.syspath IS '系统路径';

CREATE INDEX TALOGINFAILLOG_LOGINID_INDEX ON taloginfaillog(loginid);
CREATE INDEX TALOGINFAILLOG_LOGINTIME_INDEX ON taloginfaillog(logintime);

CREATE TABLE taloginhistorylog(
    LOGID VARCHAR(36) NOT NULL,
    USERID VARCHAR(36) NOT NULL,
    LOGINTIME DATETIME NOT NULL,
    LOGOUTTIME DATETIME NOT NULL,
    CLIENTIP VARCHAR(200) NOT NULL,
    SESSIONID VARCHAR(200) NOT NULL,
    SERVERIP VARCHAR(200),
    SYSPATH VARCHAR(50),
    CLIENTSYSTEM VARCHAR(50),
    CLIENTBROWSER VARCHAR(50),
    CLIENTSCREENSIZE VARCHAR(50),
    HASHVERITY VARCHAR(200),
    PRIMARY KEY (LOGID)
);

COMMENT ON TABLE taloginhistorylog IS '用户登录日志表';
COMMENT ON COLUMN taloginhistorylog.LOGID IS '日志ID';
COMMENT ON COLUMN taloginhistorylog.USERID IS '用户ID';
COMMENT ON COLUMN taloginhistorylog.LOGINTIME IS '登录时间';
COMMENT ON COLUMN taloginhistorylog.LOGOUTTIME IS '退出时间';
COMMENT ON COLUMN taloginhistorylog.CLIENTIP IS '客户端IP';
COMMENT ON COLUMN taloginhistorylog.SESSIONID IS '登录时SessionID';
COMMENT ON COLUMN taloginhistorylog.SERVERIP IS '服务端IP';
COMMENT ON COLUMN taloginhistorylog.SYSPATH IS '系统路径';
COMMENT ON COLUMN taloginhistorylog.CLIENTSYSTEM IS '客户端系统版本';
COMMENT ON COLUMN taloginhistorylog.CLIENTBROWSER IS '客户端浏览器版本';
COMMENT ON COLUMN taloginhistorylog.CLIENTSCREENSIZE IS '客户端分辨率';
COMMENT ON COLUMN taloginhistorylog.HASHVERITY IS 'hash 校验';

CREATE INDEX TALOGINHISTORYLOG_USERID_INDEX ON taloginhistorylog(USERID);
CREATE INDEX TALOGINHISTORYLOG_TIME_INDEX ON taloginhistorylog(LOGINTIME);

CREATE TABLE taloginstatlog(
    STATDATE VARCHAR(20) NOT NULL,
    POINTINTIME VARCHAR(20) NOT NULL,
    loginnum NUMERIC(15),
    HASHVERITY VARCHAR(200),
    PRIMARY KEY (STATDATE,POINTINTIME)
);

COMMENT ON TABLE taloginstatlog IS '登录统计日志表';
COMMENT ON COLUMN taloginstatlog.STATDATE IS '统计时间（YYYY-MM-DD）';
COMMENT ON COLUMN taloginstatlog.POINTINTIME IS '统计时点（HH:MM）';
COMMENT ON COLUMN taloginstatlog.loginnum IS '已经登录并注销的用户数';
COMMENT ON COLUMN taloginstatlog.HASHVERITY IS 'hash 校验';


CREATE TABLE tamsgchat(
    CHATID VARCHAR(36) NOT NULL,
    OWNER VARCHAR(36) NOT NULL,
    TWOUSERID VARCHAR(36) NOT NULL,
    TWONAME VARCHAR(100) NOT NULL,
    PRIMARY KEY (CHATID,OWNER)
);

COMMENT ON TABLE tamsgchat IS '私信对话表';
COMMENT ON COLUMN tamsgchat.CHATID IS '对话ID';
COMMENT ON COLUMN tamsgchat.OWNER IS '拥有者';
COMMENT ON COLUMN tamsgchat.TWOUSERID IS '参与者';
COMMENT ON COLUMN tamsgchat.TWONAME IS '参与者name';

CREATE INDEX TAMSGCHAT_OWNER_INDEX ON tamsgchat(OWNER);

CREATE TABLE tamsgletter(
    MID VARCHAR(36) NOT NULL,
    CHATID VARCHAR(36),
    SENDERID VARCHAR(36),
    SENDERNAME VARCHAR(100),
    SENDDATE DATETIME,
    RECEIVERID VARCHAR(36),
    CONTENT VARCHAR(450),
    TYPE VARCHAR(2),
    READSIGN VARCHAR(1),
    PRIMARY KEY (MID)
);

COMMENT ON TABLE tamsgletter IS '私信记录表';
COMMENT ON COLUMN tamsgletter.MID IS '私信ID';
COMMENT ON COLUMN tamsgletter.CHATID IS '对话ID';
COMMENT ON COLUMN tamsgletter.SENDERID IS '发送者';
COMMENT ON COLUMN tamsgletter.SENDERNAME IS '发送者name';
COMMENT ON COLUMN tamsgletter.SENDDATE IS '发送时间';
COMMENT ON COLUMN tamsgletter.RECEIVERID IS '接收者';
COMMENT ON COLUMN tamsgletter.CONTENT IS '内容';
COMMENT ON COLUMN tamsgletter.TYPE IS '类型';
COMMENT ON COLUMN tamsgletter.READSIGN IS '已读标志';

CREATE INDEX TAMSGLETTER_CHATID_INDEX ON tamsgletter(CHATID);

CREATE TABLE tamsgnotice(
    MID VARCHAR(36) NOT NULL,
    SENDERID VARCHAR(36) NOT NULL,
    SENDERNAME VARCHAR(450) NOT NULL,
    SENDDATE DATETIME NOT NULL,
    TITLE VARCHAR(200) NOT NULL,
    CONTENT TEXT NOT NULL,
    TYPE VARCHAR(10) NOT NULL,
    MENUUURL VARCHAR(200),
    OTHERDATA VARCHAR(500),
    PRIMARY KEY (MID)
);

COMMENT ON TABLE tamsgnotice IS '通知表';
COMMENT ON COLUMN tamsgnotice.MID IS '通知ID';
COMMENT ON COLUMN tamsgnotice.SENDERID IS '发送人ID';
COMMENT ON COLUMN tamsgnotice.SENDERNAME IS '发送人name';
COMMENT ON COLUMN tamsgnotice.SENDDATE IS '发送时间';
COMMENT ON COLUMN tamsgnotice.TITLE IS '标题';
COMMENT ON COLUMN tamsgnotice.CONTENT IS '内容';
COMMENT ON COLUMN tamsgnotice.TYPE IS '类型';
COMMENT ON COLUMN tamsgnotice.MENUUURL IS '业务菜单功能路径';
COMMENT ON COLUMN tamsgnotice.OTHERDATA IS '其它';

CREATE INDEX TAMSGNOTICE_SENDERID_INDEX ON tamsgnotice(SENDERID);

CREATE TABLE tamsgnoticeannex(
    ANNEXID VARCHAR(36) NOT NULL,
    ANNEXNAME VARCHAR(100),
    ANNEX BLOB,
    MID VARCHAR(36),
    PRIMARY KEY (ANNEXID)
);

COMMENT ON TABLE tamsgnoticeannex IS '通知附件表';
COMMENT ON COLUMN tamsgnoticeannex.ANNEXID IS '附件ID';
COMMENT ON COLUMN tamsgnoticeannex.ANNEXNAME IS '附件名称';
COMMENT ON COLUMN tamsgnoticeannex.ANNEX IS '附件';
COMMENT ON COLUMN tamsgnoticeannex.MID IS '通知ID';

CREATE INDEX TAMSGNOTICEANNEX_MID_INDEX ON tamsgnoticeannex(MID);

CREATE TABLE tamsgnoticestate(
    MID VARCHAR(36) NOT NULL,
    RECEIVERID VARCHAR(36) NOT NULL,
    READSIGN VARCHAR(1),
    PRIMARY KEY (MID,RECEIVERID)
);

COMMENT ON TABLE tamsgnoticestate IS '通知状态表';
COMMENT ON COLUMN tamsgnoticestate.MID IS '通知ID';
COMMENT ON COLUMN tamsgnoticestate.RECEIVERID IS '接收人ID';
COMMENT ON COLUMN tamsgnoticestate.READSIGN IS '已读标志';

CREATE INDEX TAMSGNOTICESTATE_RECEIVERID_INDEX ON tamsgnoticestate(RECEIVERID);

CREATE TABLE taobjecttags(
    OBJECTID VARCHAR(36) NOT NULL,
    OBJECTTYPE VARCHAR(3) NOT NULL,
    tagid VARCHAR(36) NOT NULL,
    PRIMARY KEY (OBJECTID,OBJECTTYPE,tagid)
);

COMMENT ON TABLE taobjecttags IS '对象表';
COMMENT ON COLUMN taobjecttags.OBJECTID IS '对象编号（用户编号、机构编号等）';
COMMENT ON COLUMN taobjecttags.OBJECTTYPE IS '对象类型（用户、机构等）';
COMMENT ON COLUMN taobjecttags.tagid IS 'TAGID';

CREATE INDEX TAOBJECTTAGS_TAGID_TYPE_INDEX ON taobjecttags(TAGID);

CREATE TABLE taonlinelog(
    LOGID VARCHAR(36) NOT NULL,
    USERID VARCHAR(36) NOT NULL,
    LOGINTIME DATETIME NOT NULL,
    CURRESOURCE VARCHAR(1000),
    CLIENTIP VARCHAR(200) NOT NULL,
    SESSIONID VARCHAR(200) NOT NULL,
    SYSPATH VARCHAR(50),
    SERVERIP VARCHAR(200),
    CLIENTSYSTEM VARCHAR(50),
    CLIENTBROWSER VARCHAR(50),
    CLIENTSCREENSIZE VARCHAR(50),
    HASHVERITY VARCHAR(200),
    PRIMARY KEY (LOGID)
);

COMMENT ON TABLE taonlinelog IS '用户在线日志表';
COMMENT ON COLUMN taonlinelog.LOGID IS '日志编号';
COMMENT ON COLUMN taonlinelog.USERID IS '用户编号';
COMMENT ON COLUMN taonlinelog.LOGINTIME IS '登录时间';
COMMENT ON COLUMN taonlinelog.CURRESOURCE IS '当前资源';
COMMENT ON COLUMN taonlinelog.CLIENTIP IS '客户端ip地址';
COMMENT ON COLUMN taonlinelog.SESSIONID IS '登录请求的sessionId';
COMMENT ON COLUMN taonlinelog.SYSPATH IS '系统路径';
COMMENT ON COLUMN taonlinelog.SERVERIP IS '服务端ip地址';
COMMENT ON COLUMN taonlinelog.CLIENTSYSTEM IS '客户端操作系统版本';
COMMENT ON COLUMN taonlinelog.CLIENTBROWSER IS '客户端浏览器版本';
COMMENT ON COLUMN taonlinelog.CLIENTSCREENSIZE IS '客户端屏幕分辨率大小';
COMMENT ON COLUMN taonlinelog.HASHVERITY IS 'hash 校验';

CREATE INDEX TAONLINELOG_USERID_INDEX ON taonlinelog(USERID);
CREATE INDEX TAONLINELOG_LOGINTIME_INDEX ON taonlinelog(LOGINTIME);

CREATE TABLE taonlinestatlog(
    STATDATE VARCHAR(20) NOT NULL,
    POINTINTIME VARCHAR(20) NOT NULL,
    loginnum NUMERIC(15),
    HASHVERITY VARCHAR(200),
    PRIMARY KEY (STATDATE,POINTINTIME)
);

COMMENT ON TABLE taonlinestatlog IS '在线统计日志表';
COMMENT ON COLUMN taonlinestatlog.STATDATE IS '统计时间（YYYY-MM-DD）';
COMMENT ON COLUMN taonlinestatlog.POINTINTIME IS '统计时点（HH:MM）';
COMMENT ON COLUMN taonlinestatlog.loginnum IS '在线用户数';
COMMENT ON COLUMN taonlinestatlog.HASHVERITY IS 'hash 校验';


CREATE TABLE taorg(
    ORGID VARCHAR(36) NOT NULL,
    ORGNAME VARCHAR(200) NOT NULL,
    SPELL VARCHAR(100) NOT NULL,
    PARENTID VARCHAR(36) NOT NULL,
    IDPATH VARCHAR(1024) NOT NULL,
    NAMEPATH VARCHAR(1024) NOT NULL,
    CUSTOMNO VARCHAR(30),
    ORDERNO NUMERIC(10),
    ORGLEVEL NUMERIC(16),
    AREA VARCHAR(36),
    EFFECTIVE VARCHAR(1) NOT NULL,
    ORGTYPE VARCHAR(2),
    CREATEUSER VARCHAR(36) NOT NULL,
    CREATETIME DATETIME,
    MODIFYTIME DATETIME,
    ORGMANAGER VARCHAR(36),
    ORGCODE VARCHAR(18),
    CONTACTS VARCHAR(36),
    ADDRESS VARCHAR(450),
    TEL VARCHAR(20),
    DESTORY VARCHAR(1),
    ORGTREETYPE VARCHAR(1),
    FIELD01 VARCHAR(1000),
    FIELD02 VARCHAR(1000),
    FIELD03 VARCHAR(1000),
    FIELD04 VARCHAR(1000),
    FIELD05 VARCHAR(1000),
    FIELD06 VARCHAR(1000),
    FIELD07 VARCHAR(1000),
    FIELD08 VARCHAR(1000),
    FIELD09 VARCHAR(1000),
    FIELD10 VARCHAR(1000),
    PRIMARY KEY (ORGID)
);

COMMENT ON TABLE taorg IS '组织机构表';
COMMENT ON COLUMN taorg.ORGID IS '组织编号';
COMMENT ON COLUMN taorg.ORGNAME IS '组织名称';
COMMENT ON COLUMN taorg.SPELL IS '组织名称拼音简写（如：民政部简写MZB）';
COMMENT ON COLUMN taorg.PARENTID IS '父组织编号';
COMMENT ON COLUMN taorg.IDPATH IS '组织编号路径';
COMMENT ON COLUMN taorg.NAMEPATH IS '组织名称路径';
COMMENT ON COLUMN taorg.CUSTOMNO IS '自定义编码';
COMMENT ON COLUMN taorg.ORDERNO IS '排序号';
COMMENT ON COLUMN taorg.ORGLEVEL IS '组织层级';
COMMENT ON COLUMN taorg.AREA IS '行政区划编号';
COMMENT ON COLUMN taorg.EFFECTIVE IS '有效性';
COMMENT ON COLUMN taorg.ORGTYPE IS '组织类型（机构（集团、子公司）、部门、组）;自定义扩展';
COMMENT ON COLUMN taorg.CREATEUSER IS '创建人';
COMMENT ON COLUMN taorg.CREATETIME IS '创建时间';
COMMENT ON COLUMN taorg.MODIFYTIME IS '最后更新时间';
COMMENT ON COLUMN taorg.ORGMANAGER IS '机构负责人';
COMMENT ON COLUMN taorg.ORGCODE IS '机构编码';
COMMENT ON COLUMN taorg.CONTACTS IS '机构联系人';
COMMENT ON COLUMN taorg.ADDRESS IS '联系人地址';
COMMENT ON COLUMN taorg.TEL IS '联系人电话';
COMMENT ON COLUMN taorg.DESTORY IS '是否销毁';
COMMENT ON COLUMN taorg.ORGTREETYPE IS '树类型;机构树，字典可扩展';
COMMENT ON COLUMN taorg.FIELD01 IS '备用字段1';
COMMENT ON COLUMN taorg.FIELD02 IS '备用字段2';
COMMENT ON COLUMN taorg.FIELD03 IS '备用字段3';
COMMENT ON COLUMN taorg.FIELD04 IS '备用字段4';
COMMENT ON COLUMN taorg.FIELD05 IS '备用字段5';
COMMENT ON COLUMN taorg.FIELD06 IS '备用字段6';
COMMENT ON COLUMN taorg.FIELD07 IS '备用字段7';
COMMENT ON COLUMN taorg.FIELD08 IS '备用字段8';
COMMENT ON COLUMN taorg.FIELD09 IS '备用字段9';
COMMENT ON COLUMN taorg.FIELD10 IS '备用字段10';

CREATE INDEX TAORG_PARENTID_INDEX ON taorg(PARENTID);
CREATE INDEX TAORG_IDPATH_INDEX ON taorg(IDPATH);
CREATE INDEX TAORG_ORGNAME_INDEX ON taorg(ORGNAME);
CREATE INDEX TAORG_SPELL_INDEX ON taorg(SPELL);
CREATE INDEX TAORG_CUSTOMNO_INDEX ON taorg(CUSTOMNO);

CREATE TABLE taorgmg(
    ROLEID VARCHAR(36) NOT NULL,
    USERID VARCHAR(36) NOT NULL,
    ORGID VARCHAR(36) NOT NULL,
    EFFECTTIME DATETIME,
    PRIMARY KEY (ROLEID,USERID,ORGID)
);

COMMENT ON TABLE taorgmg IS '组织管理范围表（角色-组织、角色-个人）';
COMMENT ON COLUMN taorgmg.ROLEID IS '角色ID';
COMMENT ON COLUMN taorgmg.USERID IS '账号ID';
COMMENT ON COLUMN taorgmg.ORGID IS '组织ID';
COMMENT ON COLUMN taorgmg.EFFECTTIME IS '有效期';

CREATE INDEX TAORGMG_USER_INDEX ON taorgmg(USERID);
CREATE INDEX TAORGMG_ORGID_INDEX ON taorgmg(ORGID);

CREATE TABLE taorgoplog(
    LOGID VARCHAR(36) NOT NULL,
    BATCHNO VARCHAR(36) NOT NULL,
    OPTYPE VARCHAR(2) NOT NULL,
    INFLUENCEBODYTYPE VARCHAR(2) NOT NULL,
    INFLUENCEBODY VARCHAR(36) NOT NULL,
    OPBODYTYPE VARCHAR(2),
    OPSUBJECT VARCHAR(36),
    CHANGECONTENT BLOB,
    OPTIME DATETIME NOT NULL,
    OPUSER VARCHAR(36) NOT NULL,
    ISPERMISSION VARCHAR(1) NOT NULL,
    HASHVERITY VARCHAR(200),
    SESSIONID VARCHAR(200),
    PRIMARY KEY (LOGID)
);

COMMENT ON TABLE taorgoplog IS '组织人员权限操作日志表';
COMMENT ON COLUMN taorgoplog.LOGID IS '日志ID';
COMMENT ON COLUMN taorgoplog.BATCHNO IS '操作批次号';
COMMENT ON COLUMN taorgoplog.OPTYPE IS '操作类型';
COMMENT ON COLUMN taorgoplog.INFLUENCEBODYTYPE IS '影响主体类型';
COMMENT ON COLUMN taorgoplog.INFLUENCEBODY IS '影响主体';
COMMENT ON COLUMN taorgoplog.OPBODYTYPE IS '操作主体类型';
COMMENT ON COLUMN taorgoplog.OPSUBJECT IS '操作主体';
COMMENT ON COLUMN taorgoplog.CHANGECONTENT IS '操作内容';
COMMENT ON COLUMN taorgoplog.OPTIME IS '操作时间';
COMMENT ON COLUMN taorgoplog.OPUSER IS '操作者';
COMMENT ON COLUMN taorgoplog.ISPERMISSION IS '操作权限';
COMMENT ON COLUMN taorgoplog.HASHVERITY IS 'hash 校验';
COMMENT ON COLUMN taorgoplog.SESSIONID IS 'sessionid';

CREATE INDEX TAORGOPLOG_BATCHNO_INDEX ON taorgoplog(BATCHNO);
CREATE INDEX TAORGOPLOG_OPTYPE_INDEX ON taorgoplog(OPTYPE);
CREATE INDEX TAORGOPLOG_INFLUENCEBODY_INDEX ON taorgoplog(INFLUENCEBODY);
CREATE INDEX TAORGOPLOG_INFLUENCEBODYTYPE_INDEX ON taorgoplog(INFLUENCEBODYTYPE);
CREATE INDEX TAORGOPLOG_OPTIME_INDEX ON taorgoplog(OPTIME);
CREATE INDEX TAORGOPLOG_OPUSER_INDEX ON taorgoplog(OPUSER);

CREATE TABLE taorgview(
    ROLEID VARCHAR(36),
    ORGID VARCHAR(36),
    PRIMARY KEY (ROLEID,ORGID)
);

COMMENT ON TABLE taorgview IS '角色组织可见范围表';
COMMENT ON COLUMN taorgview.ROLEID IS '角色ID';
COMMENT ON COLUMN taorgview.ORGID IS '组织ID';

CREATE INDEX TAORGVIEW_ORGID_INDEX ON taorgview(ORGID);

CREATE TABLE taparam(
    PARAMID VARCHAR(36) NOT NULL,
    PARAMNAME VARCHAR(255),
    VALUESCOPE VARCHAR(255),
    CODE VARCHAR(255),
    PARAMDESC VARCHAR(255),
    VALUE VARCHAR(255),
    RESOURCEID VARCHAR(36) NOT NULL,
    PRIMARY KEY (PARAMID)
);

COMMENT ON TABLE taparam IS '资源参数表';
COMMENT ON COLUMN taparam.PARAMID IS '参数ID';
COMMENT ON COLUMN taparam.PARAMNAME IS '参数名称';
COMMENT ON COLUMN taparam.VALUESCOPE IS '值范围';
COMMENT ON COLUMN taparam.CODE IS '编码';
COMMENT ON COLUMN taparam.PARAMDESC IS '参数说明';
COMMENT ON COLUMN taparam.VALUE IS '值';
COMMENT ON COLUMN taparam.RESOURCEID IS '资源ID';

CREATE INDEX TAPARAM_RESOURCEID_INDEX ON taparam(RESOURCEID);

CREATE TABLE taratelimit(
    urlid VARCHAR(100) NOT NULL,
    rate NUMERIC(10),
    maxcount BIGINT,
    timeout BIGINT,
    enable VARCHAR(100),
    PRIMARY KEY (urlid)
);

COMMENT ON TABLE taratelimit IS 'url访问限流配置表';


CREATE TABLE taregisterchannel(
    REGISTERCHANNELID VARCHAR(64) NOT NULL,
    REGISTERCHANNELNAME VARCHAR(100) NOT NULL,
    REGISTERCHANNELURL VARCHAR(100),
    CREATEUSER VARCHAR(64),
    CREATETIME DATETIME,
    EFFECTIVETIME DATETIME,
    EFFECTIVE VARCHAR(1),
    ORGID VARCHAR(64),
    ROLEID VARCHAR(64),
    PRIMARY KEY (REGISTERCHANNELID)
);

COMMENT ON TABLE taregisterchannel IS '注册通道配置表';

CREATE INDEX TAREGISTERCHANNEL_ORGID_INDEX ON taregisterchannel(ORGID);

CREATE TABLE taresource(
    RESOURCEID VARCHAR(36) NOT NULL,
    PRESOURCEID VARCHAR(36) NOT NULL,
    NAME VARCHAR(100) NOT NULL,
    CODE VARCHAR(30),
    SYSCODE VARCHAR(20) NOT NULL,
    URL VARCHAR(100),
    orderno NUMERIC(10),
    IDPATH VARCHAR(1024) NOT NULL,
    NAMEPATH VARCHAR(1024) NOT NULL,
    RESOURCELEVEL VARCHAR(2) NOT NULL,
    ICON VARCHAR(30),
    ICONCOLOR VARCHAR(7),
    SECURITYPOLICY VARCHAR(2) NOT NULL,
    SECURITYLEVEL NUMERIC(4),
    RESOURCETYPE VARCHAR(2) NOT NULL,
    EFFECTIVE VARCHAR(1) NOT NULL,
    ISDISPLAY VARCHAR(1),
    ISFILEDSCONTROL VARCHAR(1),
    CREATETIME DATETIME,
    CREATEUSER VARCHAR(36) NOT NULL,
    UIAUTHORITYPOLICY VARCHAR(2),
    WORKBENCH VARCHAR(1),
    IMAGE BLOB,
    OPENMODE VARCHAR(1),
    FIELD01 VARCHAR(10),
    FIELD02 VARCHAR(10),
    FIELD03 VARCHAR(10),
    FIELD04 VARCHAR(10),
    FIELD05 VARCHAR(10),
    FIELD06 VARCHAR(10),
    FIELD07 VARCHAR(10),
    FIELD08 VARCHAR(10),
    FIELD09 VARCHAR(10),
    FIELD10 VARCHAR(10),
    PRIMARY KEY (RESOURCEID)
);

COMMENT ON TABLE taresource IS '功能资源表';
COMMENT ON COLUMN taresource.RESOURCEID IS '功能资源ID';
COMMENT ON COLUMN taresource.PRESOURCEID IS '父级功能资源ID';
COMMENT ON COLUMN taresource.NAME IS '功能名称';
COMMENT ON COLUMN taresource.CODE IS '自定义编码';
COMMENT ON COLUMN taresource.SYSCODE IS '功能所属系统';
COMMENT ON COLUMN taresource.URL IS '功能路径';
COMMENT ON COLUMN taresource.ORDERNO IS '排序号';
COMMENT ON COLUMN taresource.IDPATH IS '资源ID路径';
COMMENT ON COLUMN taresource.NAMEPATH IS '功能名称路径';
COMMENT ON COLUMN taresource.RESOURCELEVEL IS '功能资源层级';
COMMENT ON COLUMN taresource.ICON IS '图标名称';
COMMENT ON COLUMN taresource.ICONCOLOR IS '图标颜色';
COMMENT ON COLUMN taresource.SECURITYPOLICY IS '安全策略';
COMMENT ON COLUMN taresource.SECURITYLEVEL IS '安全认证级别';
COMMENT ON COLUMN taresource.RESOURCETYPE IS '菜单类型';
COMMENT ON COLUMN taresource.EFFECTIVE IS '有效性';
COMMENT ON COLUMN taresource.ISDISPLAY IS '是否显示';
COMMENT ON COLUMN taresource.ISFILEDSCONTROL IS '是否字段权限控制';
COMMENT ON COLUMN taresource.CREATETIME IS '创建时间';
COMMENT ON COLUMN taresource.CREATEUSER IS '创建者';
COMMENT ON COLUMN taresource.UIAUTHORITYPOLICY IS '界面元素授权';
COMMENT ON COLUMN taresource.WORKBENCH IS '是否工作台模块';
COMMENT ON COLUMN taresource.IMAGE IS '功能资源图片（端应用）';
COMMENT ON COLUMN taresource.OPENMODE IS '菜单打开模式（1工作页，2浏览器页，3弹窗）';
COMMENT ON COLUMN taresource.FIELD01 IS '扩展字段01';
COMMENT ON COLUMN taresource.FIELD02 IS '扩展字段02';
COMMENT ON COLUMN taresource.FIELD03 IS '扩展字段03';
COMMENT ON COLUMN taresource.FIELD04 IS '扩展字段04';
COMMENT ON COLUMN taresource.FIELD05 IS '扩展字段05';
COMMENT ON COLUMN taresource.FIELD06 IS '扩展字段06';
COMMENT ON COLUMN taresource.FIELD07 IS '扩展字段07';
COMMENT ON COLUMN taresource.FIELD08 IS '扩展字段08';
COMMENT ON COLUMN taresource.FIELD09 IS '扩展字段09';
COMMENT ON COLUMN taresource.FIELD10 IS '扩展字段10';

CREATE INDEX TARESOURCE_PRESOURCEID_INDEX ON taresource(PRESOURCEID);

CREATE TABLE taresourcecategory(
    CATEGORYID VARCHAR(36),
    CATEGORYNAME VARCHAR(300),
    EFFECTIVE VARCHAR(1),
    CODE VARCHAR(100),
    CATEGORYCONTENT VARCHAR(255),
    CREATETIME DATETIME,
    CREATEUSER VARCHAR(36),
    PRIMARY KEY (CATEGORYID)
);

COMMENT ON TABLE taresourcecategory IS '自定义资源类型表';
COMMENT ON COLUMN taresourcecategory.CATEGORYID IS '类别ID';
COMMENT ON COLUMN taresourcecategory.CATEGORYNAME IS '类别名称';
COMMENT ON COLUMN taresourcecategory.EFFECTIVE IS '有效性';
COMMENT ON COLUMN taresourcecategory.CODE IS '类别编码';
COMMENT ON COLUMN taresourcecategory.CATEGORYCONTENT IS '类别描述';
COMMENT ON COLUMN taresourcecategory.CREATETIME IS '创建时间';
COMMENT ON COLUMN taresourcecategory.CREATEUSER IS '创建者';

CREATE TABLE taresourceui(
    PAGEELEMENTID VARCHAR(36),
    RESOURCEID VARCHAR(36),
    ELENMENTNAME VARCHAR(20),
    ELEMENTID VARCHAR(30),
    CODE VARCHAR(20),
    AUTHORITYPOLICY VARCHAR(2),
    CREATEUSER VARCHAR(36),
    CREATETIME DATETIME,
    FIELD01 VARCHAR(10),
    FIELD02 VARCHAR(10),
    FIELD03 VARCHAR(10),
    EFFECTIVE VARCHAR(1),
    PRIMARY KEY (PAGEELEMENTID)
);

COMMENT ON TABLE taresourceui IS '界面元素表（弃用）';
COMMENT ON COLUMN taresourceui.PAGEELEMENTID IS '界面元素配置ID';
COMMENT ON COLUMN taresourceui.RESOURCEID IS '功能资源ID';
COMMENT ON COLUMN taresourceui.ELENMENTNAME IS '界面元素名称';
COMMENT ON COLUMN taresourceui.ELEMENTID IS '界面元素ID';
COMMENT ON COLUMN taresourceui.CODE IS '自定义编码';
COMMENT ON COLUMN taresourceui.AUTHORITYPOLICY IS '授权策略';
COMMENT ON COLUMN taresourceui.CREATEUSER IS '创建者';
COMMENT ON COLUMN taresourceui.CREATETIME IS '创建时间';
COMMENT ON COLUMN taresourceui.FIELD01 IS '扩展字段01';
COMMENT ON COLUMN taresourceui.FIELD02 IS '扩展字段02';
COMMENT ON COLUMN taresourceui.FIELD03 IS '扩展字段03';
COMMENT ON COLUMN taresourceui.EFFECTIVE IS '有效性';

CREATE TABLE taresourceurl(
    RESOURCEID VARCHAR(36),
    URLID VARCHAR(36),
    AUTHORITYPOLICY VARCHAR(1),
    PRIMARY KEY (RESOURCEID,URLID)
);

COMMENT ON TABLE taresourceurl IS '功能资源路径表';
COMMENT ON COLUMN taresourceurl.RESOURCEID IS '功能资源ID';
COMMENT ON COLUMN taresourceurl.URLID IS '服务ID';
COMMENT ON COLUMN taresourceurl.AUTHORITYPOLICY IS '是否独立授权';

CREATE INDEX TARESOURCEURL_URLID_INDEX ON taresourceurl(URLID);

CREATE TABLE tarole(
    ROLEID VARCHAR(36) NOT NULL,
    ROLENAME VARCHAR(150) NOT NULL,
    ISADMIN VARCHAR(1),
    ROLELEVEL NUMERIC(32),
    ORGID VARCHAR(36) NOT NULL,
    ROLETYPE VARCHAR(2) NOT NULL,
    EFFECTIVE VARCHAR(1) NOT NULL,
    EFFECTIVETIME DATETIME,
    CREATEUSER VARCHAR(36),
    CREATETIME DATETIME,
    ROLEDESC VARCHAR(150),
    ROLESIGN VARCHAR(2),
    SUBORDINATE VARCHAR(1),
    ORGAUTHTYPE VARCHAR(1),
    ISSHAREORGAUTH VARCHAR(1),
    CUSTOMNO VARCHAR(36),
    PRIMARY KEY (ROLEID)
);

COMMENT ON TABLE tarole IS '角色表';
COMMENT ON COLUMN tarole.ROLEID IS '角色id';
COMMENT ON COLUMN tarole.ROLENAME IS '角色名称';
COMMENT ON COLUMN tarole.ISADMIN IS '是否为管理员';
COMMENT ON COLUMN tarole.ROLELEVEL IS '角色层级';
COMMENT ON COLUMN tarole.ORGID IS '组织id';
COMMENT ON COLUMN tarole.ROLETYPE IS '角色类型';
COMMENT ON COLUMN tarole.EFFECTIVE IS '有效状态';
COMMENT ON COLUMN tarole.EFFECTIVETIME IS '有效时间';
COMMENT ON COLUMN tarole.CREATEUSER IS '创建人';
COMMENT ON COLUMN tarole.CREATETIME IS '创建时间';
COMMENT ON COLUMN tarole.ROLEDESC IS '角色排序号';
COMMENT ON COLUMN tarole.ROLESIGN IS '角色标志';
COMMENT ON COLUMN tarole.SUBORDINATE IS '子组织可见类型;不可见、子组织可见、自定义可见范围';
COMMENT ON COLUMN tarole.ORGAUTHTYPE IS '管理员权限范围类型;本组织数据、自定义组织范围数据、直属组织数据';
COMMENT ON COLUMN tarole.ISSHAREORGAUTH IS '是否共享管理员组织管理范围;共享状态，不允许个人自定义组织管理范围';
COMMENT ON COLUMN tarole.CUSTOMNO IS '自定义编码';

CREATE INDEX TAROLE_ORGID_INDEX ON tarole(ORGID);
CREATE INDEX TAROLE_ROLENAME_INDEX ON tarole(ROLENAME);

CREATE TABLE taroleauthority(
    ROLEID VARCHAR(36) NOT NULL,
    RESOURCEID VARCHAR(36) NOT NULL,
    RESOURCETYPE VARCHAR(1) NOT NULL,
    USEPERMISSION VARCHAR(1),
    REPERMISSION VARCHAR(1),
    REAUTHRITY VARCHAR(1),
    CREATEUSER VARCHAR(36),
    CREATETIME DATETIME,
    EFFECTTIME DATETIME,
    PRIMARY KEY (ROLEID,RESOURCEID,RESOURCETYPE)
);

COMMENT ON TABLE taroleauthority IS '角色资源权限表';
COMMENT ON COLUMN taroleauthority.ROLEID IS '角色id';
COMMENT ON COLUMN taroleauthority.RESOURCEID IS '资源id';
COMMENT ON COLUMN taroleauthority.RESOURCETYPE IS '资源类型;功能资源、自定义资源';
COMMENT ON COLUMN taroleauthority.USEPERMISSION IS '用户使用权限';
COMMENT ON COLUMN taroleauthority.REPERMISSION IS '授权权限';
COMMENT ON COLUMN taroleauthority.REAUTHRITY IS '再授权权限';
COMMENT ON COLUMN taroleauthority.CREATEUSER IS '创建人';
COMMENT ON COLUMN taroleauthority.CREATETIME IS '创建时间';
COMMENT ON COLUMN taroleauthority.EFFECTTIME IS '有效时间';

CREATE INDEX TAROLEAUTHORITY_RESOURCEID_INDEX ON taroleauthority(RESOURCEID);

CREATE TABLE taroleurlauthority(
    ROLEID VARCHAR(36),
    URLID VARCHAR(36),
    RESOURCEID VARCHAR(36),
    PRIMARY KEY (ROLEID,URLID,RESOURCEID)
);

COMMENT ON TABLE taroleurlauthority IS '角色url权限表（独立授权）';
COMMENT ON COLUMN taroleurlauthority.ROLEID IS '角色id';
COMMENT ON COLUMN taroleurlauthority.URLID IS '路径id';
COMMENT ON COLUMN taroleurlauthority.RESOURCEID IS '资源id';

CREATE INDEX TAROLEURLAUTHORITY_URLID_INDEX ON taroleurlauthority(URLID);
CREATE INDEX TAROLEURLAUTHORITY_RESOURCEID_INDEX ON taroleurlauthority(RESOURCEID);

CREATE TABLE taroleuser(
    ROLEID VARCHAR(36) NOT NULL,
    OBJECTID VARCHAR(36) NOT NULL,
    OBJECTTYPE VARCHAR(1) NOT NULL,
    DEFAULTROLE VARCHAR(1),
    CREATEUSER VARCHAR(36) NOT NULL,
    CREATETIME DATETIME NOT NULL,
    PRIMARY KEY (ROLEID,OBJECTID,OBJECTTYPE)
);

COMMENT ON TABLE taroleuser IS '角色实体关系表（用户、组织）';
COMMENT ON COLUMN taroleuser.ROLEID IS '角色ID';
COMMENT ON COLUMN taroleuser.OBJECTID IS '实体ID';
COMMENT ON COLUMN taroleuser.OBJECTTYPE IS '实体类型;账号、组织';
COMMENT ON COLUMN taroleuser.DEFAULTROLE IS '默认角色';
COMMENT ON COLUMN taroleuser.CREATEUSER IS '创建人';
COMMENT ON COLUMN taroleuser.CREATETIME IS '创建时间';


CREATE INDEX TAROLEUSER_OBJECTID_TYPE_INDEX ON taroleuser(OBJECTID,OBJECTTYPE);

CREATE TABLE taroleworkbench(
    ROLEID VARCHAR(36) NOT NULL,
    WORKBENCHJSON TEXT,
    PRIMARY KEY (ROLEID)
);

COMMENT ON TABLE taroleworkbench IS '角色工作台模板';
COMMENT ON COLUMN taroleworkbench.ROLEID IS '角色id';
COMMENT ON COLUMN taroleworkbench.WORKBENCHJSON IS '工作台内容';


CREATE TABLE taserverexceptionlog(
    LOGID VARCHAR(50) NOT NULL,
    IPADDRESS VARCHAR(100),
    PORT VARCHAR(10),
    EXCEPTIONNAME VARCHAR(255),
    CONTENT BLOB,
    CREATETIME DATETIME,
    SYSPATH VARCHAR(50),
    CLIENTIP VARCHAR(50),
    URL VARCHAR(100),
    MENUID VARCHAR(50),
    MENUNAME VARCHAR(30),
    USERAGENT VARCHAR(200),
    EXCEPTIONTYPE VARCHAR(2),
    REQUESTPARAMETER BLOB,
    HASHVERITY VARCHAR(200),
    USERID VARCHAR(36),
    PRIMARY KEY (LOGID)
);

COMMENT ON TABLE taserverexceptionlog IS '服务器异常日志表';
COMMENT ON COLUMN taserverexceptionlog.LOGID IS '日志id';
COMMENT ON COLUMN taserverexceptionlog.IPADDRESS IS 'ip地址';
COMMENT ON COLUMN taserverexceptionlog.PORT IS '端口号';
COMMENT ON COLUMN taserverexceptionlog.EXCEPTIONNAME IS '异常名称';
COMMENT ON COLUMN taserverexceptionlog.CONTENT IS '异常内容';
COMMENT ON COLUMN taserverexceptionlog.CREATETIME IS '创建时间';
COMMENT ON COLUMN taserverexceptionlog.SYSPATH IS '系统上下文';
COMMENT ON COLUMN taserverexceptionlog.CLIENTIP IS '客户端ip';
COMMENT ON COLUMN taserverexceptionlog.URL IS '请求地址';
COMMENT ON COLUMN taserverexceptionlog.MENUID IS '菜单id';
COMMENT ON COLUMN taserverexceptionlog.MENUNAME IS '菜单名称';
COMMENT ON COLUMN taserverexceptionlog.EXCEPTIONTYPE IS '异常类型';
COMMENT ON COLUMN taserverexceptionlog.REQUESTPARAMETER IS '请求参数';
COMMENT ON COLUMN taserverexceptionlog.HASHVERITY IS 'hash 校验';
COMMENT ON COLUMN taserverexceptionlog.USERID IS 'userid';

CREATE INDEX TASERVEREXCEPTIONLOG_TIME_INDEX ON taserverexceptionlog(CREATETIME);

CREATE TABLE tasysbehaviorlog(
    BEHAVIORLOGID VARCHAR(36) NOT NULL,
    SERVICENAME VARCHAR(36),
    SERVICEPATH VARCHAR(120),
    SERVICEPARAMCONTENT TEXT,
    RESOURCEID VARCHAR(36),
    BUSINESSTYPE VARCHAR(256),
    BEGINTIME VARCHAR(36),
    ENDTIME VARCHAR(36),
    TIMEUSED VARCHAR(36),
    SUCCESSFLAG VARCHAR(36),
    TRACEID VARCHAR(36),
    LOGINWAY VARCHAR(36),
    CLIENTIP VARCHAR(36),
    USERID VARCHAR(36)
);

COMMENT ON TABLE tasysbehaviorlog IS '行为日志表';
COMMENT ON COLUMN tasysbehaviorlog.BEHAVIORLOGID IS '主键';
COMMENT ON COLUMN tasysbehaviorlog.SERVICENAME IS '服务名';
COMMENT ON COLUMN tasysbehaviorlog.SERVICEPATH IS '服务路径';
COMMENT ON COLUMN tasysbehaviorlog.SERVICEPARAMCONTENT IS '服务请求参数内容';
COMMENT ON COLUMN tasysbehaviorlog.RESOURCEID IS '菜单ID';
COMMENT ON COLUMN tasysbehaviorlog.BUSINESSTYPE IS '业务类型';
COMMENT ON COLUMN tasysbehaviorlog.BEGINTIME IS '开始时间';
COMMENT ON COLUMN tasysbehaviorlog.ENDTIME IS '结束时间';
COMMENT ON COLUMN tasysbehaviorlog.TIMEUSED IS '执行时间';
COMMENT ON COLUMN tasysbehaviorlog.SUCCESSFLAG IS '是否异常标志';
COMMENT ON COLUMN tasysbehaviorlog.TRACEID IS '链路ID';
COMMENT ON COLUMN tasysbehaviorlog.LOGINWAY IS '登录方式';
COMMENT ON COLUMN tasysbehaviorlog.CLIENTIP IS '客户端IP';
COMMENT ON COLUMN tasysbehaviorlog.USERID IS '经办人ID';

CREATE TABLE tasysbehaviorlogdetail(
    BEHAVIORLOGDETAILID VARCHAR(36) NOT NULL,
    BEHAVIORLOGID VARCHAR(36),
    OPERATETYPE VARCHAR(36),
    OPERATEOBJECTID VARCHAR(36),
    OPERATCONTET VARCHAR(256),
    BUSINESSTYPE VARCHAR(256),
    BUSINESSID VARCHAR(36),
    BEGINTIME VARCHAR(36),
    ENDTIME VARCHAR(36),
    TIMEUSED VARCHAR(36),
    SUCCESSFLAG VARCHAR(36)
);

COMMENT ON TABLE tasysbehaviorlogdetail IS '行为日志细节表';
COMMENT ON COLUMN tasysbehaviorlogdetail.BEHAVIORLOGDETAILID IS '主键';
COMMENT ON COLUMN tasysbehaviorlogdetail.BEHAVIORLOGID IS '行为日志ID';
COMMENT ON COLUMN tasysbehaviorlogdetail.OPERATETYPE IS '操作对象类型';
COMMENT ON COLUMN tasysbehaviorlogdetail.OPERATEOBJECTID IS '操作对象ID';
COMMENT ON COLUMN tasysbehaviorlogdetail.OPERATCONTET IS '操作行为描述';
COMMENT ON COLUMN tasysbehaviorlogdetail.BUSINESSTYPE IS '业务类型';
COMMENT ON COLUMN tasysbehaviorlogdetail.BUSINESSID IS '业务编号';
COMMENT ON COLUMN tasysbehaviorlogdetail.BEGINTIME IS '开始时间';
COMMENT ON COLUMN tasysbehaviorlogdetail.ENDTIME IS '结束时间';
COMMENT ON COLUMN tasysbehaviorlogdetail.TIMEUSED IS '执行时间';
COMMENT ON COLUMN tasysbehaviorlogdetail.SUCCESSFLAG IS '成功标志';


CREATE TABLE tasysconfig(
    id VARCHAR(36) NOT NULL,
    fieldname VARCHAR(100),
    defaultvalue VARCHAR(800),
    originalvalue VARCHAR(800),
    datatype VARCHAR(1),
    functiontype VARCHAR(1),
    collectionname VARCHAR(100),
    collectiondatas VARCHAR(2000),
    flag NUMERIC(10),
    configcomment VARCHAR(1024),
    createtime DATETIME,
    updatetime DATETIME,
    opuserid VARCHAR(36),
    opusername VARCHAR(36),
    classname VARCHAR(200),
    PRIMARY KEY (id)
);

COMMENT ON TABLE tasysconfig IS '系统配置';
COMMENT ON COLUMN tasysconfig.id IS '系统配置id';
COMMENT ON COLUMN tasysconfig.fieldname IS '字段名称';
COMMENT ON COLUMN tasysconfig.defaultvalue IS '默认值';
COMMENT ON COLUMN tasysconfig.originalvalue IS '配置原始值';
COMMENT ON COLUMN tasysconfig.datatype IS '数据类型';
COMMENT ON COLUMN tasysconfig.functiontype IS '所属配置区域';
COMMENT ON COLUMN tasysconfig.collectionname IS '码表类型';
COMMENT ON COLUMN tasysconfig.collectiondatas IS '码表值';
COMMENT ON COLUMN tasysconfig.flag IS '更新标志';
COMMENT ON COLUMN tasysconfig.configcomment IS '备注';
COMMENT ON COLUMN tasysconfig.createtime IS '创建时间';
COMMENT ON COLUMN tasysconfig.updatetime IS '更新时间';
COMMENT ON COLUMN tasysconfig.opuserid IS '操作者人员id';
COMMENT ON COLUMN tasysconfig.opusername IS '操作人人员姓名';
COMMENT ON COLUMN tasysconfig.classname IS '属性所属类名';


CREATE TABLE tatag(
    TAGID VARCHAR(36) NOT NULL,
    TAGNAME VARCHAR(255),
    TAGTYPE VARCHAR(3),
    CREATETIME DATETIME NOT NULL,
    EFFECTIVE VARCHAR(1) NOT NULL,
    CREATEUSER VARCHAR(36) NOT NULL,
    DESTORY VARCHAR(1) NOT NULL,
    UPDATETIME DATETIME NOT NULL,
    PRIMARY KEY (TAGID)
);

COMMENT ON TABLE tatag IS '标签表';
COMMENT ON COLUMN tatag.TAGID IS '标签id';
COMMENT ON COLUMN tatag.TAGNAME IS '标签名';
COMMENT ON COLUMN tatag.TAGTYPE IS '标签类型';
COMMENT ON COLUMN tatag.CREATETIME IS '创建时间';
COMMENT ON COLUMN tatag.EFFECTIVE IS '有效标识';
COMMENT ON COLUMN tatag.CREATEUSER IS '创建人';
COMMENT ON COLUMN tatag.DESTORY IS '销毁标识';
COMMENT ON COLUMN tatag.UPDATETIME IS '更新时间';


CREATE TABLE tatemplate(
    TEMPLATEID VARCHAR(36) NOT NULL,
    TEMPLATENAME VARCHAR(100) NOT NULL,
    TEMPLATETYPE VARCHAR(2) NOT NULL,
    TEMPLATEINTRO VARCHAR(255) NOT NULL,
    TEMPLATECONTENT TEXT NOT NULL,
    EFFECTIVE VARCHAR(1) NOT NULL,
    DESTROY VARCHAR(1) NOT NULL,
    CREATEUSER VARCHAR(36) NOT NULL,
    CREATETIME DATETIME,
    PRIMARY KEY (TEMPLATEID)
);

COMMENT ON TABLE tatemplate IS '在线表单模板表（弃用）';
COMMENT ON COLUMN tatemplate.TEMPLATEID IS '模板ID';
COMMENT ON COLUMN tatemplate.TEMPLATENAME IS '模板名称';
COMMENT ON COLUMN tatemplate.TEMPLATETYPE IS '模板类型';
COMMENT ON COLUMN tatemplate.TEMPLATEINTRO IS '模板描述';
COMMENT ON COLUMN tatemplate.TEMPLATECONTENT IS '模板内容';
COMMENT ON COLUMN tatemplate.EFFECTIVE IS '有效性';
COMMENT ON COLUMN tatemplate.DESTROY IS '是否销毁';
COMMENT ON COLUMN tatemplate.CREATEUSER IS '创建者';
COMMENT ON COLUMN tatemplate.CREATETIME IS '创建时间';


CREATE TABLE taurl(
    URLID VARCHAR(36) NOT NULL,
    NAME VARCHAR(128) NOT NULL,
    URL VARCHAR(255) NOT NULL,
    NAMESPACE VARCHAR(36),
    TYPE VARCHAR(1) NOT NULL,
    EFFECTIVE VARCHAR(2) NOT NULL,
    CREATETIME DATETIME NOT NULL,
    CREATEUSER VARCHAR(36) NOT NULL,
    FIELDKEY VARCHAR(255),
    PRIMARY KEY (URLID)
);

COMMENT ON TABLE taurl IS 'URL 服务表';
COMMENT ON COLUMN taurl.URLID IS '主键';
COMMENT ON COLUMN taurl.NAME IS 'url 名称';
COMMENT ON COLUMN taurl.URL IS 'url 地址';
COMMENT ON COLUMN taurl.NAMESPACE IS '命名空间（可空）';
COMMENT ON COLUMN taurl.TYPE IS '类型（命名空间还是普通rest url 地址）';
COMMENT ON COLUMN taurl.EFFECTIVE IS '有效状态';
COMMENT ON COLUMN taurl.CREATETIME IS '创建时间';
COMMENT ON COLUMN taurl.CREATEUSER IS '创建账户';
COMMENT ON COLUMN taurl.FIELDKEY IS '界面权限标识';

CREATE INDEX TAURL_NAMESPACE_INDEX ON taurl(NAMESPACE);

CREATE TABLE tauser(
    USERID VARCHAR(36) NOT NULL,
    LOGINID VARCHAR(30) NOT NULL,
    PASSWORD VARCHAR(256) NOT NULL,
    PASSWORDDEFAULTNUM NUMERIC(16),
    PWDLASTMODIFYDATE DATETIME,
    ISLOCK VARCHAR(1),
    ORDERNO NUMERIC(10),
    CREATEUSER VARCHAR(36),
    CREATETIME DATETIME,
    MODIFYTIME DATETIME,
    DESTORY VARCHAR(1) NOT NULL,
    ACCOUNTSOURCE VARCHAR(2),
    EFFECTIVE VARCHAR(1) NOT NULL,
    EFFECTIVETIME DATETIME,
    ACCOUNTTYPE VARCHAR(1),
    USERINFOID VARCHAR(36),
    PRIMARY KEY (USERID)
);

COMMENT ON TABLE tauser IS '账号表（原用户表）';
COMMENT ON COLUMN tauser.USERID IS '用户编号';
COMMENT ON COLUMN tauser.LOGINID IS '登录账号';
COMMENT ON COLUMN tauser.PASSWORD IS '登录密码';
COMMENT ON COLUMN tauser.PASSWORDDEFAULTNUM IS '密码错误次数';
COMMENT ON COLUMN tauser.PWDLASTMODIFYDATE IS '密码最后修改时间';
COMMENT ON COLUMN tauser.ISLOCK IS '是否锁定';
COMMENT ON COLUMN tauser.ORDERNO IS '排序号';
COMMENT ON COLUMN tauser.CREATEUSER IS '创建人';
COMMENT ON COLUMN tauser.CREATETIME IS '创建时间';
COMMENT ON COLUMN tauser.MODIFYTIME IS '修改时间';
COMMENT ON COLUMN tauser.DESTORY IS '销毁标识';
COMMENT ON COLUMN tauser.ACCOUNTSOURCE IS '账户来源;系统管理录入、账号申请审核、注册通道注册';
COMMENT ON COLUMN tauser.EFFECTIVE IS '有效标识';
COMMENT ON COLUMN tauser.EFFECTIVETIME IS '有效时间';
COMMENT ON COLUMN tauser.ACCOUNTTYPE IS '账号类型;经办管理账号、自然人账号';
COMMENT ON COLUMN tauser.USERINFOID IS '人员信息ID';

CREATE INDEX TAUSER_LOGINID_INDEX ON tauser(LOGINID);
CREATE INDEX TAUSER_USERINFOID_INDEX ON tauser(USERINFOID);

CREATE TABLE tauserconnection(
    userid VARCHAR(255),
    providerid VARCHAR(255),
    provideruserid VARCHAR(255),
    rank INTEGER,
    displayname VARCHAR(255),
    profileurl VARCHAR(512),
    imageurl VARCHAR(512),
    accesstoken VARCHAR(512),
    secret VARCHAR(512),
    refreshtoken VARCHAR(512),
    expiretime BIGINT,
    PRIMARY KEY (userid,providerid,provideruserid)
);

COMMENT ON TABLE tauserconnection IS '用户连接表（social登录凭证）';
COMMENT ON COLUMN tauserconnection.userid IS '系统用户id';
COMMENT ON COLUMN tauserconnection.providerid IS '第三方id';
COMMENT ON COLUMN tauserconnection.provideruserid IS '第三方用户id';
COMMENT ON COLUMN tauserconnection.rank IS '次序（数字越小优先级越高）';
COMMENT ON COLUMN tauserconnection.displayname IS '第三方用户名';
COMMENT ON COLUMN tauserconnection.profileurl IS '简介地址';
COMMENT ON COLUMN tauserconnection.imageurl IS '头像地址';
COMMENT ON COLUMN tauserconnection.accesstoken IS '访问token';
COMMENT ON COLUMN tauserconnection.secret IS '应用程序口令';
COMMENT ON COLUMN tauserconnection.refreshtoken IS '刷新token';
COMMENT ON COLUMN tauserconnection.expiretime IS '过期时间';


CREATE TABLE tauserinfo(
    USERINFOID VARCHAR(36) NOT NULL,
    ORDERNO NUMERIC(10),
    NAME VARCHAR(450),
    SEX VARCHAR(2),
    IDCARDTYPE VARCHAR(2),
    IDCARDNO VARCHAR(30),
    MOBILE VARCHAR(20),
    CREATEUSER VARCHAR(36),
    CREATETIME DATETIME,
    MODIFYTIME DATETIME,
    JOBNUMBER VARCHAR(15),
    STATE VARCHAR(10),
    BIRTHPLACE VARCHAR(12),
    ADDRESS VARCHAR(450),
    ZIPCODE VARCHAR(10),
    EMAIL VARCHAR(100),
    PHONE VARCHAR(20),
    EDUCATION VARCHAR(30),
    GRADUATESCHOOL VARCHAR(150),
    WORKPLACE VARCHAR(300),
    SPELL VARCHAR(255),
    DESTORY VARCHAR(1),
    EFFECTIVE VARCHAR(1),
    EFFECTIVETIME DATETIME,
    FIELD01 VARCHAR(1000),
    FIELD02 VARCHAR(1000),
    FIELD03 VARCHAR(1000),
    FIELD04 VARCHAR(1000),
    FIELD05 VARCHAR(1000),
    FIELD06 VARCHAR(1000),
    FIELD07 VARCHAR(1000),
    FIELD08 VARCHAR(1000),
    FIELD09 VARCHAR(1000),
    FIELD10 VARCHAR(1000),
    PRIMARY KEY (USERINFOID)
);

COMMENT ON TABLE tauserinfo IS '人员表';
COMMENT ON COLUMN tauserinfo.USERINFOID IS '用户编号';
COMMENT ON COLUMN tauserinfo.orderno IS '排序号';
COMMENT ON COLUMN tauserinfo.NAME IS '姓名';
COMMENT ON COLUMN tauserinfo.SEX IS '性别';
COMMENT ON COLUMN tauserinfo.IDCARDTYPE IS '证件类型';
COMMENT ON COLUMN tauserinfo.IDCARDNO IS '证件号码';
COMMENT ON COLUMN tauserinfo.MOBILE IS '手机号码';
COMMENT ON COLUMN tauserinfo.CREATEUSER IS '创建人';
COMMENT ON COLUMN tauserinfo.CREATETIME IS '创建时间';
COMMENT ON COLUMN tauserinfo.MODIFYTIME IS '修改时间';
COMMENT ON COLUMN tauserinfo.JOBNUMBER IS '工号';
COMMENT ON COLUMN tauserinfo.STATE IS '国家地区';
COMMENT ON COLUMN tauserinfo.BIRTHPLACE IS '户籍地（行政区划代码）';
COMMENT ON COLUMN tauserinfo.ADDRESS IS '联系地址';
COMMENT ON COLUMN tauserinfo.ZIPCODE IS '邮政编码';
COMMENT ON COLUMN tauserinfo.EMAIL IS '邮箱地址';
COMMENT ON COLUMN tauserinfo.PHONE IS '联系电话';
COMMENT ON COLUMN tauserinfo.EDUCATION IS '学历';
COMMENT ON COLUMN tauserinfo.GRADUATESCHOOL IS '毕业学校';
COMMENT ON COLUMN tauserinfo.WORKPLACE IS '工作单位';
COMMENT ON COLUMN tauserinfo.SPELL IS '拼音';
COMMENT ON COLUMN tauserinfo.DESTORY IS '销毁标识';
COMMENT ON COLUMN tauserinfo.EFFECTIVE IS '有效标识';
COMMENT ON COLUMN tauserinfo.EFFECTIVETIME IS '有效时间';
COMMENT ON COLUMN tauserinfo.FIELD01 IS '扩展字段01';
COMMENT ON COLUMN tauserinfo.FIELD02 IS '扩展字段02';
COMMENT ON COLUMN tauserinfo.FIELD03 IS '扩展字段03';
COMMENT ON COLUMN tauserinfo.FIELD04 IS '扩展字段04';
COMMENT ON COLUMN tauserinfo.FIELD05 IS '扩展字段05';
COMMENT ON COLUMN tauserinfo.FIELD06 IS '扩展字段06';
COMMENT ON COLUMN tauserinfo.FIELD07 IS '扩展字段07';
COMMENT ON COLUMN tauserinfo.FIELD08 IS '扩展字段08';
COMMENT ON COLUMN tauserinfo.FIELD09 IS '扩展字段09';
COMMENT ON COLUMN tauserinfo.FIELD10 IS '扩展字段10';

CREATE INDEX TAUSERINFO_NAME_INDEX ON tauserinfo(NAME);
CREATE INDEX TAUSERINFO_IDCARDNO_INDEX ON tauserinfo(IDCARDNO);
CREATE INDEX TAUSERINFO_MOBILE_INDEX ON tauserinfo(MOBILE);
CREATE INDEX TAUSERINFO_SPELL_INDEX ON tauserinfo(SPELL);

CREATE TABLE tauserorg(
    USERID VARCHAR(32),
    ORGID VARCHAR(32),
    ISDIRECT VARCHAR(1),
    PRIMARY KEY (USERID,ORGID)
);

COMMENT ON TABLE tauserorg IS '用户组织关联表';
COMMENT ON COLUMN tauserorg.USERID IS '用户id';
COMMENT ON COLUMN tauserorg.ORGID IS '组织id';
COMMENT ON COLUMN tauserorg.ISDIRECT IS '是否直属';

CREATE INDEX TAUSERORG_ORGID_INDEX ON tauserorg(ORGID);

CREATE TABLE tauserresource(
    userid VARCHAR(36),
    resourceid VARCHAR(36),
    createtime TIMESTAMP,
    PRIMARY KEY (userid,resourceid)
);

COMMENT ON TABLE tauserresource IS '用户资源表';
COMMENT ON COLUMN tauserresource.userid IS '用户id';
COMMENT ON COLUMN tauserresource.resourceid IS '资源id';
COMMENT ON COLUMN tauserresource.createtime IS '创建时间';

CREATE INDEX TAUSERRESOURCE_RESOURCEID_INDEX ON tauserresource(resourceid);

CREATE TABLE tauserworkbench(
    USERID VARCHAR(36) NOT NULL,
    WORKBENCHJSON TEXT,
    LASTCHOOSEWORKBENCH TEXT,
    PRIMARY KEY (USERID)
);

COMMENT ON TABLE tauserworkbench IS '用户工作台模板';
COMMENT ON COLUMN tauserworkbench.USERID IS '用户id';
COMMENT ON COLUMN tauserworkbench.WORKBENCHJSON IS '工作台内容';
COMMENT ON COLUMN tauserworkbench.LASTCHOOSEWORKBENCH IS '上次打开工作台标记数据';


CREATE TABLE tazookeeperaddressmg(
    ZKID VARCHAR(36) NOT NULL,
    ZKADDRESS VARCHAR(200),
    APPNAME VARCHAR(60),
    APPNAMESPACE VARCHAR(30),
    CONNECTFLAG VARCHAR(1),
    PRIMARY KEY (ZKID)
);

COMMENT ON TABLE tazookeeperaddressmg IS 'ZK注册中心管理';
COMMENT ON COLUMN tazookeeperaddressmg.ZKID IS '注册中心ID';
COMMENT ON COLUMN tazookeeperaddressmg.ZKADDRESS IS '注册中心地址';
COMMENT ON COLUMN tazookeeperaddressmg.APPNAME IS '应用名称';
COMMENT ON COLUMN tazookeeperaddressmg.APPNAMESPACE IS '命名空间';
COMMENT ON COLUMN tazookeeperaddressmg.CONNECTFLAG IS '连接标志';

CREATE TABLE tarunqianresource(
    RAQFILENAME VARCHAR(200) NOT NULL,
    PARENTRAQFILENAME VARCHAR(200),
    RAQNAME VARCHAR(200),
    PARENTRAQNAME VARCHAR(200),
    RAQTYPE VARCHAR(6),
    RAQFILE BLOB,
    UPLOADOR VARCHAR(50),
    UPLOADTIME DATETIME,
    SUBROW BIGINT,
    SUBCELL BIGINT,
    RAQDATASOURCE VARCHAR(50),
    RAQPARAM VARCHAR(500),
    ORGID VARCHAR(40)
);

COMMENT ON TABLE tarunqianresource IS '润乾报表模板';
COMMENT ON COLUMN tarunqianresource.RAQFILENAME IS '文件名/报表标识（RAQFILENAME）';
COMMENT ON COLUMN tarunqianresource.PARENTRAQFILENAME IS '父报表标识（PARENTRAQFILENAME）';
COMMENT ON COLUMN tarunqianresource.RAQNAME IS '报表名称（RAQNAME）';
COMMENT ON COLUMN tarunqianresource.PARENTRAQNAME IS '父报表名称（PARENTRAQNAME）';
COMMENT ON COLUMN tarunqianresource.RAQTYPE IS '报表类型（RAQTYPE）';
COMMENT ON COLUMN tarunqianresource.RAQFILE IS '资源文件（RAQFILE）';
COMMENT ON COLUMN tarunqianresource.UPLOADOR IS '上传人（UPLOADOR）';
COMMENT ON COLUMN tarunqianresource.UPLOADTIME IS '上传时间（UPLOADTIME）';
COMMENT ON COLUMN tarunqianresource.SUBROW IS '父报表位置行（SUBROW）';
COMMENT ON COLUMN tarunqianresource.SUBCELL IS '父报表位置列（SUBCELL）';
COMMENT ON COLUMN tarunqianresource.RAQDATASOURCE IS '数据源（RAQDATASOURCE）';
COMMENT ON COLUMN tarunqianresource.RAQPARAM IS '报表参数JSON格式STR（RAQPARAM）';
COMMENT ON COLUMN tarunqianresource.ORGID IS '部门编号(ORGID)';







CREATE OR REPLACE VIEW v_dict (name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks,
                               createtime, createuser, version, status, field01, field02, field03, field04, field05,
                               SYSTEM, newtype) as
select x0.name,
       x0.type,
       x0.label,
       x0.value,
       x0.parentvalue,
       x0.sort,
       x0.authority,
       x0.cssclass,
       x0.cssstyle,
       x0.remarks,
       x0.createtime,
       x0.createuser,
       x0.version,
       x0.status,
       x0.field01,
       x0.field02,
       x0.field03,
       x0.field04,
       x0.field05,
       x0.SYSTEM,
       x0.newtype
from tadict x0;


CREATE VIEW V_RESOURCECATEGORY(CATEGORYID, CATEGORYNAME, EFFECTIVE, CODE, CATEGORYCONTENT) AS
SELECT CATEGORYID, CATEGORYNAME, EFFECTIVE, CODE, CATEGORYCONTENT FROM TARESOURCECATEGORY;

CREATE VIEW V_CUSTOMRESOURCE(CUSTOMRESOURCEID, RESOURCENAME, PARENTID, CODE, RESOURCECONTENT, RESOURCECATEGORY, EFFECTIVE, ADDTIME, MODIFYTIME, SYSTEM) AS
SELECT CUSTOMRESOURCEID, RESOURCENAME, PARENTID, CODE, RESOURCECONTENT, RESOURCECATEGORY, EFFECTIVE, ADDTIME, MODIFYTIME, SYSTEM FROM TACUSTOMRESOURCE;


--  初始化人员表
INSERT INTO tauser(userid, loginid, password, passworddefaultnum, pwdlastmodifydate, islock, orderno, createuser, createtime, modifytime, destory, accountsource, effective, effectivetime, accounttype, userinfoid) VALUES ('1', 'developer', 'Sm3-n6a97kjMiB0IuzTVIQb/W5cW0iomo81euwCTDnTYx9M=', 0, sysdate, '0', 1, '1', sysdate, NULL, '0', NULL, '1', NULL, NULL, '1');
INSERT INTO tauser(userid, loginid, password, passworddefaultnum, pwdlastmodifydate, islock, orderno, createuser, createtime, modifytime, destory, accountsource, effective, effectivetime, accounttype, userinfoid) VALUES ('2', 'examiner', 'Sm3-n6a97kjMiB0IuzTVIQb/W5cW0iomo81euwCTDnTYx9M=', 0, sysdate, '0', 11, '1', sysdate, NULL, '0', NULL, '1', NULL, NULL, '2');
INSERT INTO tauser(userid, loginid, password, passworddefaultnum, pwdlastmodifydate, islock, orderno, createuser, createtime, modifytime, destory, accountsource, effective, effectivetime, accounttype, userinfoid) VALUES ('3', 'auditor', 'Sm3-n6a97kjMiB0IuzTVIQb/W5cW0iomo81euwCTDnTYx9M=', 0, sysdate, '0', 21, '1', sysdate, NULL, '0', NULL, '1', NULL, NULL, '3');

-- 默认人员表添加
INSERT INTO tauserinfo
(USERINFOID, orderno, NAME, SEX, IDCARDTYPE, IDCARDNO, MOBILE, CREATEUSER, CREATETIME, MODIFYTIME, DESTORY, EFFECTIVE, EFFECTIVETIME, JOBNUMBER, STATE, BIRTHPLACE, ADDRESS, ZIPCODE, EMAIL, PHONE, EDUCATION, GRADUATESCHOOL, WORKPLACE, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, FIELD06, FIELD07, FIELD08, FIELD09, FIELD10, SPELL)
VALUES('1', 1, '超级管理员', '1', '', NULL, '', '1', sysdate, NULL, '0', '1', NULL, NULL, NULL, NULL, '成都市/锦江区/久远银海', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO tauserinfo
(USERINFOID, orderno, NAME, SEX, IDCARDTYPE, IDCARDNO, MOBILE, CREATEUSER, CREATETIME, MODIFYTIME, DESTORY, EFFECTIVE, EFFECTIVETIME, JOBNUMBER, STATE, BIRTHPLACE, ADDRESS, ZIPCODE, EMAIL, PHONE, EDUCATION, GRADUATESCHOOL, WORKPLACE, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, FIELD06, FIELD07, FIELD08, FIELD09, FIELD10, SPELL)
VALUES('2', 1, '审核超级管理员', '1', '', NULL, '', '1', sysdate, NULL, '0', '1', NULL, NULL, NULL, NULL, '成都市/锦江区/久远银海', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO tauserinfo
(USERINFOID, orderno, NAME, SEX, IDCARDTYPE, IDCARDNO, MOBILE, CREATEUSER, CREATETIME, MODIFYTIME, DESTORY, EFFECTIVE, EFFECTIVETIME, JOBNUMBER, STATE, BIRTHPLACE, ADDRESS, ZIPCODE, EMAIL, PHONE, EDUCATION, GRADUATESCHOOL, WORKPLACE, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, FIELD06, FIELD07, FIELD08, FIELD09, FIELD10, SPELL)
VALUES('3', 1, '审计默认管理员', '1', '', NULL, '', '1', sysdate, NULL, '0', '1', NULL, NULL, NULL, NULL, '成都市/锦江区/久远银海', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);


--  初始化组织表
INSERT INTO taorg(orgid, orgname, spell, parentid, idpath, namepath, customno, orderno, orglevel, area, effective, orgtype, createuser, createtime, modifytime, orgmanager, orgcode, contacts, address, tel, destory, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10) VALUES ('fd811ab9c30440088df3e29ea784460a', '顶级组织', 'DJZZ', '0', '/fd811ab9c30440088df3e29ea784460a/', '顶级组织', '', 0, 0, '', '1', '01', '1', NULL, sysdate, '', '', '', '', '', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);


--  初始化角色
INSERT INTO tarole(roleid, rolename, isadmin, rolelevel, orgid, roletype, effective, effectivetime, createuser, createtime, roledesc, rolesign, subordinate) VALUES ('1',  '超级管理员',  NULL,  NULL,  'fd811ab9c30440088df3e29ea784460a',  '04',  '1',  NULL,  '1',  sysdate,  '超级管理员角色',  NULL,  NULL);
INSERT INTO tarole(roleid, rolename, isadmin, rolelevel, orgid, roletype, effective, effectivetime, createuser, createtime, roledesc, rolesign, subordinate) VALUES ('6908dc02a2af43a39dd8be7d9bcbbaa4',  '审核超级管理员',  NULL,  NULL,  'fd811ab9c30440088df3e29ea784460a',  '02',  '1',  NULL,  '1',  sysdate,  NULL,  NULL,  NULL);
INSERT INTO tarole(roleid, rolename, isadmin, rolelevel, orgid, roletype, effective, effectivetime, createuser, createtime, roledesc, rolesign, subordinate) VALUES ('c7a290c35198445dbaa5d45af0cbf007',  '审计默认管理员',  NULL,  NULL,  'fd811ab9c30440088df3e29ea784460a',  '05',  '1',  NULL,  '1',  sysdate,  NULL,  NULL,  NULL);


--  初始化用户组织表
INSERT INTO tauserorg(userid, orgid, isdirect) VALUES ('1', 'fd811ab9c30440088df3e29ea784460a', '1');
INSERT INTO tauserorg(userid, orgid, isdirect) VALUES ('2', 'fd811ab9c30440088df3e29ea784460a', '1');
INSERT INTO tauserorg(userid, orgid, isdirect) VALUES ('3', 'fd811ab9c30440088df3e29ea784460a', '1');


-- 初始化角色账户的关联关系
INSERT INTO taroleuser(roleid, defaultrole, createuser, createtime, objecttype, objectid) VALUES ('1', '0', '1', sysdate, '1', '1');
INSERT INTO taroleuser(roleid, defaultrole, createuser, createtime, objecttype, objectid) VALUES ('6908dc02a2af43a39dd8be7d9bcbbaa4', '0', '1', sysdate, '1', '2');
INSERT INTO taroleuser(roleid, defaultrole, createuser, createtime, objecttype, objectid) VALUES ('c7a290c35198445dbaa5d45af0cbf007', '0', '1', sysdate, '1', '3');


--  初始化码表
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作对象类型', 'OPOBJTYPE', '自定义资源', '08', NULL, 80, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('日志等级', 'LOGLEVEL', 'OFF', '0', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('角色类型', 'ROLETYPE', '管理角色', '04', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('角色标识', 'ROLESIGN', '稽核角色', '2', NULL, 2, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('角色标识', 'ROLESIGN', '业务角色', '1', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('元素在界面中的授权策略', 'ELEMENTAUTHORITYPOLICY', '不显示', '0', NULL, 31, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('元素在界面中的授权策略', 'ELEMENTAUTHORITYPOLICY', '数据可见不可编辑', '1', '', 21, '0', '', '', NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('元素在界面中的授权策略', 'ELEMENTAUTHORITYPOLICY', '数据可见可编辑', '2', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('账户类型', 'ACCOUNTTYPE', '经办管理账号', '1', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('账户类型', 'ACCOUNTTYPE', '自然人账号', '2', NULL, 2, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('界面元素授权策略', 'UIAUTHORITYPOLICY', '继承当前菜单权限', '0', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('界面元素授权策略', 'UIAUTHORITYPOLICY', '独立授权', '1', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('安全策略', 'SECURITYPOLICY', '无需登录可访问', '0', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('安全策略', 'SECURITYPOLICY', '登录均可访问', '1', NULL, 2, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('安全策略', 'SECURITYPOLICY', '授权可访问', '2', NULL, 3, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('有效标识', 'EFFECTIVE', '有效', '1', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('有效标识', 'EFFECTIVE', '无效', '0', NULL, 0, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('证件类型', 'IDCARDTYPE', '居民身份证(户口簿)', '0', '', 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('证件类型', 'IDCARDTYPE', '护照', '1', '', 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('证件类型', 'IDCARDTYPE', '军官证', '2', '', 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('证件类型', 'IDCARDTYPE', '其他', '3', '', 40, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('权限认证方式', 'AUTHMODE', '用户名密码', '01', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('权限认证方式', 'AUTHMODE', '指纹识别', '02', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('权限认证方式', 'AUTHMODE', '人脸识别', '03', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('权限认证方式', 'AUTHMODE', 'Key盘', '04', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '新增组织', '01', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '编辑组织', '02', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '禁用组织', '03', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '启用组织', '04', NULL, 40, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '删除组织', '05', NULL, 50, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '新增用户', '06', NULL, 60, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '编辑用户', '07', NULL, 70, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '禁用用户', '08', NULL, 80, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '启用用户', '09', NULL, 90, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '解锁账号', '10', NULL, 100, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '更新用户直属组织', '12', NULL, 110, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '重置密码', '13', NULL, 120, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '删除用户', '14', NULL, 130, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '新增角色', '15', NULL, 140, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '新增用户和角色关联关系', '16', NULL, 150, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '编辑角色', '17', NULL, 160, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '删除角色', '18', NULL, 170, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '角色禁用', '19', NULL, 180, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '启用角色', '20', NULL, 190, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '新增功能资源', '21', NULL, 200, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '人员导入', '68', NULL, 690, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '导入组织', '69', NULL, 700, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '资源菜单导入', '70', NULL, 710, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '导出人员', '71', NULL, 720, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '导出组织', '72', NULL, 730, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '导出资源菜单', '73', NULL, 740, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '彻底删除', '62', NULL, 750, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '回收站还原', '65', NULL, 760, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '加入回收站', '64', NULL, 680, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '编辑功能资源', '22', NULL, 210, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '删除功能资源', '23', NULL, 220, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '禁用功能资源', '24', NULL, 230, '0', NULL, NULL, NULL, sysdate, '1', '1', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '启用功能资源', '25', NULL, 240, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '更改组织权限（授予）', '26', NULL, 250, '0', NULL, NULL, NULL, sysdate, '1', '1', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '更改组织权限（回收）', '27', NULL, 260, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '更改使用权限（授权）', '28', NULL, 270, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '更改使用权限（回收）', '29', NULL, 280, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '更改授权权限（授权）', '30', NULL, 290, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '更改授权权限（回收）', '31', NULL, 300, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '更改再授权权限（授权）', '32', NULL, 310, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '更改再授权权限（回收）', '33', NULL, 320, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '删除角色人员关联关系', '34', NULL, 330, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '角色复制', '35', NULL, 340, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '权限复制', '36', NULL, 350, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '添加界面元素', '37', NULL, 360, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '删除界面元素', '38', NULL, 370, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '根据功能资源删除界面元素', '39', NULL, 380, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '编辑界面元素', '40', NULL, 390, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '根据功能资源编辑界面元素', '41', NULL, 400, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '新增行政区划', '51', NULL, 510, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '编辑行政区划', '52', NULL, 520, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '禁用行政区划', '53', NULL, 530, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '启用行政区划', '54', NULL, 540, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '删除行政区划', '55', NULL, 550, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '新增自定义组织', '56', NULL, 560, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '编辑自定义组织', '57', NULL, 570, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '禁用自定义组织', '58', NULL, 580, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '启用自定义组织', '59', NULL, 590, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '删除自定义组织', '60', NULL, 600, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '相似权限授权', '67', NULL, 670, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '修改密码', '43', NULL, 420, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作对象类型', 'OPOBJTYPE', '组织机构', '01', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作对象类型', 'OPOBJTYPE', '人员用户', '02', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作对象类型', 'OPOBJTYPE', '角色', '03', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作对象类型', 'OPOBJTYPE', '功能资源', '04', NULL, 40, '0', NULL, NULL, NULL, sysdate, '1', '1', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作对象类型', 'OPOBJTYPE', '界面元素', '05', NULL, 50, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作对象类型', 'OPOBJTYPE', '组织权限', '06', NULL, 60, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作对象类型', 'OPOBJTYPE', '权限信息', '07', NULL, 70, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作对象类型', 'OPOBJTYPE', '行政区划', '09', NULL, 90, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作对象类型', 'OPOBJTYPE', '自定义组织', '10', NULL, 100, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('日志等级', 'LOGLEVEL', 'ERROR', '1', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '1', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('日志等级', 'LOGLEVEL', 'WARN', '2', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '1', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('日志等级', 'LOGLEVEL', 'INFO', '3', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '1', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('日志等级', 'LOGLEVEL', 'DEBUG', '4', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '1', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('日志等级', 'LOGLEVEL', 'TRACE', '5', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '1', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('日志等级', 'LOGLEVEL', 'ALL', '6', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '1', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('安全认证级别', 'SECURIYTLEVEL', '无限制', '0', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('安全认证级别', 'SECURIYTLEVEL', '一级', '1', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('安全认证级别', 'SECURIYTLEVEL', '二级', '2', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('安全认证级别', 'SECURIYTLEVEL', '三级', '3', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('安全认证级别', 'SECURIYTLEVEL', '四级', '4', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('字典数据类型', 'DICTDATATYPE', '未同步', '0', '', 0, '0', '', '', NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('字典数据类型', 'DICTDATATYPE', '已同步', '2', '', 2, '0', '', '', NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('字典数据类型', 'DICTDATATYPE', '脏数据', '1', '', 1, '0', '', '', NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('是否', 'YESORNO', '是', '1', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('是否', 'YESORNO', '否', '0', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('组织类型', 'ORGTYPE', '机构', '01', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('组织类型', 'ORGTYPE', '部门', '02', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('组织类型', 'ORGTYPE', '组', '03', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('角色类型', 'ROLETYPE', '公有角色', '01', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('角色类型', 'ROLETYPE', '代理角色', '03', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('有无', 'HAVAORNOT', '无', '0', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('资源类型', 'RESOURCETYPE', '通用菜单', '0', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('资源类型', 'RESOURCETYPE', '管理菜单', '1', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('资源类型', 'RESOURCETYPE', '经办菜单', '2', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('有无', 'HAVAORNOT', '有', '1', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('状态', 'STATE', '启用', '1', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('状态', 'STATE', '禁用', '0', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('站点有效标识', 'SITEEFFECTIVE', '有效', '1', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('站点有效标识', 'SITEEFFECTIVE', '无效', '0', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('站点类型', 'SITETYPE', '网厅', '1', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('站点类型', 'SITETYPE', '业务系统', '2', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('站点类型', 'SITETYPE', '公众号', '3', NULL, 40, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('静态资源类型', 'FILERESOURCETYPE', '目录', '1', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('静态资源类型', 'FILERESOURCETYPE', '未知类型', '3', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('静态资源类型', 'FILERESOURCETYPE', '资源文件', '2', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '1', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('站点类型', 'SITETYPE', 'APP', '0', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('行政区划级别', 'AREALEVEL', '市', '1', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('行政区划级别', 'AREALEVEL', '区/县', '2', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('行政区划级别', 'AREALEVEL', '其他', '4', NULL, 50, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('行政区划级别', 'AREALEVEL', '省/自治区', '0', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('行政区划级别', 'AREALEVEL', '街道/镇', '3', NULL, 40, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('成功标识', 'ISSUCCESS', '失败', '0', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('成功标识', 'ISSUCCESS', '成功', '1', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('模版类型', 'TEMPLATETYPE', '表单模板', '1', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('模版类型', 'TEMPLATETYPE', '开发模板', '2', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('可用标识', 'AVAILABLE', '不可用', '0', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('可用标识', 'AVAILABLE', '可用', '1', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('布尔值', 'BL', 'true', 'true', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('布尔值', 'BL', 'false', 'false', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, system, newtype) VALUES ('功能配置', 'CONFIGFUNCTIONCATEGORY', '组织人员角色缓存配置', 'ta404.component.orguserauth.cache', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, system, newtype) VALUES ('功能配置', 'CONFIGFUNCTIONCATEGORY', '人员信息配置', 'ta404.component.userinfo', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, system, newtype) VALUES ('功能配置', 'CONFIGFUNCTIONCATEGORY', '审计配置', 'ta404.component.audit', NULL, 40, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, system, newtype) VALUES ('功能配置', 'CONFIGFUNCTIONCATEGORY', '审核配置', 'ta404.component.examine', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, system, newtype) VALUES ('系统配置', 'CONFIGSYSTEMCATEGORY', '字典配置', 'ta404.modules.dict', null, 10, '0', null, null, null, sysdate, '1', '0', '1', null, null, null, null, null, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, system, newtype) VALUES ('系统配置', 'CONFIGSYSTEMCATEGORY', '验证码配置', 'ta404.modules.captcha', null, 20, '0', null, null, null, sysdate, '1', '0', '1', null, null, null, null, null, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, system, newtype) VALUES ('系统配置', 'CONFIGSYSTEMCATEGORY', '防重复提交配置', 'ta404.limit', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, system, newtype) VALUES ('系统配置', 'CONFIGSYSTEMCATEGORY', '应用安全配置', 'ta404.component.security', NULL, 40, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, system, newtype) VALUES ('系统配置', 'CONFIGSYSTEMCATEGORY', 'web安全配置', 'ta404.modules.websecurity', NULL, 50, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, system, newtype) VALUES ('系统配置', 'CONFIGSYSTEMCATEGORY', 'token配置', 'ta404.component.security.token', NULL, 60, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, system, newtype) VALUES ('系统配置', 'CONFIGSYSTEMCATEGORY', 'cookie属性配置', 'ta404.component.cookie cookie', NULL, 70, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, system, newtype) VALUES ('系统配置', 'CONFIGSYSTEMCATEGORY', 'mvc配置', 'ta404.mvc', NULL, 80, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, system, newtype) VALUES ('系统配置', 'CONFIGSYSTEMCATEGORY', '门户配置', 'ta404.component.org.portal', NULL, 90, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('验证码类型', 'CHECKCODETYPE', 'simple', 'simple', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('验证码类型', 'CHECKCODETYPE', 'slide', 'slide', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('验证码类型', 'CHECKCODETYPE', 'blockPuzzle', 'blockPuzzle', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('日志输出类型', 'LOGOUTPUTTYPE', 'console', 'console', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('日志输出类型', 'LOGOUTPUTTYPE', 'file', 'file', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('日志输出类型', 'LOGOUTPUTTYPE', 'kafka', 'kafka', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('数据库密码加密类型', 'ENCODERTYPE', 'BCRYPT', 'BCRYPT', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('数据库密码加密类型', 'ENCODERTYPE', 'MD5', 'MD5', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('数据库密码加密类型', 'ENCODERTYPE', 'Sm2', 'Sm2', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('数据库密码加密类型', 'ENCODERTYPE', 'Sm3', 'Sm3', NULL, 40, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('数据库密码加密类型', 'ENCODERTYPE', 'Sm4', 'Sm4', NULL, 50, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('缓存模式', 'CACHEMODE', 'ehcache', 'ehcache', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('配置数据类型', 'CONFIGDATATYPE', '文本(TEXT)', '1', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('配置数据类型', 'CONFIGDATATYPE', '日期(DATE)', '2', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('配置数据类型', 'CONFIGDATATYPE', '选项(SELECTINPUT)', '3', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('配置数据类型', 'CONFIGDATATYPE', '数字(NUMBER)', '4', NULL, 40, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('配置数据类型', 'CONFIGDATATYPE', 'YAML(YML)', '5', NULL, 50, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('操作类型', 'OPTYPE', '通过审核', '50', NULL, 410, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('审核状态', 'EXAMINEISCHECK', '已审核', '1', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('审核状态', 'EXAMINEISCHECK', '拒审核', '2', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('性别', 'SEX', '男', '1', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('性别', 'SEX', '女', '2', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('性别', 'SEX', '无', '0', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '1', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('URL资源类型', 'URLTYPE', '命名空间', '0', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('URL资源类型', 'URLTYPE', '普通服务URL', '1', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('审计类型', 'AUDITTYPE', '登录日志', '1', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('审计类型', 'AUDITTYPE', '在线日志', '2', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('审计类型', 'AUDITTYPE', '操作日志', '4', NULL, 40, '0', NULL, NULL, NULL, sysdate, '1', '1', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('审计类型', 'AUDITTYPE', '异常日志', '3', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '1', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('审计类型', 'AUDITTYPE', '审核日志', '5', NULL, 50, '0', NULL, NULL, NULL, sysdate, '1', '1', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('审计类型', 'AUDITTYPE', '审计日志', '6', NULL, 60, '0', NULL, NULL, NULL, sysdate, '1', '1', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('审计类型', 'AUDITTYPE', '访问限制日志', '8', NULL, 80, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('审计类型', 'AUDITTYPE', '登录失败日志', '7', NULL, 70, '0', NULL, NULL, NULL, sysdate, '1', '1', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('日志存储类型', 'STORETYPE', '服务器', '1', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('日志存储类型', 'STORETYPE', '本地', '2', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('日志存储类型', 'STORETYPE', '文件服务器', '3', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('审计类型', 'AUDITTYPE', '登录环境分析日志', '9', NULL, 90, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('限制类型', 'ACCESSDENYTYPE', '未登录(TOKEN 失效)(TOKEN 失效)', '01', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('限制类型', 'ACCESSDENYTYPE', '无权限', '02', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('限制类型', 'ACCESSDENYTYPE', '跨域', '03', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('报表类型', 'RAQTYPE', '参数报表', '0', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('报表类型', 'RAQTYPE', '主报表', '1', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('报表类型', 'RAQTYPE', '子报表', '2', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('角色类型', 'ROLETYPE', '审核角色', '02', NULL, 11, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('角色类型', 'ROLETYPE', '审计角色', '05', NULL, 21, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('工作台组件类别', 'WORKTABLECOMPONENTTYPE', '普通组件', '0', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('工作台组件类别', 'WORKTABLECOMPONENTTYPE', '审核组件', '3', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('工作台组件类别', 'WORKTABLECOMPONENTTYPE', '审计组件', '4', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('限制类型', 'ACCESSDENYTYPE', '不允许的文件类型', '04', null, 40, '0', null, null, null, sysdate, '1', '0', '1', null, null, null, null, null, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('限制类型', 'ACCESSDENYTYPE', '不在IP白名单', '05', null, 50, '0', null, null, null, sysdate, '1', '0', '1', null, null, null, null, null, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('审核对象锁定', 'EXAMINELOCKEDOBJECT', '组织', '1', null, 10, '0', null, null, null, sysdate, '1', '0', '1', null, null, null, null, null, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('审核对象锁定', 'EXAMINELOCKEDOBJECT', '角色', '4', null, 40, '0', null, null, null, sysdate, '1', '0', '1', null, null, null, null, null, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('审核对象锁定', 'EXAMINELOCKEDOBJECT', '权限资源', '3', null, 30, '0', null, null, null, sysdate, '1', '1', '1', null, null, null, null, null, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('审核对象锁定', 'EXAMINELOCKEDOBJECT', '人员', '2', null, 20, '0', null, null, null, sysdate, '1', '1', '1', null, null, null, null, null, '1', '0');
-- 学历码表
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('学历', 'EDUCATION', '初中及以下', '1', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('学历', 'EDUCATION', '中专/中技', '2', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('学历', 'EDUCATION', '高中', '3', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('学历', 'EDUCATION', '大专', '4', NULL, 40, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('学历', 'EDUCATION', '本科', '5', NULL, 50, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('学历', 'EDUCATION', '硕士', '6', NULL, 60, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('学历', 'EDUCATION', '博士', '7', NULL, 70, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');

-- 新增账号来源、角色标签类型字典
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('标签类型', 'TAGTYPE', '账号', '001', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('标签类型', 'TAGTYPE', '组织', '002', NULL, 2, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('标签类型', 'TAGTYPE', '角色', '003', NULL, 3, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('账户来源', 'ACCOUNTSOURCE', '系统管理录入', '1', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('账户来源', 'ACCOUNTSOURCE', '账号申请审核', '2', NULL, 2, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('账户来源', 'ACCOUNTSOURCE', '注册通道注册', '3', NULL, 3, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');

-- 新增账号申请类型、状态字典
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('申请类型','APPLY_TYPE','账号申请','1',NULL,10,'0',NULL,NULL,NULL,sysdate,'1','0','1',NULL,NULL,NULL,NULL,NULL,'1','0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('申请状态','APPLY_STATUS','待提交','1',NULL,10,'0',NULL,NULL,NULL,sysdate,'1','0','1',NULL,NULL,NULL,NULL,NULL,'1','0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('申请状态','APPLY_STATUS','待审核','2',NULL,20,'0',NULL,NULL,NULL,sysdate,'1','0','1',NULL,NULL,NULL,NULL,NULL,'1','0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('申请状态','APPLY_STATUS','审核通过','3',NULL,30,'0',NULL,NULL,NULL,sysdate,'1','0','1',NULL,NULL,NULL,NULL,NULL,'1','0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('申请状态','APPLY_STATUS','审核不通过','4',NULL,40,'0',NULL,NULL,NULL,sysdate,'1','0','1',NULL,NULL,NULL,NULL,NULL,'1','0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('申请状态','APPLY_STATUS','作废','5',NULL,50,'0',NULL,NULL,NULL,sysdate,'1','0','1',NULL,NULL,NULL,NULL,NULL,'1','0');

-- 新增权限标识字典类型
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('权限标识类型', 'AUTHORITY', '默认', '0', NULL, 1, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');

-- 菜单打开方式码表
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('菜单打开方式','RESOURCEOPENMODE','工作页','1',NULL,10,'0',NULL,NULL,NULL,sysdate,'1','0','1',NULL,NULL,NULL,NULL,NULL,'1','0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('菜单打开方式','RESOURCEOPENMODE','浏览器页','2',NULL,20,'0',NULL,NULL,NULL,sysdate,'1','0','1',NULL,NULL,NULL,NULL,NULL,'1','0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, SYSTEM, newtype) VALUES ('菜单打开方式','RESOURCEOPENMODE','弹窗','3',NULL,30,'0',NULL,NULL,NULL,sysdate,'1','0','1',NULL,NULL,NULL,NULL,NULL,'1','0');

-- 初始化URL资源表
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e21705b74aa14db3a01388c30ec36c4c', '审计列表', 'audit/taAuditResService/queryAuditList', 'fe98234d9a0a4f2a9228743a58e86d54', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('76214a306e0e44f8b8f19eae5fa0dd1c', '异步查询自定义权限信息', 'domain/orguserauth/roleAgentRestService/queryCustomUsePermissionAsync', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0941e708ddac4155b7d4d1b6a1fc2e8e', '查询能分配权限的角色', 'domain/orguserauth/roleRestService/queryAuthRole', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e0d0c362e8f7483680c5daf3fc2bb987', '更新管理员自定义权限信息', 'domain/orguserauth/adminRoleRestService/changeCustomResourceUsePermission', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('90765bc3d2e74ae7b4adf1f7fa97a204', '查询账户信息', 'domain/orguserauth/TaUserManagementRestService/getUserByUserId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0c53f4294d9b40699e7fa440af6b7ad0', '更新自定义权限有效时间', 'domain/orguserauth/adminRoleRestService/updateCustomResourceUsePermissionEffectiveTime', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9fc1eaec4a364bada22396dc233faa63', '重置配置', 'tasysconfig/taSysConfigRestService/refreshSysConfig', '3154d229a4244fda90903cb56f7da445', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('46caf3a83bb34d248e86cb2b7d6e0ed8', '在线日志分析查询', 'logmg/loginLog/loginLogAnalysisRestService/online', '39683443f5e84c67913039e4c53133d1', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ca6ae64039b34b61ac33444db8586583', '登录历史查询', 'logmg/loginLog/loginLogAnalysisRestService/login', '39683443f5e84c67913039e4c53133d1', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8fe5b7e8c0564c1183964b3ef47de089', '删除人员对话', 'message/removeChat', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b309d59e4888483c9491f836a673edb2', '标记私信已读', 'message/readLetters', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('45fea7f87ddf4f419e5c1e5b7b356f2a', '查询用户通知', 'message/queryUserNotice', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5251eff95b944e96937d43afdd41823a', '查询组织下用户', 'message/queryOrgUser', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ea59a4b2d0c04678b6845dc2ff58e5f0', '查询一条配置数据', 'tasysconfig/taSysConfigRestService/selectOne', '3154d229a4244fda90903cb56f7da445', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('571beb27637e4a02a40b30b01eb977a1', '审核日志图', 'examine/taExamineRestService/examineChart', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1ba2429d2cf741eaa651d135cecfccfa', '批量拒绝审核', 'examine/taExamineRestService/batchRefusePass', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8f4c664e4eac467e823cca1dae037afa', '查询审核单条数据', 'examine/taExamineRestService/selectOne', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('195ea431cbda47599912e600593916d9', '批量审核通过', 'examine/taExamineRestService/batchExamineSomeone', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('44432ccd6aee42d5b8e7a5fa333335a6', '字典服务', 'codetable/getCode', null, '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0e78d360879a4bd38b3fa9c492dd106a', '分布式任务数据源', 'jobmg/elasticjob/jobDatasourceManagementRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a8897d46eef944efbfee9693dc0566b2', '查询数据源', 'jobmg/elasticjob/jobDatasourceManagementRestService/getJobDatasource', '0e78d360879a4bd38b3fa9c492dd106a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c16ef6c06f0a4ee8a47a8d853171a7ff', '新增数据源', 'jobmg/elasticjob/jobDatasourceManagementRestService/addJobDatasource', '0e78d360879a4bd38b3fa9c492dd106a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7b5dbd9796374fb4818ef6f992fb90cd', '删除数据源', 'jobmg/elasticjob/jobDatasourceManagementRestService/deleteJobDatasource', '0e78d360879a4bd38b3fa9c492dd106a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1f662d581613479f9cf7438aad814f6e', '获取作业执行历史轨迹', 'jobmg/elasticjob/jobDatasourceManagementRestService/getJobExecutionTrace', '0e78d360879a4bd38b3fa9c492dd106a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b32f17c028cc44ada9d2a8472e608b33', '获取作业历史状态', 'jobmg/elasticjob/jobDatasourceManagementRestService/getJobStatusTrace', '0e78d360879a4bd38b3fa9c492dd106a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('475f84c0bc3f4702a1e3d76de7e84f35', '功能审核', 'examine/taExamineRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c48caa74d4614c63892fbae774a6c113', '通过审核', 'examine/taExamineRestService/examineSomeone', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fc88cde084394fac9fabf7d39268df37', '拒绝审核', 'examine/taExamineRestService/refusePass', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7e13bb8bad344530abc0c997e6f29c6a', '分页审核列表', 'examine/taExamineRestService/queryExamine', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('15f7e09d42124995bb44c18ce7b625de', '审核日志接口', 'examine/taExamineRestService/queryExamineLog', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0181353f447e466ba81475d7c265aa89', '查看审核细节', 'examine/taExamineRestService/queryExamineDetail', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('39683443f5e84c67913039e4c53133d1', '登录日志分析', 'logmg/loginLog/loginLogAnalysisRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f90d3e743f6f4505b139b089eb05e5bc', '获取数据库时间', 'logmg/loginLog/loginLogAnalysisRestService/getSysTime', '39683443f5e84c67913039e4c53133d1', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('79c8a0452bb84d439ac3461d40f7df01', '查询所有在线人员信息', 'logmg/loginLog/loginLogAnalysisRestService/getOnlineInfo', '39683443f5e84c67913039e4c53133d1', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f588153009394645a497f2897ba79eb4', '查询所有人员登录历史信息', 'logmg/loginLog/loginLogAnalysisRestService/getLoginInfo', '39683443f5e84c67913039e4c53133d1', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0a5d58ba4cf54e4eae6d1157c3193e47', '查询在线时点信息', 'logmg/loginLog/loginLogAnalysisRestService/analysisOnlineStatInfo', '39683443f5e84c67913039e4c53133d1', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a03fe56632dd4e779b0196812344f86f', '分析登录客户端系统情况', 'logmg/loginLog/loginLogAnalysisRestService/analysisClientSystemInfo', '39683443f5e84c67913039e4c53133d1', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fedb18b63f474b63aeb93007c3d9c843', '分析登录客户端分辨率情况', 'logmg/loginLog/loginLogAnalysisRestService/analysisClientScreenInfo', '39683443f5e84c67913039e4c53133d1', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('40a218c972bb4dd7b3c3e71a265e2de1', '分析登录客户端分辨率情况', 'logmg/loginLog/loginLogAnalysisRestService/analysisClientBrowserInfo', '39683443f5e84c67913039e4c53133d1', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('880da059725c4b06a624a13d7e0a1d59', '运行环境详细查询', 'logmg/loginLog/loginLogAnalysisRestService/queryLoginEnvironmentDetail', '39683443f5e84c67913039e4c53133d1', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2a54d340e21b43238c5013ce0534c85a', '查询时点登录信息', 'logmg/loginLog/loginLogAnalysisRestService/analysisLoginStatInfo', '39683443f5e84c67913039e4c53133d1', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('821a8ca012154ba2a4c451918c99cfc0', '功能资源管理', 'domain/orguserauth/resourceManagementRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3910f6a32f034404a92b4278645702c8', '查询功能资源信息', 'domain/orguserauth/resourceManagementRestService/queryResourceByResourceID', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('40d60f8d1a7c440c821cb7813dcad84f', '更新功能资源', 'domain/orguserauth/resourceManagementRestService/updateResourceByResourceId', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('00c38936f3224585948b2f4076856748', '批量删除功能资源', 'domain/orguserauth/resourceManagementRestService/deleteResourceByResourceIds', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2a6fc98ead7e4606a565943e2b6cdcb7', '新增功能资源', 'domain/orguserauth/resourceManagementRestService/addResource', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('adbcb239a961413a91190a1a6e1a9687', '根据参数查询功能资源', 'domain/orguserauth/resourceManagementRestService/queryTaResourceByParameters', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fa4b47a3941e4fba9eff97249987e4d9', '查询接入系统列表', 'domain/orguserauth/resourceManagementRestService/queryAllAccessSystem', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('4753a58f0d1b4770996784b7f4ce15f9', '禁用功能资源', 'domain/orguserauth/resourceManagementRestService/disabledResourceByResourceIds', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('774535576eb142078ec9f589fa0166cf', '启用功能资源', 'domain/orguserauth/resourceManagementRestService/enabledResourceByResourceIds', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('83564254d96c4a0196d3397347737cf9', '删除行政区划数据', 'domain/orguserauth/areaAggregateRestService/deleteAreaById', '7859157a0cd94c89995b0dde9fc40bc6', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d2610e8b0c6144b0af4ea3a647d8649b', '根据条件查询行政区划数据', 'domain/orguserauth/areaAggregateRestService/queryAreaByCondition', '7859157a0cd94c89995b0dde9fc40bc6', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('33ebb96ce401430a9b630ea9bf7b19ce', '批量更新行政区划有效状态', 'domain/orguserauth/areaAggregateRestService/updateBatchEffectiveByAreaIdPath', '7859157a0cd94c89995b0dde9fc40bc6', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('16fa889f1e634f46aa1c19bef83bef5c', '查询行政区划数据', 'domain/orguserauth/areaAggregateRestService/queryAreaByAreaId', '7859157a0cd94c89995b0dde9fc40bc6', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('cbc0fbecfe4442d893d7681752d6e5eb', '批量删除行政区划数据', 'domain/orguserauth/areaAggregateRestService/deleteBatchAreaByAreaIds', '7859157a0cd94c89995b0dde9fc40bc6', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('10ce3706358e4127bb173427560244d8', '访问限制日志', 'logMg/accessLog/taAccessDenyLogRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('847d0576ff39421686a6094185ba93ef', '访问受限列表', 'logMg/accessLog/taAccessDenyLogRestService/queryAccessDenyLog', '10ce3706358e4127bb173427560244d8', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('71408b92effd4fcd99cd89f6b87bad57', '访问受限实时分析', 'logMg/accessLog/taAccessDenyLogRestService/analysisAccessDenyInfoInterval', '10ce3706358e4127bb173427560244d8', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('885e01d3494b44759c62b8b3568e6145', '标签管理', 'domain/orguserauth/tagRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9d67b3d6337a4e488a1c1f71a57cd1cb', '批量删除标签', 'domain/orguserauth/tagRestService/deleteBatchTag', '885e01d3494b44759c62b8b3568e6145', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f019482965424846a344c23762921af3', '查询配置类型', 'tasysconfig/taSysConfigRestService/queryConfigCategory', '3154d229a4244fda90903cb56f7da445', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f1ade00ea79541218e3c59ac0382cdbb', '查询标签', 'domain/orguserauth/tagRestService/queryTag', '885e01d3494b44759c62b8b3568e6145', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('32f19d310a1246eeb4804c947d1480b1', '新增标签', 'domain/orguserauth/tagRestService/addTag', '885e01d3494b44759c62b8b3568e6145', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c3f485a5d431487081927fea7bbdd6aa', '更新标签', 'domain/orguserauth/tagRestService/updateTag', '885e01d3494b44759c62b8b3568e6145', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c69bb98ae17f410eaa0862ff61c7c6dc', '批量更新标签状态', 'domain/orguserauth/tagRestService/updateBatchTagStatus', '885e01d3494b44759c62b8b3568e6145', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('626ddca64fac430d898bce60fe90fa54', '根据查询条件查询标签', 'domain/orguserauth/tagRestService/queryTagByCondition', '885e01d3494b44759c62b8b3568e6145', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('42f7383e22bd468e9aeec2e58d8dea5f', '审核角色权限管理', 'domain/orguserauth/examineRoleRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8b757b398c7145dca303538859299971', '增加人员角色关联关系', 'domain/orguserauth/examineRoleRestService/addBatchUserRole', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c53a34cb93bc4c75afc865231914ba9f', '删除角色人员关联关系', 'domain/orguserauth/examineRoleRestService/deleteBatchRoleUsers', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('bfc657fcb614420f94bb5bdf4c425510', '删除人员角色关联关系', 'domain/orguserauth/examineRoleRestService/deleteBatchUserRole', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('20700551c33b437c806b93bc92dc3c9d', '查询可分配的人员', 'domain/orguserauth/examineRoleRestService/queryUsersNoWraperByRoleId', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('cc844a5e23424fbfaf5d421b6b8658b0', '查询可分配的角色', 'domain/orguserauth/examineRoleRestService/queryNoWrapperRolesByUserId', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3e199ec988a64522a61baabcd852470d', '批量删除审核角色', 'domain/orguserauth/examineRoleRestService/deleteBatchRole', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('77f13730212647509751b06c8bb3dd27', '异步查询组织树', 'domain/orguserauth/examineRoleRestService/queryCurrentAdminRoleWrapOrgTree', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9f84c0c4803a40af9624e58197906f78', '查询已关联的角色', 'domain/orguserauth/examineRoleRestService/queryRolesByUserId', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2e34a4c83bba440abf1e65c47a1f64c1', '异步查询组织权限树', 'domain/orguserauth/examineRoleRestService/queryOrgAuthTreeByAsync', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('99b13867dfcc45d4bacd7c6917f729dc', '查询人员列表', 'domain/orguserauth/examineRoleRestService/queryUserByCondition', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('80de460d98984d96952d99e39a0a4fab', '新增组织权限', 'domain/orguserauth/examineRoleRestService/addOrgAuth', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d44c860bee374ab69fb9e5e60516c28d', '启用审核角色', 'domain/orguserauth/examineRoleRestService/enableExaminer', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('afa0c8bace664c238d31ddde03c17d7a', '查询审核角色', 'domain/orguserauth/examineRoleRestService/queryExaminer', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('76ac5cd69dbe4a0c8ab844510679957c', '查询角色的组织权限', 'domain/orguserauth/examineRoleRestService/queryOrgAuth', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('195c1e0a469547f8a37befdabea14a5e', '获取页面(数据+url)', 'review/getPage', '583c6b33a74c467aac7f67f364181450', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d709efa9492e41c985be89583d026227', '禁用审核角色', 'domain/orguserauth/examineRoleRestService/unableExaminer', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('72901308421145c88a469675d425afad', '移除组织权限', 'domain/orguserauth/examineRoleRestService/removeOrgScope', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('323d4e5765604460b96311bfaac9fc01', '更新审核角色', 'domain/orguserauth/examineRoleRestService/updateAdmin', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ed3c1f3b34334c758ddafb3235cc1a74', '新增审核角色', 'domain/orguserauth/examineRoleRestService/addAdminRole', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('28341963475b40e2982f13375a7bed27', '增加角色人员关联关系', 'domain/orguserauth/examineRoleRestService/addBatchRoleUsers', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fc810d0f2c9745da8ea370175ab3947a', '工作台管理', 'domain/orguserauth/workTableManageRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('18d5c9e17bbb496b80ccf6d587d2922e', '更新工作台组件的有效性', 'domain/orguserauth/workTableManageRestService/updateStatus', 'fc810d0f2c9745da8ea370175ab3947a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a19c95b79a694e2abad612a888d0baf7', '查询所有工作台资源', 'domain/orguserauth/workTableManageRestService/queryResource', 'fc810d0f2c9745da8ea370175ab3947a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ad0d709c33284be58a7232fa416d7bd3', '查询所有的角色', 'domain/orguserauth/workTableManageRestService/queryRole', 'fc810d0f2c9745da8ea370175ab3947a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5a41ab45c651433ba0a2623e0be9391c', '获取添加工作台组件的默认参数', 'domain/orguserauth/workTableManageRestService/queryDefaultValue', 'fc810d0f2c9745da8ea370175ab3947a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7acb6d48d51f47fc9f9491b1234c0428', '分布式任务ZK配置', 'jobmg/elasticjob/zookeeperAddressManagementRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2ec14b8ed9a04a9f85cc803d99ff0b18', '查询注册中心信息', 'jobmg/elasticjob/zookeeperAddressManagementRestService/getZookeeperRegistryCenter', '7acb6d48d51f47fc9f9491b1234c0428', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0d5c35c9b02340f2ad37397c76f36e4f', '新增注册中心', 'jobmg/elasticjob/zookeeperAddressManagementRestService/addZookeeperRegistryCenter', '7acb6d48d51f47fc9f9491b1234c0428', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d93202ddd70e4f1da4d4d70a37e1710c', '删除注册中心', 'jobmg/elasticjob/zookeeperAddressManagementRestService/deleteZookeeperRegistryCenter', '7acb6d48d51f47fc9f9491b1234c0428', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d0bc87eb27f3428686eef131314eb98e', '连接注册中心', 'jobmg/elasticjob/zookeeperAddressManagementRestService/connectZookeeperRegistryCenter', '7acb6d48d51f47fc9f9491b1234c0428', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('22392a8d036a4ad390cb754effb8183e', '分布式任务闲忙任务管理', 'jobmg/elasticjob/freeBusyJobManagerRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('67fe92e1f2f74a9ea1947dac0cf7447b', '查询非闲忙任务的任务', 'jobmg/elasticjob/freeBusyJobManagerRestService/getJobNameByZkId', '22392a8d036a4ad390cb754effb8183e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b1396cf02957493cb9dba1530863dbf6', '查询闲忙任务', 'jobmg/elasticjob/freeBusyJobManagerRestService/getFreeBusyJob', '22392a8d036a4ad390cb754effb8183e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3220d5b0942e432daf3c073ca360122b', '新增闲忙任务', 'jobmg/elasticjob/freeBusyJobManagerRestService/addFreeBusyJob', '22392a8d036a4ad390cb754effb8183e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6ad4d47081004f7598d4132264383268', '查询任务运行的服务器IP', 'jobmg/elasticjob/freeBusyJobManagerRestService/getServerIpsByJobName', '22392a8d036a4ad390cb754effb8183e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9d58b8eb62ea424287eb7562dfaaa837', '更新闲忙任务', 'jobmg/elasticjob/freeBusyJobManagerRestService/updateFreeBusyJob', '22392a8d036a4ad390cb754effb8183e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('58412f36f1c64e719d03d7bf5bba9ca6', '查询任务运行的所有服务器IP', 'jobmg/elasticjob/freeBusyJobManagerRestService/getAllServerIpsByJobName', '22392a8d036a4ad390cb754effb8183e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('795af47695b84dcd8c3bc5e6c4c8eccf', '删除闲忙任务', 'jobmg/elasticjob/freeBusyJobManagerRestService/deleteFreeBusyJob', '22392a8d036a4ad390cb754effb8183e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b88be2871149451a918264fd1ff468a3', '字典管理', 'dictmg/dictMgRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d1759dc661d640d2ad559ce4a6ece974', '保存字典类型', 'dictmg/dictMgRestService/saveType', 'b88be2871149451a918264fd1ff468a3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('4371f9f5b6f64f7490a11f5b0f46611b', '更新字典', 'dictmg/dictMgRestService/updateDict', 'b88be2871149451a918264fd1ff468a3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5b6eb06323f341ff90d735805d22e938', '批量删除字典', 'dictmg/dictMgRestService/deleteBatchDict', 'b88be2871149451a918264fd1ff468a3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6dc3fe02dcc549648eb2b5472c75b133', '根据类型禁用字典', 'dictmg/dictMgRestService/stopDictByType', 'b88be2871149451a918264fd1ff468a3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('159be0ed8e7e4107b085ab9d1020592d', '根据类型删除字典', 'dictmg/dictMgRestService/deleteDictByType', 'b88be2871149451a918264fd1ff468a3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('54cebfdf34c54e0191915d14f5e52ba0', '查询字典内容', 'dictmg/dictMgRestService/queryDictContent', 'b88be2871149451a918264fd1ff468a3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('45d43aee748d4ed2894b1f812f62360e', '查询字典信息', 'dictmg/dictMgRestService/queryDictInfo', 'b88be2871149451a918264fd1ff468a3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('dedea13dd9dd42b7aba71f1526ac7fe8', '批量启用字典', 'dictmg/dictMgRestService/startBatchDict', 'b88be2871149451a918264fd1ff468a3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d1471eda4afc45a4b915d2100fe18af9', '根据类型启用字典', 'dictmg/dictMgRestService/startDictByType', 'b88be2871149451a918264fd1ff468a3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7faf8e17e5e846e5b10e1fc42ecbe6c1', '根据字典类型查询字典', 'dictmg/dictMgRestService/queryDictByType', 'b88be2871149451a918264fd1ff468a3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2b9bb0e2179f43179d41f4fc7b22bf0e', '保存字典', 'dictmg/dictMgRestService/saveDict', 'b88be2871149451a918264fd1ff468a3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e9bc69a3bca94eccbe3e4286bf2863bf', '批量禁用字典', 'dictmg/dictMgRestService/stopBatchDict', 'b88be2871149451a918264fd1ff468a3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('08cd4ed593114680bfa8a9826936c3bd', '查询字典类型列表', 'dictmg/dictMgRestService/queryType', 'b88be2871149451a918264fd1ff468a3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a6e96e7ce7ca4b5f862e1df5b19d0a20', '查询字典权限标识列表', 'dictmg/dictMgRestService/queryAuthorityList', 'b88be2871149451a918264fd1ff468a3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('35a9fdc228314981b740a17ac3041863', '刷新字典缓存', 'dictmg/dictMgRestService/refreshDictByType', 'b88be2871149451a918264fd1ff468a3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('583c6b33a74c467aac7f67f364181450', '页面还原', 'review/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('bbcf4cf809324ffdbf9cbe95b649b652', '保存页面', 'review/savePage', '583c6b33a74c467aac7f67f364181450', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e53d524ae7884251b9551e5bd690ab20', '获取页面原Url', 'review/getPageUrl', '583c6b33a74c467aac7f67f364181450', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a7b3a24aa28d415891f67a7deae318d1', '获取已保存的页面', 'review/queryPageReview', '583c6b33a74c467aac7f67f364181450', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('af83691612354bb8933f8e3e9107df4e', '获取页面数据', 'review/getPageData', '583c6b33a74c467aac7f67f364181450', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('206adb9da5cf481f927097e4457a4381', '获取注册到zk的服务器信息', 'jobmg/elasticjob/serverOperateRestService/getServerInfo', '5855e399c9074b899850bb25ce1aaf76', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fe182cce9ff34370b62b0dacfc74679c', '失效注册到zk的服务器', 'jobmg/elasticjob/serverOperateRestService/disableServer', '5855e399c9074b899850bb25ce1aaf76', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1013f293306948acb3b0d600da8983ec', '生效注册到zk的服务器', 'jobmg/elasticjob/serverOperateRestService/enableServer', '5855e399c9074b899850bb25ce1aaf76', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('135ccd2cabaf4a6cac8c3ab5c4f49071', '终止注册到zk的服务器', 'jobmg/elasticjob/serverOperateRestService/shutdownServer', '5855e399c9074b899850bb25ce1aaf76', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e476368003a64ea49113e1f699ebf44a', '删除注册到zk的服务器', 'jobmg/elasticjob/serverOperateRestService/removeServer', '5855e399c9074b899850bb25ce1aaf76', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f5b7a9571ca640038ac5ae5f7fdfe242', '删除作业', 'jobmg/elasticjob/serverOperateRestService/removeServerJob', '5855e399c9074b899850bb25ce1aaf76', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7c08e928b3e146eea3729b1ac0222376', '使作业生效', 'jobmg/elasticjob/serverOperateRestService/enableServerJob', '5855e399c9074b899850bb25ce1aaf76', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3c72d73f18624014a3935c2d7f3e2a4c', '终止作业', 'jobmg/elasticjob/serverOperateRestService/shutdownServerJob', '5855e399c9074b899850bb25ce1aaf76', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a384ae7998c64307b98c3071747bab59', '作业失效', 'jobmg/elasticjob/serverOperateRestService/disabledServerJob', '5855e399c9074b899850bb25ce1aaf76', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9bf4ec13940041efbc48932c2a0ffe8e', '获取作业详情', 'jobmg/elasticjob/serverOperateRestService/getServerJobDetail', '5855e399c9074b899850bb25ce1aaf76', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b091f4af3eba4c5c946af8dacaf951b9', '自定义组织管理', 'domain/orguserauth/customOrgAggregateRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ca28dad434e54bf1a9b8f21e63f54b2b', '更新自定义组织', 'domain/orguserauth/customOrgAggregateRestService/updateCustomOrg', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('20800affa5404857aed051b36cec9717', '新增自定义组织', 'domain/orguserauth/customOrgAggregateRestService/addCustomOrg', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f617dc2ccb8a481b8e13b814f3447bf9', '根据条件查询自定义组织类别名称', 'domain/orguserauth/customOrgAggregateRestService/queryCustomOrgTypeNameByCondition', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7949676d97104f89bfd8fb9475977b07', '批量删除自定义组织', 'domain/orguserauth/customOrgAggregateRestService/deleteBatchCustomOrg', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9bc0b432d45c455392eb781a8f465f0d', '批量更新自定义组织有效状态', 'domain/orguserauth/customOrgAggregateRestService/updateBatchCustomOrgStatus', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('63daedc10cb9415da26c532ab9388ec8', '异常日志查询', 'audit/auditQueryRestService/exception', '90ef822f9e644448be5705a9994a263b', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d492053e1cd943a190d4e33c1d284890', '在线日志查询', 'audit/auditQueryRestService/online', '90ef822f9e644448be5705a9994a263b', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('986ee8c346f84f258658734af5ef494b', '登录日志查询', 'audit/auditQueryRestService/login', '90ef822f9e644448be5705a9994a263b', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a8806d67cc434c0382a170ef5ff5ded2', '审计日志查询', 'audit/auditQueryRestService/audit', '90ef822f9e644448be5705a9994a263b', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('611174d293ac4027b5f148fd677df4e3', '动态服务执行路径', 'rest/', '3cf89df828154febac4345a00ecd867b', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('93bb62a65b7b43eca20ea91b43d9ebe1', '查询下一级自定义组织', 'domain/orguserauth/customOrgAggregateRestService/queryCustomOrgByParentId', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2a06ec82aced450a9360f88083e7269d', '根据有效状态查询自定义组织', 'domain/orguserauth/customOrgAggregateRestService/queryCustomOrgByValidId', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9eb3d2c727814de5936c9b409de8835c', '根据自定义组织类别删除自定义组织', 'domain/orguserauth/customOrgAggregateRestService/deleteBatchCustomOrgTypeName', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1fc681db43df49caa2e1c0c03eca04a2', '新增润乾模板', 'tarunqianresource/taRunqianResourceRestService/addRunqianResource', '8545972435bb4abab8d2524fac510a04', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('dfc350b510624f79b12305e3fe7a8308', '修改润乾模板', 'tarunqianresource/taRunqianResourceRestService/editRunqianResource', '8545972435bb4abab8d2524fac510a04', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f70bb418d762405f8df422ddd05f4692', '下载润乾模板', 'tarunqianresource/taRunqianResourceRestService/downloadRunqianResource', '8545972435bb4abab8d2524fac510a04', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0ed2e37e987e47a9b995bab04dd00001', '删除润乾模板', 'tarunqianresource/taRunqianResourceRestService/delRunqianResource', '8545972435bb4abab8d2524fac510a04', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1f759706bf5548d3ae362cac46f9f39b', '查询润乾模板', 'tarunqianresource/taRunqianResourceRestService/queryRunqianResource', '8545972435bb4abab8d2524fac510a04', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('09961b00d2b34b748daa794515e3d8d6', '查询自定义组织类别', 'domain/orguserauth/customOrgAggregateRestService/queryCustomOrgTypeName', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2277168fd5a046829fe3405d49802e8e', 'URL资源管理', 'domain/orguserauth/url/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fa83267e5a984eb0804c042b8989bc57', '根据参数查询URL', 'domain/orguserauth/url/queryByParam', '2277168fd5a046829fe3405d49802e8e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('af7e74c0348947e691972de795c8f1aa', '查询URL信息', 'domain/orguserauth/url/queryUrl', '2277168fd5a046829fe3405d49802e8e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1a5380116d7b4068b8b149359e461e0f', '查询命名空间', 'domain/orguserauth/url/queryNamespace', '2277168fd5a046829fe3405d49802e8e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3381997ccaac4df68639008bd103a3c0', '更新URL', 'domain/orguserauth/url/updateUrl', '2277168fd5a046829fe3405d49802e8e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('13a1953a5f7e4527a25950b63c7bf3d4', '保存URL', 'domain/orguserauth/url/saveUrl', '2277168fd5a046829fe3405d49802e8e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2fd8146f001c4e7f979669bf36e4f1a2', '批量禁用URL', 'domain/orguserauth/url/disableBatchUrl', '2277168fd5a046829fe3405d49802e8e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2a23726d511b46c7b19a350706148868', '批量启用URL', 'domain/orguserauth/url/enableBatchUrl', '2277168fd5a046829fe3405d49802e8e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5e09b74efacf4862beda8a21bb8cbc9a', '批量删除URL', 'domain/orguserauth/url/deleteBatchUrl', '2277168fd5a046829fe3405d49802e8e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('83a48b7e93e94da78f768246aae6c84f', '根据命名空间查询URL', 'domain/orguserauth/url/queryUrlByNamespace', '2277168fd5a046829fe3405d49802e8e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ba186bfbfb1647ffbe7db23e89686e9f', '个人工作台服务', 'domain/orguserauth/workbenchRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3e09b57045fb4ea5b8b7c28b9714febf', '打开上次打开的工作台', 'domain/orguserauth/workbenchRestService/queryLastChooseWorkbench', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8949d73e53794d359ebcbf57f41359ee', '查询角色工作台信息根据角色ID', 'domain/orguserauth/workbenchRestService/queryUserWorkbenchByUserId', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f969b32f0f2640189237fb82687f2a71', '查询角色工作台通过角色ID', 'domain/orguserauth/workbenchRestService/queryRoleWorkbenchByRoleId', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('00a74d89066a47989407b208230b1709', '查询用户拥有权限的工作台组件', 'domain/orguserauth/workbenchRestService/queryResourceEffectiveByUserId', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('38a33deff4e044b999ca4bde56a53ea8', '查询角色拥有权限的工作台组件', 'domain/orguserauth/workbenchRestService/queryResourceEffectiveByRoleId', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d0e7351f05ce4b58a6fca29a19c70847', '保存角色工作台信息', 'domain/orguserauth/workbenchRestService/saveRoleWorkbench', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('417153883b114bc796c78f4420b14bb9', '保存上次打开的工作台信息标志', 'domain/orguserauth/workbenchRestService/saveLastChooseWorkbenchData', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('42bc8aea3378498e81c53334b06050ac', '保存用户工作台信息', 'domain/orguserauth/workbenchRestService/saveUserWorkbench', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('518fb0e03af64326ae3f6d1ea6da1fad', '查询用户拥有的角色', 'domain/orguserauth/workbenchRestService/queryRoleListByUserId', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3154d229a4244fda90903cb56f7da445', '动态配置管理', 'tasysconfig/taSysConfigRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('658ea1202bc843e99e0201aa5d079b00', '配置列表', 'tasysconfig/taSysConfigRestService/querySysConfig', '3154d229a4244fda90903cb56f7da445', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f91c291fd2f741f5b2eb52d3f882c153', '添加配置', 'tasysconfig/taSysConfigRestService/addSysConfig', '3154d229a4244fda90903cb56f7da445', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6426f3aad7c648f68377d1e346c02624', '更新配置', 'tasysconfig/taSysConfigRestService/updateSysConfig', '3154d229a4244fda90903cb56f7da445', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('15de371347e94563b28214db9cf37c02', '删除配置', 'tasysconfig/taSysConfigRestService/deleteSysConfig', '3154d229a4244fda90903cb56f7da445', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('aa60a746c0a24640a06dea49a4d42572', '组织管理', 'domain/orguserauth/orgRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a87e7b2776214a76ba31151c6c5cbbe5', '异步获取组织树', 'domain/orguserauth/orgRestService/getOrgByAsync', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a2d72ab0507241d19290cbb6fee6817e', '查询账户上次登录信息', 'domain/orguserauth/workbenchRestService/queryCurrentUserLastLoginLog', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('cbcdd31ffb6c40499bb1af744851a783', '管理员角色权限管理', 'domain/orguserauth/adminRoleRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e83bb87d573246e5b6c2ed534c2cd775', '新增管理员角色', 'domain/orguserauth/adminRoleRestService/addAdminRole', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e14986416987402c9283b0dfabacb379', '批量删除角色', 'domain/orguserauth/adminRoleRestService/deleteBatchRole', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e62f249ce1af41b7bb286d0e745d1471', '保存组织管理范围信息', 'domain/orguserauth/adminRoleRestService/saveOrgScope', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('877de3c0984e456ea1bbf52541a1d0f4', '更新角色信息', 'domain/orguserauth/adminRoleRestService/updateAdmin', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8ff682c3168a4b1295ed1efd32467321', '移除组织管理范围', 'domain/orguserauth/adminRoleRestService/removeOrgScope', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a911f4d576124857a80ddc37873d90e8', '批量新增账户角色关联关系', 'domain/orguserauth/adminRoleRestService/addBatchUserRole', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('37e4e66df7de4a87b14a19e8c4f44ef6', '查询账户列表', 'domain/orguserauth/adminRoleRestService/queryUserByCondition', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b05f5e9f2bb048d9adf335ab62a78cfd', '查询下级功能资源再授权信息', 'domain/orguserauth/adminRoleRestService/queryChildResourceAuthorityByRoleId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3efc328fe5fa40e38ff29b7f97deb709', '异步查询自定义资源权限再授权信息', 'domain/orguserauth/adminRoleRestService/queryChildCustomResourceAuthorityByRoleId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0cd7696d40ad49ad91f6ce91911fa2aa', '异步查询下级自定义资源', 'domain/orguserauth/adminRoleRestService/queryChildCustomResourceAsync', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('cab374d4f2af4cadaf487532c29df3ff', '批量更新权限有效使用时间', 'domain/orguserauth/adminRoleRestService/updateBatchUsePermissionEffectiveTime', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1ce091c3b3604be2932c25df07a642cd', '查询角色下的关联账户', 'domain/orguserauth/adminRoleRestService/queryUsersByRoleId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('20232206695a4aaa851e5d30a08ce295', '更新角色有效状态', 'domain/orguserauth/adminRoleRestService/updateRoleEffectiveByRoleId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2cb89bd084f74184a15e8313ad5b5647', '批量删除角色账户', 'domain/orguserauth/adminRoleRestService/deleteBatchRoleUsers', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('93acb3693a4c4dc8a47c4d0240f00890', '查询再授权顶级资源', 'domain/orguserauth/adminRoleRestService/queryRootResourceAuthority', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('697ec7337fc44943872e5fd47a6efdea', '查询下级资源权限', 'domain/orguserauth/adminRoleRestService/queryChildResourceByRoleId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('59bc6ad7451f47e5929a42d98986be98', '查询组织树', 'domain/orguserauth/adminRoleRestService/queryCurrentAdminRoleWrapOrgTree', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7ef0b9f372fd44a69ef79cdb968ec994', '查询可分配的账户列表', 'domain/orguserauth/adminRoleRestService/queryUsersNoWraperByRoleId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('60509043f699448ab9f5812e2333a11d', '查询角色列表', 'domain/orguserauth/adminRoleRestService/queryRolesByOrgId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9fcef2129d1c47e58631bf196a003d3b', '查询顶级自定义资源再授权信息', 'domain/orguserauth/adminRoleRestService/queryRootCustomResourceAuthority', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('90ef822f9e644448be5705a9994a263b', '审计查询', 'audit/auditQueryRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('41f4b754edd14be79ee108d40c061a8a', '批量新增角色账户关联关系', 'domain/orguserauth/adminRoleRestService/addBatchRoleUsers', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8d54ec9a7cbb4f1a9c3f4c54e7d341c8', '查询管理员角色列表', 'domain/orguserauth/adminRoleRestService/queryAdminRoleByOrgId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c59aaa9e3f1f4dcb961a90be46ea9b72', '删除角色账户关联关系', 'domain/orguserauth/adminRoleRestService/deleteRoleUserByKey', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fcf51d71136d4c2897b736f11af9266f', '查询顶级资源', 'domain/orguserauth/adminRoleRestService/queryRootResource', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('213caa8621494ce98a55bef675dd39f2', '查询账户的角色列表', 'domain/orguserauth/adminRoleRestService/queryRolesByUserId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8cb4565415ce47bba1d7be36cbabf40e', '批量删除账户角色关联关系', 'domain/orguserauth/adminRoleRestService/deleteBatchUserRole', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e0338aeb38164edb893d21c9af0bee43', '更新角色再授权权限', 'domain/orguserauth/adminRoleRestService/changeResourceAuthority', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('77f6d7ab67654023a997ba31bcef5262', '更新角色使用权限', 'domain/orguserauth/adminRoleRestService/changeResourceUsePermission', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('22d46236ba834a1f93fe128dd9861f6b', '选择组织管理范围', 'domain/orguserauth/adminRoleRestService/selectPermissionOrgScope', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('bbaef041a14c49f8963f1b6589a28d69', '更新自定义资源再授权', 'domain/orguserauth/adminRoleRestService/changeCustomResourceAuthority', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9cf0537f574d472fb2df4211a2e83d88', '更新使用权限有效时间', 'domain/orguserauth/adminRoleRestService/updateUsePermissionEffectiveTime', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b1fd82d7707645869a4088703dd260f1', '查询可分配的角色列表', 'domain/orguserauth/adminRoleRestService/queryNoWrapperRolesByUserId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9466add8d6e3449aab05fbbf0bda43cc', '批量更新自定义资源权限的有效时间', 'domain/orguserauth/adminRoleRestService/updateBatchCustomResourceUsePermissionEffectiveTime', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('03c3c8302674456f989739369d35fedc', '选择组织管理范围', 'domain/orguserauth/adminRoleRestService/selectOrgScope', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1e334b5580fb40588b41833ace89ece0', '禁用动态服务', 'dynamic/rest/disable', '47488be533b54ae99f0418ba3962e8b9', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('96d86e0b87264ee29424151636d50667', '启用动态服务', 'dynamic/rest/enable', '47488be533b54ae99f0418ba3962e8b9', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('84dc082ef9a94cb6a70955ded2229848', '根据服务ID获取信息', 'dynamic/rest/getByRestId', '47488be533b54ae99f0418ba3962e8b9', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a196f610e78645488b830ca21d5d73d2', '更新动态服务信息', 'dynamic/rest/update', '47488be533b54ae99f0418ba3962e8b9', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6cb53bacf6e84b2d89eb732cf29b683d', '新增动态服务', 'dynamic/rest/add', '47488be533b54ae99f0418ba3962e8b9', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('49a5ee26819f4dc9934ff23b905b249f', '查询动态服务列表', 'dynamic/rest/queryList', '47488be533b54ae99f0418ba3962e8b9', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('404773da30b843a4b70da215f73a9384', '删除动态服务', 'dynamic/rest/delete', '47488be533b54ae99f0418ba3962e8b9', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('feeec1113f5d49b2bb19eca44702b035', '查询数据源列表', 'dynamic/rest/queryDsList', '47488be533b54ae99f0418ba3962e8b9', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fb72be8cb70f40caacc63510afc9e8b3', '审计导出', 'audit/auditExportRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('72e8e1bdf40d4350b8addd7132952c5d', '在线日志导出', 'audit/auditExportRestService/online', 'fb72be8cb70f40caacc63510afc9e8b3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('993e777e680341cb86fe70437ca1150f', '权限限制日志导出', 'audit/auditExportRestService/accessDeny', 'fb72be8cb70f40caacc63510afc9e8b3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8a19872cf40e4078922ae712924102f7', '登录环境分析导出', 'audit/auditExportRestService/loginLogAnalysis', 'fb72be8cb70f40caacc63510afc9e8b3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0b0456f381374b278c804a848bceebcc', '操作日志导出', 'audit/auditExportRestService/orgOp', 'fb72be8cb70f40caacc63510afc9e8b3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('909e509a59ec430d9eb239d3265f03f5', '登录失败日志导出', 'audit/auditExportRestService/loginFail', 'fb72be8cb70f40caacc63510afc9e8b3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ff98da127f154939a0fdef59ec22f982', '系统异常日志导出', 'audit/auditExportRestService/exception', 'fb72be8cb70f40caacc63510afc9e8b3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7abfb7bd84994c0db08eb9976adfd7d8', '登录日志导出', 'audit/auditExportRestService/login', 'fb72be8cb70f40caacc63510afc9e8b3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('dec7f275ce6242b0a591ccd371474db7', '主页服务', 'indexRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('653ec11f00e8473fbad26ea69098e047', '查询当前人员信息', 'indexRestService/getCurUserAccount', 'dec7f275ce6242b0a591ccd371474db7', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('aa56d8e0833b4fffad76945a4133f4d5', '修改密码', 'indexRestService/changePassword', 'dec7f275ce6242b0a591ccd371474db7', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8c965431a61145fb98f7fa67b7271f76', '默认打开页面请求 用于判断是否登录', 'indexRestService/defaultOpen', 'dec7f275ce6242b0a591ccd371474db7', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3cf89df828154febac4345a00ecd867b', '动态服务执行服务', 'rest/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fc45635549b5487c87d0287490a79b5f', '角色权限管理', 'domain/orguserauth/roleRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e2273cbc1ffb47afb24c2c7ec24b3633', '批量删除角色', 'domain/orguserauth/roleRestService/deleteBatchRole', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('624bde4ff89e4630b220f9808b5f07e2', '批量新增账户角色关联关系', 'domain/orguserauth/roleRestService/addBatchUserRole', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a14c3e447ba044aaa5275e85ee026f0d', '查询账户列表', 'domain/orguserauth/roleRestService/queryUserByCondition', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('87566f8084c14e70a76f622abef88ee9', '异常日志图', 'logmg/exceptionlog/serverExceptionLogRestService/exceptionChart', 'f2e756c387524382a6fa4d9f3db9abf7', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f08165aeb751488cb3c42d8679d92ad1', '审计列表查询', 'audit/taAuditResService/audit', 'fe98234d9a0a4f2a9228743a58e86d54', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d2e57270fb64454b937915cead646bf5', '审计日志图', 'audit/taAuditResService/auditChart', 'fe98234d9a0a4f2a9228743a58e86d54', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2b147317777f4f728c659abfea82db57', '操作日志图', 'org/sysmg/orgOpLogRestService/opLogChart', 'badc42944eb74d38a3ce254536e997c1', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6d73b1f0ab7742e4b21caef223799261', '查询人员通知', 'queryUserNotice', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('95d6fa234ee848059a815b9c14287737', '根据日志id查询详细异常日志', 'logmg/exceptionlog/serverExceptionLogRestService/getDetalExceptionLog', 'f2e756c387524382a6fa4d9f3db9abf7', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2d69486d5fdc4336be2411e125a561c0', '根据时间查询异常日志', 'logmg/exceptionlog/serverExceptionLogRestService/getServerExceptionLog', 'f2e756c387524382a6fa4d9f3db9abf7', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('badc42944eb74d38a3ce254536e997c1', '组织人员操作日志', 'org/sysmg/orgOpLogRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('26010f3c6ec04bf59680ec7f11109e54', '操作日志列表', 'org/sysmg/orgOpLogRestService/getOrgOpLog', 'badc42944eb74d38a3ce254536e997c1', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('366d45447b244c2b8f3a6cefec338ae0', '获取操作日志操作内容', 'org/sysmg/orgOpLogRestService/getChangeContent', 'badc42944eb74d38a3ce254536e997c1', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fe98234d9a0a4f2a9228743a58e86d54', '审计日志', 'audit/taAuditResService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('31d3fcf575694d36b19d79488d10a719', '删除自定义资源使用权限', 'domain/orguserauth/roleRestService/deleteCustomResourceUsePermission', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('55f139c1352846f185d1115d6d38bb2b', '查询当前账户的组织树', 'domain/orguserauth/roleRestService/queryCurrentAdminRoleWrapeOrgTree', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8786b8c546af4a049e5c166872fb69fb', '批量更新使用权限有效期', 'domain/orguserauth/roleRestService/updateBatchUsePermissionEffectiveTime', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('598b36d8554649c19f7e88e087347c7e', '批量更新自定义资源权限有效期', 'domain/orguserauth/roleRestService/updateCustomResourceUsePermissionEffectiveTime', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('74a7d5a9739841809c3b0db1079a4ba8', '回收权限', 'domain/orguserauth/roleRestService/deleteBatchUsePermissionByMoreRole', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('081da1f5cc5a41fbbdb89e5423e2908f', '查询角色下的关联账户', 'domain/orguserauth/roleRestService/queryUsersByRoleId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('98396599f9e1494e9e99bca94de11a94', '更新角色有效状态', 'domain/orguserauth/roleRestService/updateRoleEffectiveByRoleId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('138ecb6ff95e4b58991a0f2f5d1deb3b', '批量删除角色账户关联关系', 'domain/orguserauth/roleRestService/deleteBatchRoleUser', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('19b678a580b94d5d9e38d8fa1ab32124', '查询可分配的账户列表', 'domain/orguserauth/roleRestService/queryUsersNoWraperByRoleId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('bbc17b9c19374ae0b050f77da4e23636', '查询角色列表', 'domain/orguserauth/roleRestService/queryRolesByOrgId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9a749a3305844739a424feeb0962f0e9', '查询角色的授权', 'domain/orguserauth/roleRestService/queryRePermissionByRoleId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9e367251a5fd4c658648b2b0bc177b03', '查询账户关联的角色', 'domain/orguserauth/roleRestService/queryRolesByUserId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c3aac3c6d27844aaa36831f0b05199a9', '批量删除账户角色关联关系', 'domain/orguserauth/roleRestService/deleteBatchUserRole', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5165c4afb9a546f69555b139f194ba36', '更新使用权限使用时间', 'domain/orguserauth/roleRestService/updateUsePermissionEffectiveTime', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('01fa335e75e8405b9161c14be68bdfba', '查询账户棵关联的角色列表', 'domain/orguserauth/roleRestService/queryNoWrapperRolesByUserId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('501b8e57bfac491eab3ea302bd0d1577', '批量授权', 'domain/orguserauth/roleRestService/addBatchUsePermissionByMoreRole', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0f450ebdc9de4e8897402109ee1faa6d', '更新角色信息', 'domain/orguserauth/roleRestService/updateRoleByRoleId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ce3009ef4f61445bb2ba1fe5a747e8be', '查询角色的功能资源使用权限', 'domain/orguserauth/roleRestService/queryUsePermissionByRoleId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c335de0f745049439eea4ed4ef5e8220', '查询角色的自定义资源使用权限', 'domain/orguserauth/roleRestService/queryCustomUsePermissionByRoleId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fc5a759500054f00a5016f68ba687ab2', '新增自定义使用权限', 'domain/orguserauth/roleRestService/addCustomResourceUsePermission', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1186646d85324d318e02899ca0f9508b', '更新Rest权限信息', 'domain/orguserauth/roleRestService/changeRestAuthority', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('155d58691b514a79824a95968dda38e6', '查询授权权限', 'domain/orguserauth/roleRestService/queryRePermission', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c4a5ece4fa5b414e98958972572c88b9', '异步加载自定义权限', 'domain/orguserauth/roleRestService/queryCustomRePermissionAsync', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('690b9acefd6e45f6bc5214646bb7d434', '删除使用权限', 'domain/orguserauth/roleRestService/deleteUsePermission', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('832d3322479949d8a36da9458fa3bcbb', '通过角色Id查询授权权限信息', 'domain/orguserauth/roleRestService/queryCustomRePermissionByRoleId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e65ee0ad6e4e4e29a37d8d1f6d952ede', '查询自定义资源授权权限', 'domain/orguserauth/roleRestService/queryCustomRePermission', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a5ff0d74a0084769b8e767cc51491c75', '更新自定义资源权限的使用有效时间', 'domain/orguserauth/roleRestService/updateBatchCustomResourceUsePermissionEffectiveTime', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7f5017d989ad4f1e828bced19467ee2a', '新增使用权限', 'domain/orguserauth/roleRestService/addUsePermission', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c65e391b3372485aa45fddd997cab064', '新增角色', 'domain/orguserauth/roleRestService/addRole', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d3a3b173b8c04b5a9cef0e82e2920310', '权限复制', 'domain/orguserauth/roleRestService/copyResource', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9a520b63f24c437c8861080a5526f23b', '角色复制', 'domain/orguserauth/roleRestService/copyRole', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('43900d1b828d4c8b950612fcae10a786', '登录服务', 'loginRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('52c52f7e65c84c6da2c1e7da75824182', '获取配置信息', 'loginRestService/getConfig', '43900d1b828d4c8b950612fcae10a786', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ed2bf752425d4ac580d73f99615990ab', '登录页修改密码', 'loginRestService/changePassword', '43900d1b828d4c8b950612fcae10a786', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5f77fb7579294f02908bde7aaec43ece', '账户检查', 'loginRestService/checkUser', '43900d1b828d4c8b950612fcae10a786', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c4360fdef9ad46278a677508ee8b290a', '权限代理', 'domain/orguserauth/roleAgentRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('16e2788f54794c1d9fda4036b0ebe85f', '异步查询组织', 'domain/orguserauth/roleAgentRestService/getOrgByAsync', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c6428dc1dc144d4c8efb9b0e44dc17d5', '修改代理角色有效期', 'domain/orguserauth/roleAgentRestService/updateAgentRoleAuthority', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5b43aa7145734290803796c1b9070d49', '查询当前登录管理员能管理的代理角色列表', 'domain/orguserauth/roleAgentRestService/queryAllAgentRole', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6ec810ccee18431bb058d6440c4476f7', '根据组织id查询当前管理员所能管理的代理人员', 'domain/orguserauth/roleAgentRestService/queryReAgentUsersByOrgId', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1272585f9ed944eab717939300cadbb8', '检查电话号码', 'loginRestService/checkMobile', '43900d1b828d4c8b950612fcae10a786', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3a0b491b01674d9fab78498550320cd0', '根据userId获取当前账户可授权的自定义资源权限', 'domain/orguserauth/roleAgentRestService/queryCustomUsePermissionByUserId', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b7ea5b5a2bdc462aa14e8aca7961d61f', '新增代理角色', 'domain/orguserauth/roleAgentRestService/addAgent', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('aef954306f5d4c6d9b5e507aa3f175c6', '首页功能资源服务', 'domain/orguserauth/menuAction/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9d4b47c0490640008c31e892314520fc', '查询功能资源资源列表', 'domain/orguserauth/menuAction/queryRootChildrenMenus', 'aef954306f5d4c6d9b5e507aa3f175c6', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('58606a643d29437c9afcd69e8dd6482e', '接入系统管理', 'domain/orguserauth/accessSystemManagementRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6b844095d25d44daba36670ed5d31bd7', '查询有效的接入系统列表', 'domain/orguserauth/accessSystemManagementRestService/queryEffectiveAccessSystem', '58606a643d29437c9afcd69e8dd6482e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3bc7226a89454115b2c09d10aff96749', '批量删除接入系统', 'domain/orguserauth/accessSystemManagementRestService/deleteBatchTaAccessSystemById', '58606a643d29437c9afcd69e8dd6482e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c9b473e0bd044a579aef3a73d4ffc3cf', '新增接入系统', 'domain/orguserauth/accessSystemManagementRestService/addTaAccessSystem', '58606a643d29437c9afcd69e8dd6482e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3974a2411df2404ab1e0afa08653da95', '删除接入系统', 'domain/orguserauth/accessSystemManagementRestService/deleteTaAccessSystemById', '58606a643d29437c9afcd69e8dd6482e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f9de9a6d93e94f31ac446d213c5782fa', '更新接入系统列表', 'domain/orguserauth/accessSystemManagementRestService/updateTaAccessSystemById', '58606a643d29437c9afcd69e8dd6482e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('807ef17eaa8446188acbf92929171c56', '接入系统列表', 'domain/orguserauth/accessSystemManagementRestService/queryAccessSystemByParam', '58606a643d29437c9afcd69e8dd6482e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2c3c1591444842e1aa1588a0aab998a8', '相似权限', 'domain/orguserauth/similarAuthorityRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8cea2eccc40c4c418e43f4321ee970c3', '根据资源Ids查找所有拥有这些资源使用权的角色', 'domain/orguserauth/similarAuthorityRestService/queryUsePermissionRoleByResourceId', '2c3c1591444842e1aa1588a0aab998a8', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('83ca67228517485fba1f73bffc17b8a6', '查询当前角色可授权资源', 'domain/orguserauth/similarAuthorityRestService/queryCurrentUserAllRePermissionResource', '2c3c1591444842e1aa1588a0aab998a8', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('83a2c0425f3d469d92f24e7043a5d0f7', '查询当前角色可授权资源-小异步', 'domain/orguserauth/similarAuthorityRestService/queryCurrentUserSomeRePermission', '2c3c1591444842e1aa1588a0aab998a8', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('09ec9781ec834b079889bcf60fcd7388', '批量授予相似权限', 'domain/orguserauth/similarAuthorityRestService/addBatchSimilarAuthority', '2c3c1591444842e1aa1588a0aab998a8', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0bd90473a1b64ae1ab705302bb6881fc', '分布式任务之任务操作', 'jobmg/elasticjob/jobOperateRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b20b7169663b49d6b548a34e0d128b25', '清除已完成作业缓存', 'jobmg/elasticjob/jobOperateRestService/clearCache', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8d9b9595a4f84d28b5cad358335a37f7', '使作业生效', 'jobmg/elasticjob/jobOperateRestService/enableJob', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6e135682e38140a3bc423ff6406119d3', '终止作业', 'jobmg/elasticjob/jobOperateRestService/shutdownJob', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('80ee0be75f024dee80412a61c3284256', '使分片项生效', 'jobmg/elasticjob/jobOperateRestService/effectSharding', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d105c3c113bf4188afd27fe2a2347a08', '获取作业进度', 'jobmg/elasticjob/jobOperateRestService/getJobProgress', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('302d500716e34c3490523c3093201174', '删除作业', 'jobmg/elasticjob/jobOperateRestService/removeJob', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('4b9d98dd01dd479d942b7d9fee2b8eae', '刷新作业进度', 'jobmg/elasticjob/jobOperateRestService/refreshProgress', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('db1104eba4874533b3042af4651e9c75', '触发作业', 'jobmg/elasticjob/jobOperateRestService/triggerJob', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('97615539a3fe4dceb66a01ddaa5b4b4b', '获取作业详细信息', 'jobmg/elasticjob/jobOperateRestService/getJobDetailInfo', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('80018c73459347e0bf7cb9f089179e05', '获取作业信息', 'jobmg/elasticjob/jobOperateRestService/getJobInfo', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3568ca57a1bf4600a38b716982600625', '使分片项失效', 'jobmg/elasticjob/jobOperateRestService/disabledSharding', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d9d0f34456c24b44a6efa5634a6f115c', '使作业失效', 'jobmg/elasticjob/jobOperateRestService/disableJob', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d23f6f951ce5490dbe54dce47b1acfb1', '查看作业详情', 'jobmg/elasticjob/jobOperateRestService/getJobServerDetail', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('59064e87d0294f5097e8bb192ab4e98c', '更新作业信息', 'jobmg/elasticjob/jobOperateRestService/saveJobDetailInfo', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b32b96d0142d4fea8d5fd8a58011c0c2', '人员账号管理', 'domain/orguserauth/TaUserManagementRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('72fc1e15bee64231beee40b2d482aa43', '修改账户密码', 'domain/orguserauth/TaUserManagementRestService/updateUserPwdByUserId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b94fc48142fe4130b29757a07f385aeb', '异步查询组织', 'domain/orguserauth/TaUserManagementRestService/getOrgByAsync', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('634743c9057749748b82045ef69dd214', '条件查询人员信息', 'domain/orguserauth/TaUserManagementRestService/queryUserByConditon', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5861ad2003b94d8ab0d730112a098fae', '根据账户ID获取该账户的关联组织', 'domain/orguserauth/TaUserManagementRestService/getOrgUserByUserId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('02484f5c350544b5b16ead7293c3558e', '通过userId查询账户头像', 'domain/orguserauth/TaUserManagementRestService/queryAvatar', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('bdbe704910d8436fa2fe57985ff20431', '根据账户Id更新组织人员关联关系', 'domain/orguserauth/TaUserManagementRestService/updateOrgUserByUserId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b4bfe4f72bc54f5fb6d431053323ff7d', '查询组织树', 'domain/orguserauth/TaUserManagementRestService/queryOrgTree', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a8d04425958c452eb9461a93867884ac', '通过自定义组织id查询user', 'domain/orguserauth/TaUserManagementRestService/queryBatchUserByCustomOrgId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ab213f6723aa4e37bd2ce5597928fd53', '修改人员组织机构', 'domain/orguserauth/TaUserManagementRestService/updateUserOrgByUserId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('06cd55b18e1f44adbdeded3fca1355f7', '查询账户的组织信息', 'domain/orguserauth/TaUserManagementRestService/queryOrgInfoByUserId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('90829994ecaa4a0ca78c8ff564f2753b', '批量解锁', 'domain/orguserauth/TaUserManagementRestService/updateBatchUserUnLockByUserIds', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b39bb0d6720d4764b2cd0224f3659a80', '通过组织id查询组织人员信息', 'domain/orguserauth/TaUserManagementRestService/queryBatchUserByOrgId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('24cee81a613f416a8649d159931cede2', '批量禁用', 'domain/orguserauth/TaUserManagementRestService/updateBatchUserDisabledByUserIds', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5f575442b1f84595a1c05aef61f85765', '重置账户密码', 'domain/orguserauth/TaUserManagementRestService/resetUserPwByUserId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('25a2a2f205114fab819203012b99fa68', '通过userId修改该人员信息', 'domain/orguserauth/TaUserManagementRestService/updateUserByUserId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('203d2e7109ec4853997e2cfb462f5501', '获取组织模式配置', 'domain/orguserauth/TaUserManagementRestService/getUserOrgMultiConfig', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1e9ea424827e475c89f036ab1f85137e', '批量删除人员信息', 'domain/orguserauth/TaUserManagementRestService/deleteBatchUserByUserIds', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a1fa23f246904da9a3198966bbdd2122', '批量启用', 'domain/orguserauth/TaUserManagementRestService/updateBatchUserAbleByUserIds', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f2df6f27fcb945b39f263962a387aa16', '查询可发送人员', 'message/queryUser', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a50c6b036e164d5daaba6a33881d5370', '查询人员消息', 'message/queryUserMessage', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a272cfdac31c4af593d644e951b80a80', '查询通知附件', 'message/queryNoticeFiles', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('eb848308e574462e8d3bc9acb9d5defd', '发送通知', 'message/sendNotice2', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('02bbbed032b44a52ae6c5e82ec45d83a', '发送通知', 'message/sendNotice', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0aae6ab603b442429a9ca0b29396da80', '通知标记已读', 'message/readNotice', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('4b19cb3a440e4e3f9f6f34de34e6d695', '通知标记已读(批量)', 'message/readNotices', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f157f212127b49659461b34a0c23eb95', '上传通知附件', 'message/uploadNoticeFile', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('45fc94a23742400d9a681ac08695c246', '删除通知(批量)', 'message/removeNotices', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a213d1db5b6b4e08b204b701c0476024', '下载通知附件', 'message/downloadNoticeFile', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('09024f31af1341a7b6c19c38359fcca1', '查询人员私信', 'message/queryUserLetter', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('4bf5eb1024ea41e09fdd8e714ea57492', '发送私信', 'message/sendLetter2', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e8ac1a55c9db46d8a38d0c43f424a0bf', '发送私信', 'message/sendLetter', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2a2eb9bfce6d42168eaa271fa3a001b8', '查询人员对话', 'message/queryUserChat', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('448d31215eba49ecb975f1800ed00f79', '私信标记已读', 'message/readLetter', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8b11d3374b344f6292b2e13794b14fd7', '新建对话', 'message/createChat', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7ffbbd9552954860b058a29f27dc55f0', '查询人员私信(未读)', 'message/queryUserLetterNoRead', '415a0c59d499478da3b40fc896470569', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('af0be155c6934ecd83d8cf62b36437e2', '自定义资源', 'domain/orguserauth/customResourceRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('98c8e94f284e4cc19a66fb60ba04056d', '更新自定义资源', 'domain/orguserauth/customResourceRestService/updateTaCustomResourceByCustomResourceId', 'af0be155c6934ecd83d8cf62b36437e2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('91a7ffff45334096b124645f4f29974f', '删除自定义资源', 'domain/orguserauth/customResourceRestService/deleteTaCustomResourceByCustomResourceId', 'af0be155c6934ecd83d8cf62b36437e2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2e457c6060bd411eb65bae7068d3d5d9', '新增自定义资源', 'domain/orguserauth/customResourceRestService/addTaCustomResource', 'af0be155c6934ecd83d8cf62b36437e2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('89d993cfaedb4c5db40fcf1bbe4a96ae', '查询自定义资源', 'domain/orguserauth/customResourceRestService/queryTaCustomResourceByCustomResourceId', 'af0be155c6934ecd83d8cf62b36437e2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7859157a0cd94c89995b0dde9fc40bc6', '行政区划管理', 'domain/orguserauth/areaAggregateRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6cbf93b5f4b346b890f104756c55d078', '更新行政区划数据', 'domain/orguserauth/areaAggregateRestService/updateArea', '7859157a0cd94c89995b0dde9fc40bc6', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e0819670288c4a8991872a398894488e', '新增行政区划数据', 'domain/orguserauth/areaAggregateRestService/addArea', '7859157a0cd94c89995b0dde9fc40bc6', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0cdb0e7d42b941e3bc99399aa69a0f89', '异步加载行政区划数据', 'domain/orguserauth/areaAggregateRestService/queryAreaByAsync', '7859157a0cd94c89995b0dde9fc40bc6', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6a73d3aa6f5949caa1f4c536ffe01400', '异步加载下级功能资源', 'domain/orguserauth/resourceManagementRestService/queryChildResource', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f2e756c387524382a6fa4d9f3db9abf7', '服务异常日志', 'logmg/exceptionlog/serverExceptionLogRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('676b9aca9d054d65aac3830436d2322e', '用户可配置字段', 'domain/orguserauth/configurableFieldsRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b771b6243ac04ddc8f9e496c175f8f52', '更新配置字段详细信息', 'domain/orguserauth/configurableFieldsRestService/modifyConfigurableField', '676b9aca9d054d65aac3830436d2322e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d3dd51b3f42141b490aab2a4b76dba44', '重置默认字段配置', 'domain/orguserauth/configurableFieldsRestService/reloadDefaultManageableFields', '676b9aca9d054d65aac3830436d2322e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('bbde0232aa2c41b59593666c95186ab6', '更新配置字段', 'domain/orguserauth/configurableFieldsRestService/modifyConfigurableFields', '676b9aca9d054d65aac3830436d2322e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('47a91cf250b8446c89cc98628adbcd31', '获取配置字段', 'domain/orguserauth/configurableFieldsRestService/queryManageableFields', '676b9aca9d054d65aac3830436d2322e', '1', '1', sysdate, '1');
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('652c99e71c26473d92dc1c9b881f2b64', '扩展字段', 'domain/orguserauth/configurableFieldsRestService/addConfigurableField', '676b9aca9d054d65aac3830436d2322e', '1', '1', sysdate, '1', NULL);

INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8545972435bb4abab8d2524fac510a04', '润乾页面请求', 'tarunqianresource/taRunqianResourceRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5855e399c9074b899850bb25ce1aaf76', '分布式任务之服务器操作', 'jobmg/elasticjob/serverOperateRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c7ae73b1a4cd4ad8a4b4e2f199bad322', '新增自定义组织类别', 'domain/orguserauth/customOrgAggregateRestService/addCustomOrgTypeName', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ef46133fe6b04b04b0a5e7481d92b2ea', '更新自定义组织类别', 'domain/orguserauth/customOrgAggregateRestService/updateCustomOrgTypeName', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', sysdate, '1');
-- INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('47488be533b54ae99f0418ba3962e8b9', '动态服务', 'dynamic/rest/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7ac1c71173f2421ea634f0e274303521', '新增批量的角色账户关联关系', 'domain/orguserauth/roleRestService/addBatchRoleUsers', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('670804d52f2c473bb58ad6c87ceefbf9', '删除角色账户关联关系', 'domain/orguserauth/roleRestService/deleteRoleUserByKey', 'fc45635549b5487c87d0287490a79b5f', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('072db1f4c4a848cd938d95b4057ff4c0', '根据userId获取当前账户可授权的菜单权限', 'domain/orguserauth/roleAgentRestService/queryUsePermissionByUserId', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7ff6d8f6a9994e7cb6710b4edd1d109c', '批量删除代理角色', 'domain/orguserauth/roleAgentRestService/deleteBatchAgentRole', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e546d678a75740c0be0e9a0914d3fac4', '查询代理角色权限', 'domain/orguserauth/roleAgentRestService/queryAgentRoleAuthority', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('4b3512dec0204cd19b7c5fbdd75a97fa', '获取人员列表', 'domain/orguserauth/TaUserManagementRestService/queryEffectiveUser', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('416f405ee4dc43d5b5c9f4bbbf6414ef', '获取标签', 'domain/orguserauth/TaUserManagementRestService/queryTagByUserId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d3db2fb5724d4839b04ff4260174a70d', '添加人员信息', 'domain/orguserauth/TaUserManagementRestService/addUser', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('415a0c59d499478da3b40fc896470569', '消息通知', 'message/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('efaecd1281c64c89ba87363bf1c18900', '根据资源类别查询自定义资源树', 'domain/orguserauth/customResourceRestService/queryALLTaCustomResourceTreeByCategoryId', 'af0be155c6934ecd83d8cf62b36437e2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7af7009339d145c1967e8d46a4b194d1', '查询资源类别', 'domain/orguserauth/customResourceRestService/queryALLTaCustomResourceParent', 'af0be155c6934ecd83d8cf62b36437e2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('321ac2aade66460dbea294f434dc4c9d', '日志动态配置', 'logmg/logconfig/logConfigRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e34479764c66437e993c27072a13dfca', '新增日志配置', 'logmg/logconfig/logConfigRestService/addLogConfig', '321ac2aade66460dbea294f434dc4c9d', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('72fb390446eb475cbefd28e0a26041c0', '获取日志配置', 'logmg/logconfig/logConfigRestService/getLogConfig', '321ac2aade66460dbea294f434dc4c9d', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('89872c1e30e543e1ae0352c0b1ecfb72', '删除日志配置', 'logmg/logconfig/logConfigRestService/deleteLogConfigByName', '321ac2aade66460dbea294f434dc4c9d', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0e4c73815f2e43deaadbb0646cc9ae7c', '修改文件名类型', 'logmg/logconfig/logConfigRestService/configFileNamePattern', '321ac2aade66460dbea294f434dc4c9d', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ffffdb7e67b74234a3a230abd06e4d32', '修改日志级别和日志输出类型', 'logmg/logconfig/logConfigRestService/configLevelAndAppenderType', '321ac2aade66460dbea294f434dc4c9d', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7f621426782342faa371319c677f066e', '自定义资源类别', 'domain/orguserauth/resourceCategoryRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f757b23bbfbe461b8478e5ad456ec64f', '删除自定义资源类别', 'domain/orguserauth/resourceCategoryRestService/deleteTaResourceCategoryByCategoryId', '7f621426782342faa371319c677f066e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b94b3fd3ce4f4e89860a3b0b00f985b4', '更新自定义资源类别', 'domain/orguserauth/resourceCategoryRestService/updateTaResourceCategoryByCategoryId', '7f621426782342faa371319c677f066e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('80d490da38044ad58c96da08e6e58efd', '查询所有的自定义资源类别列表', 'domain/orguserauth/resourceCategoryRestService/queryALLTaResourceCategory', '7f621426782342faa371319c677f066e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8c54473a278248628dcad204a57ebd9a', '新增自定义资源类别', 'domain/orguserauth/resourceCategoryRestService/addTaResourceCategory', '7f621426782342faa371319c677f066e', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('4e90eb395b2e41ed81285b7ba9362b84', '登录失败日志', 'logMg/loginFailLog/taLoginFailLogRestService/**', null, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('498a9c21ba1d4042b85b2d0b8d4ec61f', '登录失败列表', 'logMg/loginFailLog/taLoginFailLogRestService/queryLoginFailLog', '4e90eb395b2e41ed81285b7ba9362b84', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('20cf287cd36448d4ba6b6930ea472824', '查询所有的自定义资源树', 'domain/orguserauth/customResourceRestService/queryALLTaCustomResourceTree', 'af0be155c6934ecd83d8cf62b36437e2', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('314759f094014f24b991d467378d1755', '登录失败图', 'logMg/loginFailLog/taLoginFailLogRestService/loginFailChart', '4e90eb395b2e41ed81285b7ba9362b84', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('253c2eae3e0046c2ba4f53f4ffa89ba3', '在线表单模板管理', 'onlineForm/templateMg/templateMgRestService/**', NULL, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d9a5e31b83b141279b387bc4dfac0713', '表单模板条件分页查询', 'onlineForm/templateMg/templateMgRestService/queryTemplate', '253c2eae3e0046c2ba4f53f4ffa89ba3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('66ee423b80f84407a952e4edf2d27901', '通过ID查询模板JSON字符串', 'onlineForm/templateMg/templateMgRestService/queryTemplateContentById', '253c2eae3e0046c2ba4f53f4ffa89ba3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2133136c1fca4fa79ed4c5b960bf914c', '新增表单模板', 'onlineForm/templateMg/templateMgRestService/insertTemplate', '253c2eae3e0046c2ba4f53f4ffa89ba3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8d50ff41b6aa42c383f119cc4dd9abf6', '更新表单模板', 'onlineForm/templateMg/templateMgRestService/updateTemplate', '253c2eae3e0046c2ba4f53f4ffa89ba3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('59ea5a521dd1439f839466603c76302a', '更新模板有效性', 'onlineForm/templateMg/templateMgRestService/updateTemplateEffective', '253c2eae3e0046c2ba4f53f4ffa89ba3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c433ed5a2a1340f6a0063637861de279', '删除表单模板', 'onlineForm/templateMg/templateMgRestService/deleteTemplates', '253c2eae3e0046c2ba4f53f4ffa89ba3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('632895cc5cd14dbdb76d9c10fffd0786', '获取表结构', 'onlineForm/templateMg/templateMgRestService/queryTableColumns', '253c2eae3e0046c2ba4f53f4ffa89ba3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('60bd58fe73d0472881349ecbfdd0bd29', '获取数据库表', 'onlineForm/templateMg/templateMgRestService/queryTable', '253c2eae3e0046c2ba4f53f4ffa89ba3', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c92a08d19d514b6c8c462de3174b16a9', '注册角色权限管理', 'org/authority/registeredRoleAuthorityManagementRestService/**',NULL, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6b8628679fd84bf989030e328248bd0a', '下载组织导入模板', 'domain/orguserauth/orgRestService/downloadOrgFIle', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b117d8dfb50946bfb8714fcb235845d2', '下载功能资源导入模板', 'domain/orguserauth/resourceManagementRestService/downloadResourceFIle', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a2fe720ce245464ea4cf8997645d8ae4', '导入导出功能', 'org/orguser/importAndExportService/**',NULL, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER) VALUES('60f05ed2d53c4676978971a62ab5b885', '会话管理', 'session/**', NULL, '0', '1', sysdate, '1');
INSERT INTO taurl (urlid, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER) VALUES('65062ea24d9f40e884314ad23429f717', '在线人员信息', 'session/onlineUser', '60f05ed2d53c4676978971a62ab5b885', '1', '1', sysdate, '1');
INSERT INTO taurl (urlid, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER) VALUES('9b061fa118ec4954b6db899e1332d3ac', '在线人员下线', 'session/offline', '60f05ed2d53c4676978971a62ab5b885', '1', '1', sysdate, '1');


INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('246a43849d3c4394b9b3ae9bd099c778', '账号申请', 'domain/applyexamine/account/apply/**', '', '0', '1', sysdate, '1', '');
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('e86bd08c7763451da3bdfcf161528df3', '账号申请审核', 'domain/applyexamine/account/examine/**', NULL, '0', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('b288d7d44bbe4ace808f83aac39aa323', '注册通道管理', 'domain/orguserauth/taregisterchannel/**', '', '0', '1', sysdate, '1', '');
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('ba99b41be0c74fe6a650ee44524dfc0d', '顶级组织管理', 'domain/orguserauth/orgRootRestService/**', NULL, '0', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('85ea1fc6a56a44e082eb8f20bb11faf3', '人员信息管理', 'domain/orguserauth/userInfoAggregateRestService/**', NULL, '0', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('8eec112446c445a5a8f4ebc6c3cf96f4', '人员账号导入导出', 'domain/orguserauth/userImportAndExportRestService/**', NULL, '0', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('c15684fd217840119148ac802806cc2c', '公共组织查询', 'domain/orguserauth/orgCommonRestService/**', NULL, '0', '1', sysdate, '1', NULL);

INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('bf8aa724aada49c8b46102111af3d038', '限流管理', 'rateLimit/**', NULL, '0', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('8f5b497d2ffc4ff6acad74ecbbfc77a2', '第三方访问权限管理', 'accessAuthority/**', NULL, '0', '1', sysdate, '1', NULL);


INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1d40a4bc2fbd4f2f9ebbe3cab99114b2', '查询组织树', 'domain/orguserauth/orgRestService/queryOrgTree', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('f14e0a7b0bd040449684f0211644b990', '查询所有组织标签', 'domain/orguserauth/orgRestService/queryTags', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1b0945a85ec6402baff5259749cd7e3c', '查询组织的标签ID', 'domain/orguserauth/orgRestService/queryOrgTags', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1d91c8e736c248759ca0ad253c814e3c', '查询组织树(排除自身-变更上级用)', 'domain/orguserauth/orgRestService/queryOrgTreeNoSelf', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('36ce781771ad47d1a0c7bc141eaed5a7', '查询组织树节点', 'domain/orguserauth/orgRestService/queryOrgTreeNode', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('93f1226eae3c4055b569804e6811784e', '分页查询组织下人员', 'domain/orguserauth/orgRestService/queryOrgUserPage', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('476927f6f5b0407a8ac8866a36b10460', '查询组织下权限数据', 'domain/orguserauth/orgRestService/queryOrgAuthority', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('79c2b2f99cdc45f79d158398f544368f', '查询组织下权限数据(根据角色标记数据)', 'domain/orguserauth/orgRestService/queryOrgAuthorityWithRole', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('59cbef2dc69444bfa5e0f6992a2caaf9', '查询组织下角色(分页)', 'domain/orguserauth/orgRestService/queryOrgRole', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('04ec9298c40748d7a223768e97fee9c1', '查询当前登录人员可管理的角色(分页)', 'domain/orguserauth/orgRestService/queryManageableRole', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('7f23bc98596a41cf9c923aa0d2e81a4c', '根据组织ID查询该组织可以被分配的角色列表', 'domain/orguserauth/orgRestService/queryNoWrapperRolesByOrgId', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('416d55f16fbd47ceaecd0fa323ac6c7a', '查询当前登录人员可管理的人员(分页)', 'domain/orguserauth/orgRestService/queryManageableUser', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('00d21e0434be4af7813e4807c788944e', '新增组织', 'domain/orguserauth/orgRestService/addOrg', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('ebddc36d3c1b48c3bd004a3fc78a7581', '编辑组织', 'domain/orguserauth/orgRestService/editOrg', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('8903e43ab05a41fb86e9a87132e5e445', '异步组织树查询(排除自己)', 'domain/orguserauth/orgRestService/queryOrgTreeNodeWithNoSelf', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('7547ddd0e28d48428b1d1011a69958ed', '变更上级组织', 'domain/orguserauth/orgRestService/editParentOrg', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('d57eb127763f432194740c5afca97619', '启用/禁用组织', 'domain/orguserauth/orgRestService/enableOrg', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('39cfd84752494965a0962fccdedce6d6', '删除组织', 'domain/orguserauth/orgRestService/removeOrg', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('370d6e23d66e4e3cab7c5c4d28e3aee6', '组织绑定角色', 'domain/orguserauth/orgRestService/bindOrgRole', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('da41b0851b4e41da996e0f801598679f', '组织删除绑定角色', 'domain/orguserauth/orgRestService/removeOrgRole', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('629b1f09629b4397b3de584df4a12dfa', '组织绑定人员', 'domain/orguserauth/orgRestService/bindOrgUser', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('941be66daf39418eaa058776053a8918', '组织删除绑定人员', 'domain/orguserauth/orgRestService/removeOrgUser', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('62c105f215b140aabb3d7d3413eae183', '批量导入组织', 'domain/orguserauth/orgRestService/importBatchOrgExcel', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('ea96f8c1599740ccbd173138d7856f82', '导出组织', 'domain/orguserauth/orgRestService/exportOrg', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1531fa815d5444369848456ef3ce6ec1', '查询行政区划', 'domain/orguserauth/orgRestService/queryAreaByAsync', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('a2098760f6cd4811b340260def2b2d5f', '条件查询人员', 'domain/orguserauth/orgRestService/queryEffectiveUser', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', sysdate, '1', NULL);


INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a89f03ca59754c49b38ad5278b0f5c8d', '获取全部的有效界面元素信息', 'domain/orguserauth/menuAction/queryAllElement', 'aef954306f5d4c6d9b5e507aa3f175c6', '1', '1', sysdate, '1');

INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('0170b4dc35c34023a838c5f09aa68bd0', '行为日志', 'oplogRestService/**', '', '0', '1', sysdate, '1', '');
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('0d8cabb5ed624eed9686a87e3dbb5620', '行为日志分页查询', 'oplogrestservice/page', '0170b4dc35c34023a838c5f09aa68bd0', '1', '1', sysdate, '1', NULL);


--  初始化功能菜单
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('0415d44401b24605a21b589b6aaa349e','40337bdecb19484ebeb39d6c21aaca26','管理系统','','sysmg','',51,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e','银海软件/管理系统','1','setting','','2',0,'0','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('0aac95c1e73947bea41be639cc4e9036','7b7f9cd1675a4b54b05c7c7cf0a7ac63','工作台角色模板管理',NULL,'sysmg','workTablePage.html#/roleTemplateMg',20,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7b7f9cd1675a4b54b05c7c7cf0a7ac63/0aac95c1e73947bea41be639cc4e9036','银海软件/管理系统/工作台模块/工作台角色模板管理','3','layout','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('1b7f1d4202a841aaac9186c8772cfdac','ea2297291cb24f8ba2eb01c2ae00850d','功能审核',NULL,'sysmg','authority.html#/examineManagement',10,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/ea2297291cb24f8ba2eb01c2ae00850d/1b7f1d4202a841aaac9186c8772cfdac','银海软件/管理系统/审核管理/功能审核','3','','','2',0,'3','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('1d4e283ad5584e02811f6b188d3592bc','0415d44401b24605a21b589b6aaa349e','系统管理','','sysmg','',41,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc','银海软件/管理系统/系统管理','2','setting','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('1e706f26bc144c1da12022359c238053','48afedddc8f04c668b3c1572c30a7745','组织机构管理','','sysmg','orguser.html#/orgManagement',0,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/48afedddc8f04c668b3c1572c30a7745/1e706f26bc144c1da12022359c238053','银海软件/管理系统/组织人员管理/组织机构管理','3','cluster','','2',1,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('25e6a452ed7a454ab3c757b5f8ff0a70','6d0bbcbe65e543d8a5d272d6a6a3bf4c','登录失败日志',NULL,'sysmg','audit.html#/loginFailLog',30,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/6d0bbcbe65e543d8a5d272d6a6a3bf4c/25e6a452ed7a454ab3c757b5f8ff0a70','银海软件/管理系统/审计管理/登录失败日志','3','','','2',0,'4','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('2ac775f74f934264ab1d43b5a42b56ef','6d0bbcbe65e543d8a5d272d6a6a3bf4c','登录日志分析',NULL,'sysmg','audit.html#/loginLogAnalysis',0,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/6d0bbcbe65e543d8a5d272d6a6a3bf4c/2ac775f74f934264ab1d43b5a42b56ef','银海软件/管理系统/审计管理/登录日志分析','3','','','2',0,'4','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('3dbde33722154503a7d22ac60f6a0e4e','48afedddc8f04c668b3c1572c30a7745','自定义组织管理','','sysmg','orguser.html#/customOrgManagement',20,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/48afedddc8f04c668b3c1572c30a7745/3dbde33722154503a7d22ac60f6a0e4e','银海软件/管理系统/组织人员管理/自定义组织管理','3','deployment-unit','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('3df588fc565d4287b3cefcd00a39cd91','7459c1b525934151a1d309a304933644','标签管理','','sysmg','sysmg.html#/tagManagement',30,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7459c1b525934151a1d309a304933644/3df588fc565d4287b3cefcd00a39cd91','银海软件/管理系统/资源管理/标签管理','3','tags','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('40337bdecb19484ebeb39d6c21aaca26','0','银海软件','','sysmg','',0,'40337bdecb19484ebeb39d6c21aaca26','银海软件','0','','','2','1','0','1','','1','',sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('43f468b40c6c4c76a3a2fe4be903f4c7','1d4e283ad5584e02811f6b188d3592bc','系统异常日志',NULL,'sysmg','audit.html#/systemExceptionLog',110,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/43f468b40c6c4c76a3a2fe4be903f4c7','银海软件/管理系统/系统管理/系统异常日志','3','exception','','2',0,'1','1','1','1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('466cc05d2e2f4dcea17a09e3ea0ca9aa','5b658a734b39420c965f1bc68832c4fa','系统健康情况',NULL,'sysmg','projectCommon/workTableComponents/systemhealth',60,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7b7f9cd1675a4b54b05c7c7cf0a7ac63/5b658a734b39420c965f1bc68832c4fa/466cc05d2e2f4dcea17a09e3ea0ca9aa','银海软件/管理系统/工作台模块/工作台组件/系统健康情况','4',NULL,NULL,'2',1,'0','1','1','1',NULL,sysdate,'1','0','001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('48afedddc8f04c668b3c1572c30a7745','0415d44401b24605a21b589b6aaa349e','组织人员管理','','sysmg','',10,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/48afedddc8f04c668b3c1572c30a7745','银海软件/管理系统/组织人员管理','2','usergroup-add','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('49296b8d9bd04786a8860e7011d4481f','6d0bbcbe65e543d8a5d272d6a6a3bf4c','系统异常日志',NULL,'sysmg','audit.html#/systemExceptionLog',40,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/6d0bbcbe65e543d8a5d272d6a6a3bf4c/49296b8d9bd04786a8860e7011d4481f','银海软件/管理系统/审计管理/系统异常日志','3','','','2',0,'4','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('4b2eee0d7ded4e8094d4acf439fd3a1c','48afedddc8f04c668b3c1572c30a7745','行政区划管理','','sysmg','orguser.html#/areaManagement',30,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/48afedddc8f04c668b3c1572c30a7745/4b2eee0d7ded4e8094d4acf439fd3a1c','银海软件/管理系统/组织人员管理/行政区划管理','3','gold','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('4d2f1971a6d947898a848aa775a45b0c','78ad02fdb879406ebc5e7a4faf8f5905','注册用户权限管理','','sysmg','authority.html#/registerAuthorityManagement',40,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/78ad02fdb879406ebc5e7a4faf8f5905/4d2f1971a6d947898a848aa775a45b0c','银海软件/管理系统/资源权限管理/注册用户权限管理','3','usergroup-add',NULL,'2',2,'1','1','0','1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('519e7153c57745edb884dd10ae7dccf5','ea2297291cb24f8ba2eb01c2ae00850d','功能审核日志',NULL,'sysmg','authority.html#/examineManagementLog',30,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/ea2297291cb24f8ba2eb01c2ae00850d/519e7153c57745edb884dd10ae7dccf5','银海软件/管理系统/审核管理/功能审核日志','3','','','2',0,'3','1','1','1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('525a8cb155444e7f823cd070802f753f','48afedddc8f04c668b3c1572c30a7745','人员账号管理','','sysmg','orguser.html#/userInfoManagement',10,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/48afedddc8f04c668b3c1572c30a7745/525a8cb155444e7f823cd070802f753f','银海软件/管理系统/组织人员管理/人员账号管理','3','usergroup-add',NULL,'2',0,'1','1','1','1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('5611d1533d494a839c0be1e7a05da31f','7459c1b525934151a1d309a304933644','URL资源管理',NULL,'sysmg','resourcemg.html#/urlmg',50,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7459c1b525934151a1d309a304933644/5611d1533d494a839c0be1e7a05da31f','银海软件/管理系统/资源管理/URL资源管理','3','global','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('59a7fb9c459a4dd48d468f2add1d32b2','1d4e283ad5584e02811f6b188d3592bc','接入系统管理','','sysmg','sysmg.html#/accessSystem',70,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/59a7fb9c459a4dd48d468f2add1d32b2','银海软件/管理系统/系统管理/接入系统管理','3','api','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('5ab7dcc091da41c785b7110d59f93a30','48afedddc8f04c668b3c1572c30a7745','账号申请','','sysmg','orguser.html#/userApplyManagement',40,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/48afedddc8f04c668b3c1572c30a7745/5ab7dcc091da41c785b7110d59f93a30','银海软件/管理系统/组织人员管理/账号申请','3','usergroup-add',NULL,'2',0,'1','1','1','1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('5b658a734b39420c965f1bc68832c4fa','7b7f9cd1675a4b54b05c7c7cf0a7ac63','工作台组件',NULL,'sysmg',NULL,30,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7b7f9cd1675a4b54b05c7c7cf0a7ac63/5b658a734b39420c965f1bc68832c4fa','银海软件/管理系统/工作台模块/工作台组件','3','','','2',0,'0','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('5e67c7acef914c349d8aff076921f6b5','78ad02fdb879406ebc5e7a4faf8f5905','相似权限管理','','sysmg','authority.html#/similarAuthority',30,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/78ad02fdb879406ebc5e7a4faf8f5905/5e67c7acef914c349d8aff076921f6b5','银海软件/管理系统/资源权限管理/相似权限管理','3','block','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('60554e93387146bb9c7357907ba093fa','7b7f9cd1675a4b54b05c7c7cf0a7ac63','工作台组件管理',NULL,'sysmg','workTablePage.html#/componentsMg',10,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7b7f9cd1675a4b54b05c7c7cf0a7ac63/60554e93387146bb9c7357907ba093fa','银海软件/管理系统/工作台模块/工作台组件管理','3','block','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('6d0bbcbe65e543d8a5d272d6a6a3bf4c','0415d44401b24605a21b589b6aaa349e','审计管理',NULL,'sysmg',NULL,'61','40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/6d0bbcbe65e543d8a5d272d6a6a3bf4c','银海软件/管理系统/审计管理','2','','','2',0,'4','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('722e1cc774a14178a488eb42ef4099de','7459c1b525934151a1d309a304933644','润乾模板管理',NULL,'sysmg','functionModules.html#/runqian',60,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7459c1b525934151a1d309a304933644/722e1cc774a14178a488eb42ef4099de','银海软件/管理系统/资源管理/润乾模板管理','3','form','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('72888507aba5484a8942e8dd0e6b6f7f','fb8637c2e52e4b05bd2c07d742141ee7','作业管理',NULL,'sysmg','sysmg.html#/jobManager',0,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/fb8637c2e52e4b05bd2c07d742141ee7/72888507aba5484a8942e8dd0e6b6f7f','银海软件/管理系统/系统管理/分布式任务管理/作业管理','4','form','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('73dd0eb461784dfe8bb7d18616a83a09','6d0bbcbe65e543d8a5d272d6a6a3bf4c','审计日志',NULL,'sysmg','audit.html#/auditLog',60,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/6d0bbcbe65e543d8a5d272d6a6a3bf4c/73dd0eb461784dfe8bb7d18616a83a09','银海软件/管理系统/审计管理/审计日志','3','','','2',0,'4','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('7459c1b525934151a1d309a304933644','0415d44401b24605a21b589b6aaa349e','资源管理','','sysmg','',40,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7459c1b525934151a1d309a304933644','银海软件/管理系统/资源管理','2','align-left','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('76514eaf165e42c5838b1fbcf7993863','6d0bbcbe65e543d8a5d272d6a6a3bf4c','访问限制日志',NULL,'sysmg','audit.html#/accessDenyLog',20,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/6d0bbcbe65e543d8a5d272d6a6a3bf4c/76514eaf165e42c5838b1fbcf7993863','银海软件/管理系统/审计管理/访问限制日志','3','','','2',0,'4','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('781cf3051659466bb255cdac3a1078a3','48afedddc8f04c668b3c1572c30a7745','账号申请审核','','sysmg','orguser.html#/userExamineManagement',50,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/48afedddc8f04c668b3c1572c30a7745/781cf3051659466bb255cdac3a1078a3','银海软件/管理系统/组织人员管理/账号申请审核','3','usergroup-add',NULL,'2',0,'1','1','1','1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('78ad02fdb879406ebc5e7a4faf8f5905','0415d44401b24605a21b589b6aaa349e','资源权限管理','','sysmg','',30,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/78ad02fdb879406ebc5e7a4faf8f5905','银海软件/管理系统/资源权限管理','2','safety-certificate','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('78fe48d6ab83452892dd10cd657d5f59','6d0bbcbe65e543d8a5d272d6a6a3bf4c','审核日志',NULL,'sysmg','authority.html#/examineManagementLog',10,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/6d0bbcbe65e543d8a5d272d6a6a3bf4c/78fe48d6ab83452892dd10cd657d5f59','银海软件/管理系统/审计管理/审核日志','3','','','2',0,'4','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('7b7f9cd1675a4b54b05c7c7cf0a7ac63','0415d44401b24605a21b589b6aaa349e','工作台模块',NULL,'sysmg',NULL,'51','40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7b7f9cd1675a4b54b05c7c7cf0a7ac63','银海软件/管理系统/工作台模块','2','laptop','','2',0,'0','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('7c1dabd160974d8f90858c187cefa128','1d4e283ad5584e02811f6b188d3592bc','日志动态配置','','sysmg','logmg.html#/logConfig',0,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/7c1dabd160974d8f90858c187cefa128','银海软件/管理系统/系统管理/日志动态配置','3','file-sync','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('86451810f9e24bb8ae6ac992422fd38f','5b658a734b39420c965f1bc68832c4fa','系统活跃度',NULL,'sysmg','projectCommon/workTableComponents/systemactivity',50,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7b7f9cd1675a4b54b05c7c7cf0a7ac63/5b658a734b39420c965f1bc68832c4fa/86451810f9e24bb8ae6ac992422fd38f','银海软件/管理系统/工作台模块/工作台组件/系统活跃度','4',NULL,NULL,'2',1,'0','1','1','1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('86d9f05789894b8fae5fb431c6e14168','5b658a734b39420c965f1bc68832c4fa','分析',NULL,'sysmg','projectCommon/workTableComponents/analysis',10,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7b7f9cd1675a4b54b05c7c7cf0a7ac63/5b658a734b39420c965f1bc68832c4fa/86d9f05789894b8fae5fb431c6e14168','银海软件/管理系统/工作台模块/工作台组件/分析','4','','','1',1,'0','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('877e407281dd48acb05a77fcb922bc73','78ad02fdb879406ebc5e7a4faf8f5905','权限代理','','sysmg','authority.html#/authorityAgent',20,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/78ad02fdb879406ebc5e7a4faf8f5905/877e407281dd48acb05a77fcb922bc73','银海软件/管理系统/资源权限管理/权限代理','3','team','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('8aa86ed4c7f84183935a262db4a605d3','78ad02fdb879406ebc5e7a4faf8f5905','管理员权限管理','','sysmg','authority.html#/adminAuthority',10,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/78ad02fdb879406ebc5e7a4faf8f5905/8aa86ed4c7f84183935a262db4a605d3','银海软件/管理系统/资源权限管理/管理员权限管理','3','safety','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('8b00ee078064440cbd1ee34118ed2c52','1d4e283ad5584e02811f6b188d3592bc','会话管理','','sysmg','sysmg.html#/sessionManagement',130,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/8b00ee078064440cbd1ee34118ed2c52','银海软件/管理系统/系统管理/会话管理','3','qq',NULL,'2',0,'1','1','1','1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('95bb9b749bf54e4692b0b1f14fd1b5ab','78ad02fdb879406ebc5e7a4faf8f5905','经办权限管理','','sysmg','authority.html#/roleAuthorityManagement',0,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/78ad02fdb879406ebc5e7a4faf8f5905/95bb9b749bf54e4692b0b1f14fd1b5ab','银海软件/管理系统/资源权限管理/角色权限管理','3','idcard','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('a0015ca02a574200afd7ca4178017c75','48afedddc8f04c668b3c1572c30a7745','顶级组织管理',NULL,'sysmg','orguser.html#/orgRootManagement',80,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/48afedddc8f04c668b3c1572c30a7745/a0015ca02a574200afd7ca4178017c75','银海软件/管理系统/组织人员管理/顶级组织管理','3','usergroup-add',NULL,'2',1,'1','1','1',NULL,NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('a3c94b4edf1e4e9d8665a81dc1c5f778','1d4e283ad5584e02811f6b188d3592bc','动态服务','','sysmg','sysmg.html#/dynamicRest',100,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/a3c94b4edf1e4e9d8665a81dc1c5f778','银海软件/管理系统/系统管理/动态服务','3','database','','2',0,'1','1','0','1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('a90da8b111ef40d29b670776c774851d','5b658a734b39420c965f1bc68832c4fa','超级测试',NULL,'sysmg','projectCommon/workTableComponents/analysis',30,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7b7f9cd1675a4b54b05c7c7cf0a7ac63/5b658a734b39420c965f1bc68832c4fa/a90da8b111ef40d29b670776c774851d','银海软件/管理系统/工作台模块/工作台组件/超级测试','4',NULL,NULL,'2',1,'0','1','1','1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('b2d888803a5e425fa59af5d247a87a6e','6d0bbcbe65e543d8a5d272d6a6a3bf4c','操作日志',NULL,'sysmg','audit.html#/operationLog',50,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/6d0bbcbe65e543d8a5d272d6a6a3bf4c/b2d888803a5e425fa59af5d247a87a6e','银海软件/管理系统/审计管理/操作日志','3','','','2',0,'4','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('b4e12d46211443069618302072200e71','1d4e283ad5584e02811f6b188d3592bc','第三方访问权限管理','','sysmg','sysmg.html#/accessAuthority',150,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/b4e12d46211443069618302072200e71','银海软件/管理系统/系统管理/第三方访问权限管理','3','setting',NULL,'2',0,'1','1','1','1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('bd9d0bba145c458e841aa9da0aeeb1d8','7459c1b525934151a1d309a304933644','用户可管理字段配置','','sysmg','sysmg.html#/userInfoManagement',40,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7459c1b525934151a1d309a304933644/bd9d0bba145c458e841aa9da0aeeb1d8','银海软件/管理系统/资源管理/用户可管理字段配置','3','user','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('bdcd5003137943299f69b36e55f2dc66','1d4e283ad5584e02811f6b188d3592bc','行为日志',NULL,'sysmg','logmg.html#/opLog',140,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/bdcd5003137943299f69b36e55f2dc66','银海软件/管理系统/系统管理/行为日志','3',NULL,NULL,'2',0,'1','1','1','1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('bf447212de284c79a0d73c658d0692b4','1d4e283ad5584e02811f6b188d3592bc','功能配置管理','sysmg','sysmg','sysmg.html#/configManagement',80,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/bf447212de284c79a0d73c658d0692b4','银海软件/管理系统/系统管理/功能配置管理','3','compass','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('c2207fbb63c545cfb463924bfc6a378c','48afedddc8f04c668b3c1572c30a7745','线上注册通道配置','','sysmg','orguser.html#/registerChannelManagement',60,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/48afedddc8f04c668b3c1572c30a7745/c2207fbb63c545cfb463924bfc6a378c','银海软件/管理系统/组织人员管理/线上注册通道配置','3','usergroup-add',NULL,'2',0,'1','1','1','1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('c2745b7cae7846acb9bcf8d0f4e836e8','7459c1b525934151a1d309a304933644','自定义资源管理','','sysmg','sysmg.html#/customResource',20,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7459c1b525934151a1d309a304933644/c2745b7cae7846acb9bcf8d0f4e836e8','银海软件/管理系统/资源管理/自定义资源管理','3','copyright','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('c578d9f8626d48f2971d7a18ac5281c5','fb8637c2e52e4b05bd2c07d742141ee7','作业历史',NULL,'sysmg','sysmg.html#/jobHistory',10,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/fb8637c2e52e4b05bd2c07d742141ee7/c578d9f8626d48f2971d7a18ac5281c5','银海软件/管理系统/系统管理/分布式任务管理/作业历史','4','clock-circle','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('c6be27122b164a179cb2c0ca026ee2e4','5b658a734b39420c965f1bc68832c4fa','系统规模',NULL,'sysmg','projectCommon/workTableComponents/systemsize',40,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7b7f9cd1675a4b54b05c7c7cf0a7ac63/5b658a734b39420c965f1bc68832c4fa/c6be27122b164a179cb2c0ca026ee2e4','银海软件/管理系统/工作台模块/工作台组件/系统规模','4',NULL,NULL,'1',1,'0','1','1','1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('ccbf8db88cc548eba4dfbb920c74feec','5b658a734b39420c965f1bc68832c4fa','监控',NULL,'sysmg','projectCommon/workTableComponents/monitor',20,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7b7f9cd1675a4b54b05c7c7cf0a7ac63/5b658a734b39420c965f1bc68832c4fa/ccbf8db88cc548eba4dfbb920c74feec','银海软件/管理系统/工作台模块/工作台组件/监控','4','','','1',0,'0','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('cd49aa1e1a724404a4dfb4f290e1ed62','1d4e283ad5584e02811f6b188d3592bc','系统配置管理',NULL,'sysmg','sysmg.html#/systemConfigManagement',90,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/cd49aa1e1a724404a4dfb4f290e1ed62','银海软件/管理系统/系统管理/系统配置管理','3','tool','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('d81d6ee5ec684fd8a32935ceb03d1cf7','5b658a734b39420c965f1bc68832c4fa','用户',NULL,'sysmg','projectCommon/workTableComponents/user',0,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7b7f9cd1675a4b54b05c7c7cf0a7ac63/5b658a734b39420c965f1bc68832c4fa/d81d6ee5ec684fd8a32935ceb03d1cf7','银海软件/管理系统/工作台模块/工作台组件/用户','4','','','1',0,'0','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('daceeff8a97b46cb9573b93ba3a5a792','48afedddc8f04c668b3c1572c30a7745','人员管理','','sysmg','orguser.html#/userManagement',10,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/48afedddc8f04c668b3c1572c30a7745/daceeff8a97b46cb9573b93ba3a5a792','银海软件/管理系统/组织人员管理/人员管理','3','user','','2',0,'1','1','0','1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('e43aeeaffbf9462dbc7bee0796b74834','5b658a734b39420c965f1bc68832c4fa','test',NULL,'sysmg','projectCommon/workTableComponents/analysis',70,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7b7f9cd1675a4b54b05c7c7cf0a7ac63/5b658a734b39420c965f1bc68832c4fa/e43aeeaffbf9462dbc7bee0796b74834','银海软件/管理系统/工作台模块/工作台组件/test','4',NULL,NULL,'2',1,'0','1','1','1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('e7542892ef424e809c3bb8cfa8c0051b','ea2297291cb24f8ba2eb01c2ae00850d','审核角色权限管理',NULL,'sysmg','authority.html#/adminAuthority',20,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/ea2297291cb24f8ba2eb01c2ae00850d/e7542892ef424e809c3bb8cfa8c0051b','银海软件/管理系统/审核管理/审核角色权限管理','3','','','2',0,'3','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('ea2297291cb24f8ba2eb01c2ae00850d','0415d44401b24605a21b589b6aaa349e','审核管理',NULL,'sysmg',NULL,'61','40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/ea2297291cb24f8ba2eb01c2ae00850d','银海软件/管理系统/审核管理','2','','','2',0,'3','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('ec56a0a43b09429482632cb61f7c6908','7459c1b525934151a1d309a304933644','功能资源管理','','sysmg','sysmg.html#/resourceManagement',0,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/7459c1b525934151a1d309a304933644/ec56a0a43b09429482632cb61f7c6908','银海软件/管理系统/资源管理/功能资源管理','3','compass','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('f2ce62655c724394a877518690469391','1d4e283ad5584e02811f6b188d3592bc','限流管理',NULL,'sysmg','sysmg.html#/rateLimit',160,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/f2ce62655c724394a877518690469391','银海软件/管理系统/系统管理/限流管理','3','setting',NULL,'2',0,'1','1','1','1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('fb8637c2e52e4b05bd2c07d742141ee7','1d4e283ad5584e02811f6b188d3592bc','分布式任务管理',NULL,'sysmg',NULL,50,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/fb8637c2e52e4b05bd2c07d742141ee7','银海软件/管理系统/系统管理/分布式任务管理','3','gold','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('fe8be18859b5478d8b76a7653f02e5eb','1d4e283ad5584e02811f6b188d3592bc','操作日志',NULL,'sysmg','audit.html#/operationLog',120,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/fe8be18859b5478d8b76a7653f02e5eb','银海软件/管理系统/系统管理/操作日志','3','form','','2',0,'1','1','1','1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);
INSERT INTO taresource(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, openmode, isfiledscontrol, createtime, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES ('ffa74f43e853441dac0ee90c787cb2e6','1d4e283ad5584e02811f6b188d3592bc','字典管理','','sysmg','sysmg.html#/dictionaryManager',30,'40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/ffa74f43e853441dac0ee90c787cb2e6','银海软件/管理系统/系统管理/字典管理','3','book','','2',0,'1','1',NULL,'1',NULL,sysdate,'1','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'0',NULL);

--  初始化接入系统
INSERT INTO taaccesssystem
(id,syscode,sysname,spell,protocol,domain,port,contextpath,portalsystem,menuservice,provider,effective,regtime,modifytime,backgroundaddress) VALUES ('e55b9b7046434d66bc11b94d553fada2','sysmg','管理系统','GLXT','http','localhost','8081','sysmg','1','111','ss','1',sysdate,sysdate,'***********:8083');

--  初始化菜单路径表taresourceurl
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('0aac95c1e73947bea41be639cc4e9036', 'ad0d709c33284be58a7232fa416d7bd3', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('0aac95c1e73947bea41be639cc4e9036', 'ba186bfbfb1647ffbe7db23e89686e9f', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('0da142c0f8444c1a85f2d861db818055', '0e78d360879a4bd38b3fa9c492dd106a', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('144a2bc62c5845b0b17019dd59954ef9', '7acb6d48d51f47fc9f9491b1234c0428', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('1b7f1d4202a841aaac9186c8772cfdac', '2f786a24ec574ed48368a9e633e07351', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('1b7f1d4202a841aaac9186c8772cfdac', '416f405ee4dc43d5b5c9f4bbbf6414ef', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('1b7f1d4202a841aaac9186c8772cfdac', '475f84c0bc3f4702a1e3d76de7e84f35', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('1b7f1d4202a841aaac9186c8772cfdac', '47a91cf250b8446c89cc98628adbcd31', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('1b7f1d4202a841aaac9186c8772cfdac', '832d3322479949d8a36da9458fa3bcbb', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('1b7f1d4202a841aaac9186c8772cfdac', '83ca67228517485fba1f73bffc17b8a6', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('1b7f1d4202a841aaac9186c8772cfdac', '9a749a3305844739a424feeb0962f0e9', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('1b7f1d4202a841aaac9186c8772cfdac', 'ce3009ef4f61445bb2ba1fe5a747e8be', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('1e706f26bc144c1da12022359c238053', '0cdb0e7d42b941e3bc99399aa69a0f89', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('1e706f26bc144c1da12022359c238053', '47a91cf250b8446c89cc98628adbcd31', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('1e706f26bc144c1da12022359c238053', '48503f8ac8ec49a8ab43daf32f7257c9', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('1e706f26bc144c1da12022359c238053', '4b3512dec0204cd19b7c5fbdd75a97fa', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('1e706f26bc144c1da12022359c238053', '6ae920d5f8ac419f96006055b6aab350', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('1e706f26bc144c1da12022359c238053', 'aa60a746c0a24640a06dea49a4d42572', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('25e6a452ed7a454ab3c757b5f8ff0a70', '4e90eb395b2e41ed81285b7ba9362b84', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('25e6a452ed7a454ab3c757b5f8ff0a70', 'fb72be8cb70f40caacc63510afc9e8b3', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('2ac775f74f934264ab1d43b5a42b56ef', '39683443f5e84c67913039e4c53133d1', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('2ac775f74f934264ab1d43b5a42b56ef', 'fb72be8cb70f40caacc63510afc9e8b3', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('2eb04d9c851240cf81cc38a73fcd19b4', 'b32b96d0142d4fea8d5fd8a58011c0c2', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('3dbde33722154503a7d22ac60f6a0e4e', '4b3512dec0204cd19b7c5fbdd75a97fa', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('3dbde33722154503a7d22ac60f6a0e4e', '634743c9057749748b82045ef69dd214', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('3dbde33722154503a7d22ac60f6a0e4e', 'a87e7b2776214a76ba31151c6c5cbbe5', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('3dbde33722154503a7d22ac60f6a0e4e', 'b091f4af3eba4c5c946af8dacaf951b9', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('3df588fc565d4287b3cefcd00a39cd91', '885e01d3494b44759c62b8b3568e6145', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('43f468b40c6c4c76a3a2fe4be903f4c7', '63daedc10cb9415da26c532ab9388ec8', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('43f468b40c6c4c76a3a2fe4be903f4c7', 'f2e756c387524382a6fa4d9f3db9abf7', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('43f468b40c6c4c76a3a2fe4be903f4c7', 'ff98da127f154939a0fdef59ec22f982', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('49296b8d9bd04786a8860e7011d4481f', 'f2e756c387524382a6fa4d9f3db9abf7', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('49296b8d9bd04786a8860e7011d4481f', 'fb72be8cb70f40caacc63510afc9e8b3', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('4b2eee0d7ded4e8094d4acf439fd3a1c', '7859157a0cd94c89995b0dde9fc40bc6', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('519e7153c57745edb884dd10ae7dccf5', '15f7e09d42124995bb44c18ce7b625de', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('519e7153c57745edb884dd10ae7dccf5', '2f786a24ec574ed48368a9e633e07351', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('519e7153c57745edb884dd10ae7dccf5', '416f405ee4dc43d5b5c9f4bbbf6414ef', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('519e7153c57745edb884dd10ae7dccf5', '475f84c0bc3f4702a1e3d76de7e84f35', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('519e7153c57745edb884dd10ae7dccf5', '47a91cf250b8446c89cc98628adbcd31', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('519e7153c57745edb884dd10ae7dccf5', '832d3322479949d8a36da9458fa3bcbb', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('519e7153c57745edb884dd10ae7dccf5', '83ca67228517485fba1f73bffc17b8a6', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('519e7153c57745edb884dd10ae7dccf5', '9a749a3305844739a424feeb0962f0e9', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('519e7153c57745edb884dd10ae7dccf5', 'ce3009ef4f61445bb2ba1fe5a747e8be', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('525a8cb155444e7f823cd070802f753f', '85ea1fc6a56a44e082eb8f20bb11faf3', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('525a8cb155444e7f823cd070802f753f', '8eec112446c445a5a8f4ebc6c3cf96f4', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('525a8cb155444e7f823cd070802f753f', '47a91cf250b8446c89cc98628adbcd31', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('525a8cb155444e7f823cd070802f753f', '676b9aca9d054d65aac3830436d2322e', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('525a8cb155444e7f823cd070802f753f', '9a749a3305844739a424feeb0962f0e9', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('525a8cb155444e7f823cd070802f753f', 'b32b96d0142d4fea8d5fd8a58011c0c2', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('525a8cb155444e7f823cd070802f753f', 'fc45635549b5487c87d0287490a79b5f', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('5611d1533d494a839c0be1e7a05da31f', '2277168fd5a046829fe3405d49802e8e', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('59a7fb9c459a4dd48d468f2add1d32b2', '58606a643d29437c9afcd69e8dd6482e', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('5ab7dcc091da41c785b7110d59f93a30', '246a43849d3c4394b9b3ae9bd099c778', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('5e67c7acef914c349d8aff076921f6b5', '2c3c1591444842e1aa1588a0aab998a8', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('60554e93387146bb9c7357907ba093fa', '00c38936f3224585948b2f4076856748', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('60554e93387146bb9c7357907ba093fa', '1a5380116d7b4068b8b149359e461e0f', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('60554e93387146bb9c7357907ba093fa', '2a6fc98ead7e4606a565943e2b6cdcb7', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('60554e93387146bb9c7357907ba093fa', '40d60f8d1a7c440c821cb7813dcad84f', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('60554e93387146bb9c7357907ba093fa', '83a48b7e93e94da78f768246aae6c84f', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('60554e93387146bb9c7357907ba093fa', 'fa83267e5a984eb0804c042b8989bc57', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('60554e93387146bb9c7357907ba093fa', 'fc810d0f2c9745da8ea370175ab3947a', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('61c2c5270e6b446d927d6a13d51abd3e', '7859157a0cd94c89995b0dde9fc40bc6', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('633e5a56c7ec447582b8fadf20b12d07', 'aa60a746c0a24640a06dea49a4d42572', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('722e1cc774a14178a488eb42ef4099de', '8545972435bb4abab8d2524fac510a04', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('72888507aba5484a8942e8dd0e6b6f7f', '0bd90473a1b64ae1ab705302bb6881fc', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('72888507aba5484a8942e8dd0e6b6f7f', '22392a8d036a4ad390cb754effb8183e', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('72888507aba5484a8942e8dd0e6b6f7f', '5855e399c9074b899850bb25ce1aaf76', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('72888507aba5484a8942e8dd0e6b6f7f', '7acb6d48d51f47fc9f9491b1234c0428', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('73dd0eb461784dfe8bb7d18616a83a09', '90ef822f9e644448be5705a9994a263b', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('73dd0eb461784dfe8bb7d18616a83a09', 'fe98234d9a0a4f2a9228743a58e86d54', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('76514eaf165e42c5838b1fbcf7993863', '10ce3706358e4127bb173427560244d8', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('76514eaf165e42c5838b1fbcf7993863', '71408b92effd4fcd99cd89f6b87bad57', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('76514eaf165e42c5838b1fbcf7993863', 'fb72be8cb70f40caacc63510afc9e8b3', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('781cf3051659466bb255cdac3a1078a3', 'e86bd08c7763451da3bdfcf161528df3', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('78fe48d6ab83452892dd10cd657d5f59', '15f7e09d42124995bb44c18ce7b625de', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('78fe48d6ab83452892dd10cd657d5f59', '2f786a24ec574ed48368a9e633e07351', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('78fe48d6ab83452892dd10cd657d5f59', '416f405ee4dc43d5b5c9f4bbbf6414ef', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('78fe48d6ab83452892dd10cd657d5f59', '475f84c0bc3f4702a1e3d76de7e84f35', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('78fe48d6ab83452892dd10cd657d5f59', '47a91cf250b8446c89cc98628adbcd31', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('78fe48d6ab83452892dd10cd657d5f59', '832d3322479949d8a36da9458fa3bcbb', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('78fe48d6ab83452892dd10cd657d5f59', '83ca67228517485fba1f73bffc17b8a6', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('78fe48d6ab83452892dd10cd657d5f59', '9a749a3305844739a424feeb0962f0e9', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('78fe48d6ab83452892dd10cd657d5f59', 'ce3009ef4f61445bb2ba1fe5a747e8be', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('78fe48d6ab83452892dd10cd657d5f59', 'fb72be8cb70f40caacc63510afc9e8b3', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('7c1dabd160974d8f90858c187cefa128', '321ac2aade66460dbea294f434dc4c9d', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('823c2134cae14d029f7db31e42b3451f', '253c2eae3e0046c2ba4f53f4ffa89ba3', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('877e407281dd48acb05a77fcb922bc73', 'c4360fdef9ad46278a677508ee8b290a', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('89cf6f31916d4919bb3995796866f8d7', '8545972435bb4abab8d2524fac510a04', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('8aa86ed4c7f84183935a262db4a605d3', 'cbcdd31ffb6c40499bb1af744851a783', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('8b00ee078064440cbd1ee34118ed2c52', '60f05ed2d53c4676978971a62ab5b885', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('8b00ee078064440cbd1ee34118ed2c52', '65062ea24d9f40e884314ad23429f717', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('8b00ee078064440cbd1ee34118ed2c52', '9b061fa118ec4954b6db899e1332d3ac', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('90e5730164b845898feb23c091140ea0', '58606a643d29437c9afcd69e8dd6482e', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('95bb9b749bf54e4692b0b1f14fd1b5ab', 'fc45635549b5487c87d0287490a79b5f', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('9c85db45a79c487abad5a3bf31c31ed4', 'aa60a746c0a24640a06dea49a4d42572', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('a0015ca02a574200afd7ca4178017c75', 'ba99b41be0c74fe6a650ee44524dfc0d', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('a3c94b4edf1e4e9d8665a81dc1c5f778', '47488be533b54ae99f0418ba3962e8b9', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('b2d888803a5e425fa59af5d247a87a6e', 'badc42944eb74d38a3ce254536e997c1', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('b2d888803a5e425fa59af5d247a87a6e', 'fb72be8cb70f40caacc63510afc9e8b3', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('b4e12d46211443069618302072200e71', '8f5b497d2ffc4ff6acad74ecbbfc77a2', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('bd9d0bba145c458e841aa9da0aeeb1d8', '676b9aca9d054d65aac3830436d2322e', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('bdcd5003137943299f69b36e55f2dc66', '0170b4dc35c34023a838c5f09aa68bd0', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('bdcd5003137943299f69b36e55f2dc66', '0d8cabb5ed624eed9686a87e3dbb5620', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('bf447212de284c79a0d73c658d0692b4', '3154d229a4244fda90903cb56f7da445', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('c2207fbb63c545cfb463924bfc6a378c', 'b288d7d44bbe4ace808f83aac39aa323', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('c2745b7cae7846acb9bcf8d0f4e836e8', '6b844095d25d44daba36670ed5d31bd7', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('c2745b7cae7846acb9bcf8d0f4e836e8', '7f621426782342faa371319c677f066e', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('c2745b7cae7846acb9bcf8d0f4e836e8', 'af0be155c6934ecd83d8cf62b36437e2', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('c578d9f8626d48f2971d7a18ac5281c5', '0e78d360879a4bd38b3fa9c492dd106a', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('cd49aa1e1a724404a4dfb4f290e1ed62', '3154d229a4244fda90903cb56f7da445', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('daceeff8a97b46cb9573b93ba3a5a792', '25bb2528eb9f44bf95bd740f7fd26d3b', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('daceeff8a97b46cb9573b93ba3a5a792', '47a91cf250b8446c89cc98628adbcd31', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('daceeff8a97b46cb9573b93ba3a5a792', 'a87e7b2776214a76ba31151c6c5cbbe5', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('daceeff8a97b46cb9573b93ba3a5a792', 'b32b96d0142d4fea8d5fd8a58011c0c2', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('daceeff8a97b46cb9573b93ba3a5a792', 'be02802280924df0a7549418caf28a00', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('e7542892ef424e809c3bb8cfa8c0051b', '42f7383e22bd468e9aeec2e58d8dea5f', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('ec56a0a43b09429482632cb61f7c6908', '1a5380116d7b4068b8b149359e461e0f', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('ec56a0a43b09429482632cb61f7c6908', '47a91cf250b8446c89cc98628adbcd31', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('ec56a0a43b09429482632cb61f7c6908', '821a8ca012154ba2a4c451918c99cfc0', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('ec56a0a43b09429482632cb61f7c6908', '83a48b7e93e94da78f768246aae6c84f', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('ec56a0a43b09429482632cb61f7c6908', 'fa83267e5a984eb0804c042b8989bc57', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('f2ce62655c724394a877518690469391', 'bf8aa724aada49c8b46102111af3d038', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('fe8be18859b5478d8b76a7653f02e5eb', '0b0456f381374b278c804a848bceebcc', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('fe8be18859b5478d8b76a7653f02e5eb', 'badc42944eb74d38a3ce254536e997c1', '0');
INSERT INTO taresourceurl (resourceid, urlid, authoritypolicy) VALUES ('ffa74f43e853441dac0ee90c787cb2e6', 'b88be2871149451a918264fd1ff468a3', '0');
--  初始化自定义组织类别名称
INSERT INTO tacustomorgtypename (customorgtypenameid, customorgtypename, effective, createuser, createtime, updatetime, destory, customorgtypenamecode, customorgtypenamedesc)VALUES ('baa315851d3c4a90a47889672404505d', '社保组织', '1', '1', sysdate, sysdate, '0', 'SBZZ', '这是社保组织描述');

-- 添加审核管理员权限菜单参数
INSERT INTO taparam(PARAMID, PARAMNAME, VALUESCOPE, CODE, PARAMDESC, VALUE, RESOURCEID) VALUES('4c991036c08a458899d31521c4146862', 'isExamine', '1,0', '', '是否', '1', 'e7542892ef424e809c3bb8cfa8c0051b');
CREATE TABLE taredissequence(
                             BIZ_TAG VARCHAR(36) NOT NULL,
                             START_INDEX numeric(20,0)  NOT NULL,
                             MAX_ID numeric(20,0)  NOT NULL,
                             STEP numeric(10,0)  NOT NULL,
                             PRIMARY KEY (BIZ_TAG)
);

COMMENT ON TABLE taredissequence IS 'REDIS 序列信息表';
COMMENT ON COLUMN taredissequence.BIZ_TAG IS '序列名称';
COMMENT ON COLUMN taredissequence.START_INDEX IS '起始值';
COMMENT ON COLUMN taredissequence.MAX_ID IS '最新值';
COMMENT ON COLUMN taredissequence.STEP IS '步长';

CREATE TABLE taworkernode(
                             ID numeric(10,0) NOT NULL,
                             HOST_NAME VARCHAR(36) NOT NULL,
                             PORT VARCHAR(36) NOT NULL,
                             PRIMARY KEY (ID)
);

COMMENT ON TABLE taworkernode IS 'SNOWFLOW 序列工作节点信息表';
COMMENT ON COLUMN taworkernode.ID IS '工作节点ID';
insert into TAREDISSEQUENCE(BIZ_TAG, START_INDEX, MAX_ID, STEP) values ('HIBERNATE_SEQUENCE',0,0,100);



-- 用户可管理字段标签类型字典
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('组织页面标签类型', 'EXTENDORGTAG', '组织信息', '1', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('组织页面标签类型', 'EXTENDORGTAG', '其他信息', '2', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('组织页面标签类型', 'EXTENDORGTAG', '扩展信息', '3', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('功能资源页面标签类型', 'EXTENDRESOURCETAG', '基本信息', '1', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('功能资源页面标签类型', 'EXTENDRESOURCETAG', '更多信息', '2', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('用户页面标签类型', 'EXTENDUSERTAG', '账号信息', '1', NULL, 10, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('用户页面标签类型', 'EXTENDUSERTAG', '人员信息', '2', NULL, 20, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('用户页面标签类型', 'EXTENDUSERTAG', '其他信息', '3', NULL, 30, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');



INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '更改使用权限有效时间', '90', NULL, 870, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '账号删除和角色的关联关系', '89', NULL, 860, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '账号新增和角色的关联关系', '88', NULL, 850, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '组织删除和角色的关联关系', '87', NULL, 840, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '组织新增和角色的关联关系', '86', NULL, 830, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '角色删除和组织的关联关系', '85', NULL, 820, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '角色新增和组织的关联关系', '84', NULL, 810, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '修改角色子组织可见性', '83', NULL, 800, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '更改账号自定义组织权限（收回）', '82', NULL, 790, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '更改账号自定义组织权限（授予）', '81', NULL, 780, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '管理员角色组织管理范围类型修改', '80', NULL, 770, '0', NULL, NULL, NULL, sysdate, '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');


DELETE FROM taresourceurl
WHERE RESOURCEID IN ('1b7f1d4202a841aaac9186c8772cfdac','519e7153c57745edb884dd10ae7dccf5','e7542892ef424e809c3bb8cfa8c0051b');
DELETE FROM taurl
WHERE URL like 'examine/taExamineRestService/%' or URL like 'domain/orguserauth/examineRoleRestService/%';
INSERT INTO taurl
(URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY)
VALUES('15f7e09d42124995bb44c18ce7b625de', '审核日志接口', 'examine/taExamineRestService/queryExamineLog', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', '2024-01-03 10:01:45', '1', NULL);

INSERT INTO taurl
(URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY)
VALUES('15f7e09d42124995bb44c18ce7b62501', '查询可管理字段', 'domain/orguserauth/configurableFieldsRestService/queryManageableFields', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', '2024-01-03 10:01:45', '1', NULL);

INSERT INTO taurl
(URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY)
VALUES('15f7e09d42124995bb44c18ce7b62502', '查询组织标签', 'domain/orguserauth/orgRestService/queryTags', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', '2024-01-03 10:01:45', '1', NULL);
INSERT INTO taurl
(URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY)
VALUES('475f84c0bc3f4702a1e3d76de7e84f35', '功能审核', 'examine/taExamineRestService/**', NULL, '0', '1', '2024-01-03 10:01:44', '1', NULL);

INSERT INTO taurl
(URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY)
VALUES('15f7e09d42124995bb44c18ce7b62503', '功能审核明细', 'examine/taExamineRestService/queryExamineDetail', NULL, '0', '1', '2024-01-03 10:01:44', '1', NULL);
INSERT INTO taurl
(URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY)
VALUES('42f7383e22bd468e9aeec2e58d8dea5f', '审核角色权限管理', 'domain/orguserauth/examineRoleRestService/**', NULL, '0', '1', '2024-01-03 10:01:56', '1', NULL);

INSERT INTO taresourceurl
(RESOURCEID, URLID, AUTHORITYPOLICY)
VALUES('1b7f1d4202a841aaac9186c8772cfdac', '475f84c0bc3f4702a1e3d76de7e84f35', '0');
INSERT INTO taresourceurl
(RESOURCEID, URLID, AUTHORITYPOLICY)
VALUES('e7542892ef424e809c3bb8cfa8c0051b', '42f7383e22bd468e9aeec2e58d8dea5f', '0');
INSERT INTO taresourceurl
(RESOURCEID, URLID, AUTHORITYPOLICY)
VALUES('1b7f1d4202a841aaac9186c8772cfdac', '15f7e09d42124995bb44c18ce7b62501', '0');
INSERT INTO taresourceurl
(RESOURCEID, URLID, AUTHORITYPOLICY)
VALUES('1b7f1d4202a841aaac9186c8772cfdac', '15f7e09d42124995bb44c18ce7b62502', '0');
INSERT INTO taresourceurl
(RESOURCEID, URLID, AUTHORITYPOLICY)
VALUES('519e7153c57745edb884dd10ae7dccf5', '15f7e09d42124995bb44c18ce7b625de', '0');
INSERT INTO taresourceurl
(RESOURCEID, URLID, AUTHORITYPOLICY)
VALUES('519e7153c57745edb884dd10ae7dccf5', '15f7e09d42124995bb44c18ce7b62502', '0');
INSERT INTO taresourceurl
(RESOURCEID, URLID, AUTHORITYPOLICY)
VALUES('519e7153c57745edb884dd10ae7dccf5', '15f7e09d42124995bb44c18ce7b62501', '0');
INSERT INTO taresourceurl
(RESOURCEID, URLID, AUTHORITYPOLICY)
VALUES('519e7153c57745edb884dd10ae7dccf5', '15f7e09d42124995bb44c18ce7b62503', '0');
INSERT INTO taresourceurl
(RESOURCEID, URLID, AUTHORITYPOLICY)
VALUES('78fe48d6ab83452892dd10cd657d5f59', '15f7e09d42124995bb44c18ce7b62501', '0');
INSERT INTO taresourceurl
(RESOURCEID, URLID, AUTHORITYPOLICY)
VALUES('78fe48d6ab83452892dd10cd657d5f59', '15f7e09d42124995bb44c18ce7b62502', '0');
INSERT INTO taresourceurl
(RESOURCEID, URLID, AUTHORITYPOLICY)
VALUES('78fe48d6ab83452892dd10cd657d5f59', '15f7e09d42124995bb44c18ce7b62503', '0');


UPDATE taresource
SET PRESOURCEID='ea2297291cb24f8ba2eb01c2ae00850d', NAME='功能审核', CODE=NULL, SYSCODE='sysmg', URL='authority.html#/examineManagement', orderno=10, IDPATH='40337bdecb19484ebeb39d6c21aaca26/ea2297291cb24f8ba2eb01c2ae00850d/1b7f1d4202a841aaac9186c8772cfdac', NAMEPATH='银海软件/审核管理/功能审核', RESOURCELEVEL='2', ICON=NULL, ICONCOLOR=NULL, SECURITYPOLICY='2', securitylevel=0, RESOURCETYPE='3', EFFECTIVE='1', ISDISPLAY=NULL, OPENMODE='1', ISFILEDSCONTROL=NULL, CREATETIME='2023-12-04 17:20:07', CREATEUSER='1', UIAUTHORITYPOLICY='0', FIELD01=NULL, FIELD02=NULL, FIELD03=NULL, FIELD04=NULL, FIELD05=NULL, FIELD06=NULL, FIELD07=NULL, FIELD08=NULL, FIELD09=NULL, FIELD10=NULL, workbench='0', IMAGE=NULL
WHERE RESOURCEID='1b7f1d4202a841aaac9186c8772cfdac';
UPDATE taresource
SET PRESOURCEID='ea2297291cb24f8ba2eb01c2ae00850d', NAME='功能审核日志', CODE=NULL, SYSCODE='sysmg', URL='authority.html#/examineManagementLog', orderno=30, IDPATH='40337bdecb19484ebeb39d6c21aaca26/ea2297291cb24f8ba2eb01c2ae00850d/519e7153c57745edb884dd10ae7dccf5', NAMEPATH='银海软件/审核管理/功能审核日志', RESOURCELEVEL='2', ICON=NULL, ICONCOLOR=NULL, SECURITYPOLICY='2', securitylevel=0, RESOURCETYPE='3', EFFECTIVE='1', ISDISPLAY='1', OPENMODE='1', ISFILEDSCONTROL=NULL, CREATETIME='2023-12-04 17:20:07', CREATEUSER='1', UIAUTHORITYPOLICY='0', FIELD01=NULL, FIELD02=NULL, FIELD03=NULL, FIELD04=NULL, FIELD05=NULL, FIELD06=NULL, FIELD07=NULL, FIELD08=NULL, FIELD09=NULL, FIELD10=NULL, workbench='0', IMAGE=NULL
WHERE RESOURCEID='519e7153c57745edb884dd10ae7dccf5';
UPDATE taresource
SET PRESOURCEID='ea2297291cb24f8ba2eb01c2ae00850d', NAME='审核角色权限管理', CODE=NULL, SYSCODE='sysmg', URL='authority.html#/adminAuthority', orderno=20, IDPATH='40337bdecb19484ebeb39d6c21aaca26/ea2297291cb24f8ba2eb01c2ae00850d/e7542892ef424e809c3bb8cfa8c0051b', NAMEPATH='银海软件/审核管理/审核角色权限管理', RESOURCELEVEL='2', ICON=NULL, ICONCOLOR=NULL, SECURITYPOLICY='2', securitylevel=0, RESOURCETYPE='3', EFFECTIVE='1', ISDISPLAY=NULL, OPENMODE='1', ISFILEDSCONTROL=NULL, CREATETIME='2023-12-04 17:20:09', CREATEUSER='1', UIAUTHORITYPOLICY='0', FIELD01=NULL, FIELD02=NULL, FIELD03=NULL, FIELD04=NULL, FIELD05=NULL, FIELD06=NULL, FIELD07=NULL, FIELD08=NULL, FIELD09=NULL, FIELD10=NULL, workbench='0', IMAGE=NULL
WHERE RESOURCEID='e7542892ef424e809c3bb8cfa8c0051b';
UPDATE taresource
SET PRESOURCEID='40337bdecb19484ebeb39d6c21aaca26', NAME='审核管理', CODE=NULL, SYSCODE='sysmg', URL=NULL, orderno=61, IDPATH='40337bdecb19484ebeb39d6c21aaca26/ea2297291cb24f8ba2eb01c2ae00850d', NAMEPATH='银海软件/审核管理', RESOURCELEVEL='1', ICON=NULL, ICONCOLOR=NULL, SECURITYPOLICY='2', securitylevel=0, RESOURCETYPE='3', EFFECTIVE='1', ISDISPLAY=NULL, OPENMODE='1', ISFILEDSCONTROL=NULL, CREATETIME='2023-12-04 17:20:09', CREATEUSER='1', UIAUTHORITYPOLICY='0', FIELD01=NULL, FIELD02=NULL, FIELD03=NULL, FIELD04=NULL, FIELD05=NULL, FIELD06=NULL, FIELD07=NULL, FIELD08=NULL, FIELD09=NULL, FIELD10=NULL, workbench='0', IMAGE=NULL
WHERE RESOURCEID='ea2297291cb24f8ba2eb01c2ae00850d';



INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '回收人员信息', '96', NULL, 960, '0', NULL, NULL, NULL, '2024-06-04 14:56:25', '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '删除人员信息', '95', NULL, 950, '0', NULL, NULL, NULL, '2024-06-04 14:56:09', '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '启用人员信息', '94', NULL, 940, '0', NULL, NULL, NULL, '2024-06-04 14:55:55', '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '禁用人员信息', '93', NULL, 930, '0', NULL, NULL, NULL, '2024-06-04 14:55:40', '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '编辑人员信息', '92', NULL, 920, '0', NULL, NULL, NULL, '2024-06-04 14:55:22', '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '添加人员信息', '91', NULL, 910, '0', NULL, NULL, NULL, '2024-06-04 14:55:04', '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, TYPE, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, SYSTEM, NEWTYPE)
VALUES('操作对象类型', 'OPOBJTYPE', '人员信息', '12', NULL, 120, '0', NULL, NULL, NULL, '2024-06-04 14:53:59', '1', '1', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
