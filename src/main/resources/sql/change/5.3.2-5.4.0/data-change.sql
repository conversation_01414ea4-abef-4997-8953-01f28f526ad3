-- 删除旧taurl表原始数据（5.3.2版本）
DELETE FROM taurl WHERE ID IN (
                               'ec651e93d17d45b4a2179efa40e3d218' ,
                               'e21705b74aa14db3a01388c30ec36c4c' ,
                               '76214a306e0e44f8b8f19eae5fa0dd1c' ,
                               '0941e708ddac4155b7d4d1b6a1fc2e8e' ,
                               'e0d0c362e8f7483680c5daf3fc2bb987' ,
                               '90765bc3d2e74ae7b4adf1f7fa97a204' ,
                               '0c53f4294d9b40699e7fa440af6b7ad0' ,
                               '9fc1eaec4a364bada22396dc233faa63' ,
                               '46caf3a83bb34d248e86cb2b7d6e0ed8' ,
                               'ca6ae64039b34b61ac33444db8586583' ,
                               '8fe5b7e8c0564c1183964b3ef47de089' ,
                               'b309d59e4888483c9491f836a673edb2' ,
                               '45fea7f87ddf4f419e5c1e5b7b356f2a' ,
                               '5251eff95b944e96937d43afdd41823a' ,
                               'ea59a4b2d0c04678b6845dc2ff58e5f0' ,
                               '571beb27637e4a02a40b30b01eb977a1' ,
                               '1ba2429d2cf741eaa651d135cecfccfa' ,
                               '8f4c664e4eac467e823cca1dae037afa' ,
                               '195ea431cbda47599912e600593916d9' ,
                               '44432ccd6aee42d5b8e7a5fa333335a6' ,
                               '0e78d360879a4bd38b3fa9c492dd106a' ,
                               'a8897d46eef944efbfee9693dc0566b2' ,
                               'c16ef6c06f0a4ee8a47a8d853171a7ff' ,
                               '7b5dbd9796374fb4818ef6f992fb90cd' ,
                               '1f662d581613479f9cf7438aad814f6e' ,
                               'b32f17c028cc44ada9d2a8472e608b33' ,
                               '475f84c0bc3f4702a1e3d76de7e84f35' ,
                               'c48caa74d4614c63892fbae774a6c113' ,
                               'fc88cde084394fac9fabf7d39268df37' ,
                               '7e13bb8bad344530abc0c997e6f29c6a' ,
                               '15f7e09d42124995bb44c18ce7b625de' ,
                               '0181353f447e466ba81475d7c265aa89' ,
                               '39683443f5e84c67913039e4c53133d1' ,
                               'f90d3e743f6f4505b139b089eb05e5bc' ,
                               '79c8a0452bb84d439ac3461d40f7df01' ,
                               'f588153009394645a497f2897ba79eb4' ,
                               '0a5d58ba4cf54e4eae6d1157c3193e47' ,
                               'a03fe56632dd4e779b0196812344f86f' ,
                               'fedb18b63f474b63aeb93007c3d9c843' ,
                               '40a218c972bb4dd7b3c3e71a265e2de1' ,
                               '880da059725c4b06a624a13d7e0a1d59' ,
                               '2a54d340e21b43238c5013ce0534c85a' ,
                               '821a8ca012154ba2a4c451918c99cfc0' ,
                               '3910f6a32f034404a92b4278645702c8' ,
                               '40d60f8d1a7c440c821cb7813dcad84f' ,
                               '00c38936f3224585948b2f4076856748' ,
                               '2a6fc98ead7e4606a565943e2b6cdcb7' ,
                               'adbcb239a961413a91190a1a6e1a9687' ,
                               'fa4b47a3941e4fba9eff97249987e4d9' ,
                               '4753a58f0d1b4770996784b7f4ce15f9' ,
                               '774535576eb142078ec9f589fa0166cf' ,
                               '83564254d96c4a0196d3397347737cf9' ,
                               'd2610e8b0c6144b0af4ea3a647d8649b' ,
                               '33ebb96ce401430a9b630ea9bf7b19ce' ,
                               '16fa889f1e634f46aa1c19bef83bef5c' ,
                               'cbc0fbecfe4442d893d7681752d6e5eb' ,
                               '10ce3706358e4127bb173427560244d8' ,
                               '847d0576ff39421686a6094185ba93ef' ,
                               '71408b92effd4fcd99cd89f6b87bad57' ,
                               '885e01d3494b44759c62b8b3568e6145' ,
                               '5831fba3851241fdb990311b6f2d2c58' ,
                               '9d67b3d6337a4e488a1c1f71a57cd1cb' ,
                               'f019482965424846a344c23762921af3' ,
                               'f1ade00ea79541218e3c59ac0382cdbb' ,
                               '32f19d310a1246eeb4804c947d1480b1' ,
                               'c3f485a5d431487081927fea7bbdd6aa' ,
                               '2ec2c5d9963d4f868cffa9889eb80b6a' ,
                               'c69bb98ae17f410eaa0862ff61c7c6dc' ,
                               '626ddca64fac430d898bce60fe90fa54' ,
                               '42f7383e22bd468e9aeec2e58d8dea5f' ,
                               '8b757b398c7145dca303538859299971' ,
                               'c53a34cb93bc4c75afc865231914ba9f' ,
                               'bfc657fcb614420f94bb5bdf4c425510' ,
                               '20700551c33b437c806b93bc92dc3c9d' ,
                               'cc844a5e23424fbfaf5d421b6b8658b0' ,
                               '3e199ec988a64522a61baabcd852470d' ,
                               '77f13730212647509751b06c8bb3dd27' ,
                               '9f84c0c4803a40af9624e58197906f78' ,
                               '2e34a4c83bba440abf1e65c47a1f64c1' ,
                               '99b13867dfcc45d4bacd7c6917f729dc' ,
                               '80de460d98984d96952d99e39a0a4fab' ,
                               'd44c860bee374ab69fb9e5e60516c28d' ,
                               'afa0c8bace664c238d31ddde03c17d7a' ,
                               '76ac5cd69dbe4a0c8ab844510679957c' ,
                               '195c1e0a469547f8a37befdabea14a5e' ,
                               'd709efa9492e41c985be89583d026227' ,
                               '72901308421145c88a469675d425afad' ,
                               '323d4e5765604460b96311bfaac9fc01' ,
                               'ed3c1f3b34334c758ddafb3235cc1a74' ,
                               '28341963475b40e2982f13375a7bed27' ,
                               'fc810d0f2c9745da8ea370175ab3947a' ,
                               '18d5c9e17bbb496b80ccf6d587d2922e' ,
                               'a19c95b79a694e2abad612a888d0baf7' ,
                               'ad0d709c33284be58a7232fa416d7bd3' ,
                               '5a41ab45c651433ba0a2623e0be9391c' ,
                               '7acb6d48d51f47fc9f9491b1234c0428' ,
                               '2ec14b8ed9a04a9f85cc803d99ff0b18' ,
                               '0d5c35c9b02340f2ad37397c76f36e4f' ,
                               'd93202ddd70e4f1da4d4d70a37e1710c' ,
                               'd0bc87eb27f3428686eef131314eb98e' ,
                               '22392a8d036a4ad390cb754effb8183e' ,
                               '67fe92e1f2f74a9ea1947dac0cf7447b' ,
                               'b1396cf02957493cb9dba1530863dbf6' ,
                               '3220d5b0942e432daf3c073ca360122b' ,
                               '6ad4d47081004f7598d4132264383268' ,
                               '9d58b8eb62ea424287eb7562dfaaa837' ,
                               '58412f36f1c64e719d03d7bf5bba9ca6' ,
                               '795af47695b84dcd8c3bc5e6c4c8eccf' ,
                               'b88be2871149451a918264fd1ff468a3' ,
                               'd1759dc661d640d2ad559ce4a6ece974' ,
                               '4371f9f5b6f64f7490a11f5b0f46611b' ,
                               '5b6eb06323f341ff90d735805d22e938' ,
                               '6dc3fe02dcc549648eb2b5472c75b133' ,
                               '159be0ed8e7e4107b085ab9d1020592d' ,
                               '54cebfdf34c54e0191915d14f5e52ba0' ,
                               '45d43aee748d4ed2894b1f812f62360e' ,
                               'dedea13dd9dd42b7aba71f1526ac7fe8' ,
                               'd1471eda4afc45a4b915d2100fe18af9' ,
                               '7faf8e17e5e846e5b10e1fc42ecbe6c1' ,
                               '2b9bb0e2179f43179d41f4fc7b22bf0e' ,
                               'e9bc69a3bca94eccbe3e4286bf2863bf' ,
                               '08cd4ed593114680bfa8a9826936c3bd' ,
                               'a6e96e7ce7ca4b5f862e1df5b19d0a20' ,
                               '35a9fdc228314981b740a17ac3041863' ,
                               '583c6b33a74c467aac7f67f364181450' ,
                               'bbcf4cf809324ffdbf9cbe95b649b652' ,
                               'e53d524ae7884251b9551e5bd690ab20' ,
                               'a7b3a24aa28d415891f67a7deae318d1' ,
                               'af83691612354bb8933f8e3e9107df4e' ,
                               '206adb9da5cf481f927097e4457a4381' ,
                               'fe182cce9ff34370b62b0dacfc74679c' ,
                               '1013f293306948acb3b0d600da8983ec' ,
                               '135ccd2cabaf4a6cac8c3ab5c4f49071' ,
                               'e476368003a64ea49113e1f699ebf44a' ,
                               'f5b7a9571ca640038ac5ae5f7fdfe242' ,
                               '7c08e928b3e146eea3729b1ac0222376' ,
                               '3c72d73f18624014a3935c2d7f3e2a4c' ,
                               'a384ae7998c64307b98c3071747bab59' ,
                               '9bf4ec13940041efbc48932c2a0ffe8e' ,
                               'b091f4af3eba4c5c946af8dacaf951b9' ,
                               'ca28dad434e54bf1a9b8f21e63f54b2b' ,
                               '20800affa5404857aed051b36cec9717' ,
                               'f617dc2ccb8a481b8e13b814f3447bf9' ,
                               '7949676d97104f89bfd8fb9475977b07' ,
                               '9bc0b432d45c455392eb781a8f465f0d' ,
                               '63daedc10cb9415da26c532ab9388ec8' ,
                               'd492053e1cd943a190d4e33c1d284890' ,
                               '986ee8c346f84f258658734af5ef494b' ,
                               'a8806d67cc434c0382a170ef5ff5ded2' ,
                               '611174d293ac4027b5f148fd677df4e3' ,
                               '93bb62a65b7b43eca20ea91b43d9ebe1' ,
                               '37e80fb9e192462fa2146e0a6ccce592' ,
                               '2a06ec82aced450a9360f88083e7269d' ,
                               '28efc47547b94deca7fb745a3ccd9f0a' ,
                               '9eb3d2c727814de5936c9b409de8835c' ,
                               '1fc681db43df49caa2e1c0c03eca04a2' ,
                               'dfc350b510624f79b12305e3fe7a8308' ,
                               'f70bb418d762405f8df422ddd05f4692' ,
                               '0ed2e37e987e47a9b995bab04dd00001' ,
                               '1f759706bf5548d3ae362cac46f9f39b' ,
                               '09961b00d2b34b748daa794515e3d8d6' ,
                               '8a5bf6151747497ea819f0dec6f2ac33' ,
                               '912d99f0d32f4f819206e7d2f2b66df2' ,
                               '2277168fd5a046829fe3405d49802e8e' ,
                               'fa83267e5a984eb0804c042b8989bc57' ,
                               'af7e74c0348947e691972de795c8f1aa' ,
                               '1a5380116d7b4068b8b149359e461e0f' ,
                               '3381997ccaac4df68639008bd103a3c0' ,
                               '13a1953a5f7e4527a25950b63c7bf3d4' ,
                               '2fd8146f001c4e7f979669bf36e4f1a2' ,
                               '2a23726d511b46c7b19a350706148868' ,
                               '5e09b74efacf4862beda8a21bb8cbc9a' ,
                               '83a48b7e93e94da78f768246aae6c84f' ,
                               'ba186bfbfb1647ffbe7db23e89686e9f' ,
                               '3e09b57045fb4ea5b8b7c28b9714febf' ,
                               '8949d73e53794d359ebcbf57f41359ee' ,
                               'f969b32f0f2640189237fb82687f2a71' ,
                               '00a74d89066a47989407b208230b1709' ,
                               '38a33deff4e044b999ca4bde56a53ea8' ,
                               'd0e7351f05ce4b58a6fca29a19c70847' ,
                               '417153883b114bc796c78f4420b14bb9' ,
                               '42bc8aea3378498e81c53334b06050ac' ,
                               '518fb0e03af64326ae3f6d1ea6da1fad' ,
                               '3154d229a4244fda90903cb56f7da445' ,
                               '658ea1202bc843e99e0201aa5d079b00' ,
                               'f91c291fd2f741f5b2eb52d3f882c153' ,
                               '6426f3aad7c648f68377d1e346c02624' ,
                               '15de371347e94563b28214db9cf37c02' ,
                               'aa60a746c0a24640a06dea49a4d42572' ,
                               'ec9eda6ecc7a4e6c8d0f6e2e8a0472bc' ,
                               'a87e7b2776214a76ba31151c6c5cbbe5' ,
                               '5316d8f69b98419b8fcf6f96002f5e0e' ,
                               '2fdcb068e93c428d8cf612cb92c65418' ,
                               '2f786a24ec574ed48368a9e633e07351' ,
                               '03ae618da1fd4bf0a3a5b3ef31ea586f' ,
                               'd8202e6246994e8f89e1c8c55752f8f0' ,
                               '314abaf6c856464194670bee9edf3e3a' ,
                               '1e49a3453812453891c46331a4b91f46' ,
                               'a2d72ab0507241d19290cbb6fee6817e' ,
                               '88c9acc56ac24b61a003073c68828c03' ,
                               'cbcdd31ffb6c40499bb1af744851a783' ,
                               'e83bb87d573246e5b6c2ed534c2cd775' ,
                               'e14986416987402c9283b0dfabacb379' ,
                               'e62f249ce1af41b7bb286d0e745d1471' ,
                               '877de3c0984e456ea1bbf52541a1d0f4' ,
                               '8ff682c3168a4b1295ed1efd32467321' ,
                               'a911f4d576124857a80ddc37873d90e8' ,
                               '37e4e66df7de4a87b14a19e8c4f44ef6' ,
                               'b05f5e9f2bb048d9adf335ab62a78cfd' ,
                               '3efc328fe5fa40e38ff29b7f97deb709' ,
                               '0cd7696d40ad49ad91f6ce91911fa2aa' ,
                               'cab374d4f2af4cadaf487532c29df3ff' ,
                               '1ce091c3b3604be2932c25df07a642cd' ,
                               '20232206695a4aaa851e5d30a08ce295' ,
                               '2cb89bd084f74184a15e8313ad5b5647' ,
                               '93acb3693a4c4dc8a47c4d0240f00890' ,
                               '697ec7337fc44943872e5fd47a6efdea' ,
                               '26ae2d0f31404495ad8c6d3bf8df9da1' ,
                               '59bc6ad7451f47e5929a42d98986be98' ,
                               '7ef0b9f372fd44a69ef79cdb968ec994' ,
                               '60509043f699448ab9f5812e2333a11d' ,
                               '9fcef2129d1c47e58631bf196a003d3b' ,
                               '90ef822f9e644448be5705a9994a263b' ,
                               '41f4b754edd14be79ee108d40c061a8a' ,
                               '8d54ec9a7cbb4f1a9c3f4c54e7d341c8' ,
                               'bb63ae66fd6d4807948948106d410f89' ,
                               'c59aaa9e3f1f4dcb961a90be46ea9b72' ,
                               'fcf51d71136d4c2897b736f11af9266f' ,
                               '941c10445bcc40369cb4b55e468aaf48' ,
                               '213caa8621494ce98a55bef675dd39f2' ,
                               '8cb4565415ce47bba1d7be36cbabf40e' ,
                               'e0338aeb38164edb893d21c9af0bee43' ,
                               '77f6d7ab67654023a997ba31bcef5262' ,
                               '22d46236ba834a1f93fe128dd9861f6b' ,
                               'bbaef041a14c49f8963f1b6589a28d69' ,
                               '9cf0537f574d472fb2df4211a2e83d88' ,
                               'b1fd82d7707645869a4088703dd260f1' ,
                               '9466add8d6e3449aab05fbbf0bda43cc' ,
                               '03c3c8302674456f989739369d35fedc' ,
                               '1e334b5580fb40588b41833ace89ece0' ,
                               '96d86e0b87264ee29424151636d50667' ,
                               '84dc082ef9a94cb6a70955ded2229848' ,
                               'a196f610e78645488b830ca21d5d73d2' ,
                               '6cb53bacf6e84b2d89eb732cf29b683d' ,
                               '49a5ee26819f4dc9934ff23b905b249f' ,
                               '404773da30b843a4b70da215f73a9384' ,
                               'feeec1113f5d49b2bb19eca44702b035' ,
                               'fb72be8cb70f40caacc63510afc9e8b3' ,
                               '72e8e1bdf40d4350b8addd7132952c5d' ,
                               '993e777e680341cb86fe70437ca1150f' ,
                               '8a19872cf40e4078922ae712924102f7' ,
                               '0b0456f381374b278c804a848bceebcc' ,
                               '909e509a59ec430d9eb239d3265f03f5' ,
                               'ff98da127f154939a0fdef59ec22f982' ,
                               '7abfb7bd84994c0db08eb9976adfd7d8' ,
                               'dec7f275ce6242b0a591ccd371474db7' ,
                               '653ec11f00e8473fbad26ea69098e047' ,
                               'aa56d8e0833b4fffad76945a4133f4d5' ,
                               '8c965431a61145fb98f7fa67b7271f76' ,
                               '3cf89df828154febac4345a00ecd867b' ,
                               'fc45635549b5487c87d0287490a79b5f' ,
                               'e2273cbc1ffb47afb24c2c7ec24b3633' ,
                               '624bde4ff89e4630b220f9808b5f07e2' ,
                               'a14c3e447ba044aaa5275e85ee026f0d' ,
                               '87566f8084c14e70a76f622abef88ee9' ,
                               'f08165aeb751488cb3c42d8679d92ad1' ,
                               'd2e57270fb64454b937915cead646bf5' ,
                               '2b147317777f4f728c659abfea82db57' ,
                               '6d73b1f0ab7742e4b21caef223799261' ,
                               '95d6fa234ee848059a815b9c14287737' ,
                               '2d69486d5fdc4336be2411e125a561c0' ,
                               'badc42944eb74d38a3ce254536e997c1' ,
                               '26010f3c6ec04bf59680ec7f11109e54' ,
                               '366d45447b244c2b8f3a6cefec338ae0' ,
                               'fe98234d9a0a4f2a9228743a58e86d54' ,
                               '31d3fcf575694d36b19d79488d10a719' ,
                               '55f139c1352846f185d1115d6d38bb2b' ,
                               '8786b8c546af4a049e5c166872fb69fb' ,
                               '598b36d8554649c19f7e88e087347c7e' ,
                               '74a7d5a9739841809c3b0db1079a4ba8' ,
                               '081da1f5cc5a41fbbdb89e5423e2908f' ,
                               '98396599f9e1494e9e99bca94de11a94' ,
                               '138ecb6ff95e4b58991a0f2f5d1deb3b' ,
                               '19b678a580b94d5d9e38d8fa1ab32124' ,
                               'bbc17b9c19374ae0b050f77da4e23636' ,
                               '9a749a3305844739a424feeb0962f0e9' ,
                               '9e367251a5fd4c658648b2b0bc177b03' ,
                               'c3aac3c6d27844aaa36831f0b05199a9' ,
                               '5165c4afb9a546f69555b139f194ba36' ,
                               '01fa335e75e8405b9161c14be68bdfba' ,
                               '501b8e57bfac491eab3ea302bd0d1577' ,
                               '0f450ebdc9de4e8897402109ee1faa6d' ,
                               'ce3009ef4f61445bb2ba1fe5a747e8be' ,
                               'c335de0f745049439eea4ed4ef5e8220' ,
                               'f6f90c9794c547e89ac15b1c1e2cd1c5' ,
                               'fc5a759500054f00a5016f68ba687ab2' ,
                               'daab8c8fac94444e8f73f670166b6123' ,
                               '1186646d85324d318e02899ca0f9508b' ,
                               'bdba806884544d378c6a12f912a90e69' ,
                               '155d58691b514a79824a95968dda38e6' ,
                               'c4a5ece4fa5b414e98958972572c88b9' ,
                               '690b9acefd6e45f6bc5214646bb7d434' ,
                               '832d3322479949d8a36da9458fa3bcbb' ,
                               'e65ee0ad6e4e4e29a37d8d1f6d952ede' ,
                               'a5ff0d74a0084769b8e767cc51491c75' ,
                               '7f5017d989ad4f1e828bced19467ee2a' ,
                               '14ad6adb99464b0c811e3a0c307efdfd' ,
                               'c65e391b3372485aa45fddd997cab064' ,
                               'd3a3b173b8c04b5a9cef0e82e2920310' ,
                               '9a520b63f24c437c8861080a5526f23b' ,
                               '43900d1b828d4c8b950612fcae10a786' ,
                               '52c52f7e65c84c6da2c1e7da75824182' ,
                               'ed2bf752425d4ac580d73f99615990ab' ,
                               '5f77fb7579294f02908bde7aaec43ece' ,
                               'c4360fdef9ad46278a677508ee8b290a' ,
                               '16e2788f54794c1d9fda4036b0ebe85f' ,
                               'c6428dc1dc144d4c8efb9b0e44dc17d5' ,
                               '5b43aa7145734290803796c1b9070d49' ,
                               '6ec810ccee18431bb058d6440c4476f7' ,
                               '1272585f9ed944eab717939300cadbb8' ,
                               '3a0b491b01674d9fab78498550320cd0' ,
                               'b7ea5b5a2bdc462aa14e8aca7961d61f' ,
                               'aef954306f5d4c6d9b5e507aa3f175c6' ,
                               '9d4b47c0490640008c31e892314520fc' ,
                               '58606a643d29437c9afcd69e8dd6482e' ,
                               '6b844095d25d44daba36670ed5d31bd7' ,
                               '3bc7226a89454115b2c09d10aff96749' ,
                               'c9b473e0bd044a579aef3a73d4ffc3cf' ,
                               '3974a2411df2404ab1e0afa08653da95' ,
                               'f9de9a6d93e94f31ac446d213c5782fa' ,
                               '807ef17eaa8446188acbf92929171c56' ,
                               '2c3c1591444842e1aa1588a0aab998a8' ,
                               '8cea2eccc40c4c418e43f4321ee970c3' ,
                               '83ca67228517485fba1f73bffc17b8a6' ,
                               '83a2c0425f3d469d92f24e7043a5d0f7' ,
                               '09ec9781ec834b079889bcf60fcd7388' ,
                               '0bd90473a1b64ae1ab705302bb6881fc' ,
                               'b20b7169663b49d6b548a34e0d128b25' ,
                               '8d9b9595a4f84d28b5cad358335a37f7' ,
                               '6e135682e38140a3bc423ff6406119d3' ,
                               '80ee0be75f024dee80412a61c3284256' ,
                               'd105c3c113bf4188afd27fe2a2347a08' ,
                               '302d500716e34c3490523c3093201174' ,
                               '4b9d98dd01dd479d942b7d9fee2b8eae' ,
                               'db1104eba4874533b3042af4651e9c75' ,
                               '97615539a3fe4dceb66a01ddaa5b4b4b' ,
                               '80018c73459347e0bf7cb9f089179e05' ,
                               '3568ca57a1bf4600a38b716982600625' ,
                               'd9d0f34456c24b44a6efa5634a6f115c' ,
                               'd23f6f951ce5490dbe54dce47b1acfb1' ,
                               '59064e87d0294f5097e8bb192ab4e98c' ,
                               'b32b96d0142d4fea8d5fd8a58011c0c2' ,
                               '72fc1e15bee64231beee40b2d482aa43' ,
                               'b94fc48142fe4130b29757a07f385aeb' ,
                               '634743c9057749748b82045ef69dd214' ,
                               '5861ad2003b94d8ab0d730112a098fae' ,
                               '02484f5c350544b5b16ead7293c3558e' ,
                               'bdbe704910d8436fa2fe57985ff20431' ,
                               'b4bfe4f72bc54f5fb6d431053323ff7d' ,
                               'a8d04425958c452eb9461a93867884ac' ,
                               'ab213f6723aa4e37bd2ce5597928fd53' ,
                               '06cd55b18e1f44adbdeded3fca1355f7' ,
                               '90829994ecaa4a0ca78c8ff564f2753b' ,
                               'b39bb0d6720d4764b2cd0224f3659a80' ,
                               '24cee81a613f416a8649d159931cede2' ,
                               '5f575442b1f84595a1c05aef61f85765' ,
                               '25a2a2f205114fab819203012b99fa68' ,
                               '203d2e7109ec4853997e2cfb462f5501' ,
                               '1e9ea424827e475c89f036ab1f85137e' ,
                               'a1fa23f246904da9a3198966bbdd2122' ,
                               'f2df6f27fcb945b39f263962a387aa16' ,
                               'a50c6b036e164d5daaba6a33881d5370' ,
                               'a272cfdac31c4af593d644e951b80a80' ,
                               'eb848308e574462e8d3bc9acb9d5defd' ,
                               '02bbbed032b44a52ae6c5e82ec45d83a' ,
                               '0aae6ab603b442429a9ca0b29396da80' ,
                               '4b19cb3a440e4e3f9f6f34de34e6d695' ,
                               'f157f212127b49659461b34a0c23eb95' ,
                               '45fc94a23742400d9a681ac08695c246' ,
                               'a213d1db5b6b4e08b204b701c0476024' ,
                               '09024f31af1341a7b6c19c38359fcca1' ,
                               '4bf5eb1024ea41e09fdd8e714ea57492' ,
                               'e8ac1a55c9db46d8a38d0c43f424a0bf' ,
                               '2a2eb9bfce6d42168eaa271fa3a001b8' ,
                               '448d31215eba49ecb975f1800ed00f79' ,
                               '8b11d3374b344f6292b2e13794b14fd7' ,
                               '7ffbbd9552954860b058a29f27dc55f0' ,
                               'af0be155c6934ecd83d8cf62b36437e2' ,
                               '98c8e94f284e4cc19a66fb60ba04056d' ,
                               '91a7ffff45334096b124645f4f29974f' ,
                               '2e457c6060bd411eb65bae7068d3d5d9' ,
                               '89d993cfaedb4c5db40fcf1bbe4a96ae' ,
                               '7859157a0cd94c89995b0dde9fc40bc6' ,
                               '6cbf93b5f4b346b890f104756c55d078' ,
                               'e0819670288c4a8991872a398894488e' ,
                               '0cdb0e7d42b941e3bc99399aa69a0f89' ,
                               '6a73d3aa6f5949caa1f4c536ffe01400' ,
                               'c1b1724998ed41aeaecd8dafa167278a' ,
                               'd072f9cf46f24a52ba5fb68f7e393c21' ,
                               '243a1cf716ed49dcae4f105ef450aa45' ,
                               'b8d148426f6746b1b76f36d11c059dbf' ,
                               'f2e756c387524382a6fa4d9f3db9abf7' ,
                               '676b9aca9d054d65aac3830436d2322e' ,
                               'b771b6243ac04ddc8f9e496c175f8f52' ,
                               'd3dd51b3f42141b490aab2a4b76dba44' ,
                               'bbde0232aa2c41b59593666c95186ab6' ,
                               '47a91cf250b8446c89cc98628adbcd31' ,
                               '8545972435bb4abab8d2524fac510a04' ,
                               '5855e399c9074b899850bb25ce1aaf76' ,
                               'c7ae73b1a4cd4ad8a4b4e2f199bad322' ,
                               'ef46133fe6b04b04b0a5e7481d92b2ea' ,
                               '9c99e327ead94f96a0799377d8aea579' ,
                               'fd4b922d759a448e894c489336b9443e' ,
                               '7ac1c71173f2421ea634f0e274303521' ,
                               '670804d52f2c473bb58ad6c87ceefbf9' ,
                               '072db1f4c4a848cd938d95b4057ff4c0' ,
                               '7ff6d8f6a9994e7cb6710b4edd1d109c' ,
                               'e546d678a75740c0be0e9a0914d3fac4' ,
                               '4b3512dec0204cd19b7c5fbdd75a97fa' ,
                               '416f405ee4dc43d5b5c9f4bbbf6414ef' ,
                               'd3db2fb5724d4839b04ff4260174a70d' ,
                               '415a0c59d499478da3b40fc896470569' ,
                               'efaecd1281c64c89ba87363bf1c18900' ,
                               '7af7009339d145c1967e8d46a4b194d1' ,
                               '321ac2aade66460dbea294f434dc4c9d' ,
                               'e34479764c66437e993c27072a13dfca' ,
                               '72fb390446eb475cbefd28e0a26041c0' ,
                               '89872c1e30e543e1ae0352c0b1ecfb72' ,
                               '0e4c73815f2e43deaadbb0646cc9ae7c' ,
                               'ffffdb7e67b74234a3a230abd06e4d32' ,
                               '7f621426782342faa371319c677f066e' ,
                               'f757b23bbfbe461b8478e5ad456ec64f' ,
                               'b94b3fd3ce4f4e89860a3b0b00f985b4' ,
                               '80d490da38044ad58c96da08e6e58efd' ,
                               '8c54473a278248628dcad204a57ebd9a' ,
                               '4e90eb395b2e41ed81285b7ba9362b84' ,
                               '498a9c21ba1d4042b85b2d0b8d4ec61f' ,
                               '20cf287cd36448d4ba6b6930ea472824' ,
                               '314759f094014f24b991d467378d1755' ,
                               '253c2eae3e0046c2ba4f53f4ffa89ba3' ,
                               'd9a5e31b83b141279b387bc4dfac0713' ,
                               '66ee423b80f84407a952e4edf2d27901' ,
                               '2133136c1fca4fa79ed4c5b960bf914c' ,
                               '8d50ff41b6aa42c383f119cc4dd9abf6' ,
                               '59ea5a521dd1439f839466603c76302a' ,
                               'c433ed5a2a1340f6a0063637861de279' ,
                               '632895cc5cd14dbdb76d9c10fffd0786' ,
                               '60bd58fe73d0472881349ecbfdd0bd29' );


-- 新增5.4.0版本url
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e21705b74aa14db3a01388c30ec36c4c', '审计列表', 'audit/taAuditResService/queryAuditList', 'fe98234d9a0a4f2a9228743a58e86d54', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('76214a306e0e44f8b8f19eae5fa0dd1c', '异步查询自定义权限信息', 'domain/orguserauth/roleAgentRestService/queryCustomUsePermissionAsync', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0941e708ddac4155b7d4d1b6a1fc2e8e', '查询能分配权限的角色', 'domain/orguserauth/roleRestService/queryAuthRole', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e0d0c362e8f7483680c5daf3fc2bb987', '更新管理员自定义权限信息', 'domain/orguserauth/adminRoleRestService/changeCustomResourceUsePermission', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('90765bc3d2e74ae7b4adf1f7fa97a204', '查询账户信息', 'domain/orguserauth/TaUserManagementRestService/getUserByUserId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0c53f4294d9b40699e7fa440af6b7ad0', '更新自定义权限有效时间', 'domain/orguserauth/adminRoleRestService/updateCustomResourceUsePermissionEffectiveTime', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9fc1eaec4a364bada22396dc233faa63', '重置配置', 'tasysconfig/taSysConfigRestService/refreshSysConfig', '3154d229a4244fda90903cb56f7da445', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('46caf3a83bb34d248e86cb2b7d6e0ed8', '在线日志分析查询', 'logmg/loginLog/loginLogAnalysisRestService/online', '39683443f5e84c67913039e4c53133d1', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ca6ae64039b34b61ac33444db8586583', '登录历史查询', 'logmg/loginLog/loginLogAnalysisRestService/login', '39683443f5e84c67913039e4c53133d1', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8fe5b7e8c0564c1183964b3ef47de089', '删除人员对话', 'message/removeChat', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b309d59e4888483c9491f836a673edb2', '标记私信已读', 'message/readLetters', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('45fea7f87ddf4f419e5c1e5b7b356f2a', '查询用户通知', 'message/queryUserNotice', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5251eff95b944e96937d43afdd41823a', '查询组织下用户', 'message/queryOrgUser', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ea59a4b2d0c04678b6845dc2ff58e5f0', '查询一条配置数据', 'tasysconfig/taSysConfigRestService/selectOne', '3154d229a4244fda90903cb56f7da445', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('571beb27637e4a02a40b30b01eb977a1', '审核日志图', 'examine/taExamineRestService/examineChart', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1ba2429d2cf741eaa651d135cecfccfa', '批量拒绝审核', 'examine/taExamineRestService/batchRefusePass', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8f4c664e4eac467e823cca1dae037afa', '查询审核单条数据', 'examine/taExamineRestService/selectOne', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('195ea431cbda47599912e600593916d9', '批量审核通过', 'examine/taExamineRestService/batchExamineSomeone', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('44432ccd6aee42d5b8e7a5fa333335a6', '字典服务', 'codetable/getCode', null, '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0e78d360879a4bd38b3fa9c492dd106a', '分布式任务数据源', 'jobmg/elasticjob/jobDatasourceManagementRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a8897d46eef944efbfee9693dc0566b2', '查询数据源', 'jobmg/elasticjob/jobDatasourceManagementRestService/getJobDatasource', '0e78d360879a4bd38b3fa9c492dd106a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c16ef6c06f0a4ee8a47a8d853171a7ff', '新增数据源', 'jobmg/elasticjob/jobDatasourceManagementRestService/addJobDatasource', '0e78d360879a4bd38b3fa9c492dd106a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7b5dbd9796374fb4818ef6f992fb90cd', '删除数据源', 'jobmg/elasticjob/jobDatasourceManagementRestService/deleteJobDatasource', '0e78d360879a4bd38b3fa9c492dd106a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1f662d581613479f9cf7438aad814f6e', '获取作业执行历史轨迹', 'jobmg/elasticjob/jobDatasourceManagementRestService/getJobExecutionTrace', '0e78d360879a4bd38b3fa9c492dd106a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b32f17c028cc44ada9d2a8472e608b33', '获取作业历史状态', 'jobmg/elasticjob/jobDatasourceManagementRestService/getJobStatusTrace', '0e78d360879a4bd38b3fa9c492dd106a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('475f84c0bc3f4702a1e3d76de7e84f35', '功能审核', 'examine/taExamineRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c48caa74d4614c63892fbae774a6c113', '通过审核', 'examine/taExamineRestService/examineSomeone', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fc88cde084394fac9fabf7d39268df37', '拒绝审核', 'examine/taExamineRestService/refusePass', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7e13bb8bad344530abc0c997e6f29c6a', '分页审核列表', 'examine/taExamineRestService/queryExamine', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('15f7e09d42124995bb44c18ce7b625de', '审核日志接口', 'examine/taExamineRestService/queryExamineLog', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0181353f447e466ba81475d7c265aa89', '查看审核细节', 'examine/taExamineRestService/queryExamineDetail', '475f84c0bc3f4702a1e3d76de7e84f35', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('39683443f5e84c67913039e4c53133d1', '登录日志分析', 'logmg/loginLog/loginLogAnalysisRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f90d3e743f6f4505b139b089eb05e5bc', '获取数据库时间', 'logmg/loginLog/loginLogAnalysisRestService/getSysTime', '39683443f5e84c67913039e4c53133d1', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('79c8a0452bb84d439ac3461d40f7df01', '查询所有在线人员信息', 'logmg/loginLog/loginLogAnalysisRestService/getOnlineInfo', '39683443f5e84c67913039e4c53133d1', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f588153009394645a497f2897ba79eb4', '查询所有人员登录历史信息', 'logmg/loginLog/loginLogAnalysisRestService/getLoginInfo', '39683443f5e84c67913039e4c53133d1', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0a5d58ba4cf54e4eae6d1157c3193e47', '查询在线时点信息', 'logmg/loginLog/loginLogAnalysisRestService/analysisOnlineStatInfo', '39683443f5e84c67913039e4c53133d1', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a03fe56632dd4e779b0196812344f86f', '分析登录客户端系统情况', 'logmg/loginLog/loginLogAnalysisRestService/analysisClientSystemInfo', '39683443f5e84c67913039e4c53133d1', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fedb18b63f474b63aeb93007c3d9c843', '分析登录客户端分辨率情况', 'logmg/loginLog/loginLogAnalysisRestService/analysisClientScreenInfo', '39683443f5e84c67913039e4c53133d1', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('40a218c972bb4dd7b3c3e71a265e2de1', '分析登录客户端分辨率情况', 'logmg/loginLog/loginLogAnalysisRestService/analysisClientBrowserInfo', '39683443f5e84c67913039e4c53133d1', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('880da059725c4b06a624a13d7e0a1d59', '运行环境详细查询', 'logmg/loginLog/loginLogAnalysisRestService/queryLoginEnvironmentDetail', '39683443f5e84c67913039e4c53133d1', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2a54d340e21b43238c5013ce0534c85a', '查询时点登录信息', 'logmg/loginLog/loginLogAnalysisRestService/analysisLoginStatInfo', '39683443f5e84c67913039e4c53133d1', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('821a8ca012154ba2a4c451918c99cfc0', '功能资源管理', 'domain/orguserauth/resourceManagementRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3910f6a32f034404a92b4278645702c8', '查询功能资源信息', 'domain/orguserauth/resourceManagementRestService/queryResourceByResourceID', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('40d60f8d1a7c440c821cb7813dcad84f', '更新功能资源', 'domain/orguserauth/resourceManagementRestService/updateResourceByResourceId', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('00c38936f3224585948b2f4076856748', '批量删除功能资源', 'domain/orguserauth/resourceManagementRestService/deleteResourceByResourceIds', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2a6fc98ead7e4606a565943e2b6cdcb7', '新增功能资源', 'domain/orguserauth/resourceManagementRestService/addResource', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('adbcb239a961413a91190a1a6e1a9687', '根据参数查询功能资源', 'domain/orguserauth/resourceManagementRestService/queryTaResourceByParameters', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fa4b47a3941e4fba9eff97249987e4d9', '查询接入系统列表', 'domain/orguserauth/resourceManagementRestService/queryAllAccessSystem', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('4753a58f0d1b4770996784b7f4ce15f9', '禁用功能资源', 'domain/orguserauth/resourceManagementRestService/disabledResourceByResourceIds', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('774535576eb142078ec9f589fa0166cf', '启用功能资源', 'domain/orguserauth/resourceManagementRestService/enabledResourceByResourceIds', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('83564254d96c4a0196d3397347737cf9', '删除行政区划数据', 'domain/orguserauth/areaAggregateRestService/deleteAreaById', '7859157a0cd94c89995b0dde9fc40bc6', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d2610e8b0c6144b0af4ea3a647d8649b', '根据条件查询行政区划数据', 'domain/orguserauth/areaAggregateRestService/queryAreaByCondition', '7859157a0cd94c89995b0dde9fc40bc6', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('33ebb96ce401430a9b630ea9bf7b19ce', '批量更新行政区划有效状态', 'domain/orguserauth/areaAggregateRestService/updateBatchEffectiveByAreaIdPath', '7859157a0cd94c89995b0dde9fc40bc6', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('16fa889f1e634f46aa1c19bef83bef5c', '查询行政区划数据', 'domain/orguserauth/areaAggregateRestService/queryAreaByAreaId', '7859157a0cd94c89995b0dde9fc40bc6', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('cbc0fbecfe4442d893d7681752d6e5eb', '批量删除行政区划数据', 'domain/orguserauth/areaAggregateRestService/deleteBatchAreaByAreaIds', '7859157a0cd94c89995b0dde9fc40bc6', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('10ce3706358e4127bb173427560244d8', '访问限制日志', 'logMg/accessLog/taAccessDenyLogRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('847d0576ff39421686a6094185ba93ef', '访问受限列表', 'logMg/accessLog/taAccessDenyLogRestService/queryAccessDenyLog', '10ce3706358e4127bb173427560244d8', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('71408b92effd4fcd99cd89f6b87bad57', '访问受限实时分析', 'logMg/accessLog/taAccessDenyLogRestService/analysisAccessDenyInfoInterval', '10ce3706358e4127bb173427560244d8', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('885e01d3494b44759c62b8b3568e6145', '标签管理', 'domain/orguserauth/tagRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9d67b3d6337a4e488a1c1f71a57cd1cb', '批量删除标签', 'domain/orguserauth/tagRestService/deleteBatchTag', '885e01d3494b44759c62b8b3568e6145', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f019482965424846a344c23762921af3', '查询配置类型', 'tasysconfig/taSysConfigRestService/queryConfigCategory', '3154d229a4244fda90903cb56f7da445', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f1ade00ea79541218e3c59ac0382cdbb', '查询标签', 'domain/orguserauth/tagRestService/queryTag', '885e01d3494b44759c62b8b3568e6145', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('32f19d310a1246eeb4804c947d1480b1', '新增标签', 'domain/orguserauth/tagRestService/addTag', '885e01d3494b44759c62b8b3568e6145', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c3f485a5d431487081927fea7bbdd6aa', '更新标签', 'domain/orguserauth/tagRestService/updateTag', '885e01d3494b44759c62b8b3568e6145', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c69bb98ae17f410eaa0862ff61c7c6dc', '批量更新标签状态', 'domain/orguserauth/tagRestService/updateBatchTagStatus', '885e01d3494b44759c62b8b3568e6145', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('626ddca64fac430d898bce60fe90fa54', '根据查询条件查询标签', 'domain/orguserauth/tagRestService/queryTagByCondition', '885e01d3494b44759c62b8b3568e6145', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('42f7383e22bd468e9aeec2e58d8dea5f', '审核角色权限管理', 'domain/orguserauth/examineRoleRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8b757b398c7145dca303538859299971', '增加人员角色关联关系', 'domain/orguserauth/examineRoleRestService/addBatchUserRole', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c53a34cb93bc4c75afc865231914ba9f', '删除角色人员关联关系', 'domain/orguserauth/examineRoleRestService/deleteBatchRoleUsers', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('bfc657fcb614420f94bb5bdf4c425510', '删除人员角色关联关系', 'domain/orguserauth/examineRoleRestService/deleteBatchUserRole', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('20700551c33b437c806b93bc92dc3c9d', '查询可分配的人员', 'domain/orguserauth/examineRoleRestService/queryUsersNoWraperByRoleId', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('cc844a5e23424fbfaf5d421b6b8658b0', '查询可分配的角色', 'domain/orguserauth/examineRoleRestService/queryNoWrapperRolesByUserId', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3e199ec988a64522a61baabcd852470d', '批量删除审核角色', 'domain/orguserauth/examineRoleRestService/deleteBatchRole', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('77f13730212647509751b06c8bb3dd27', '异步查询组织树', 'domain/orguserauth/examineRoleRestService/queryCurrentAdminRoleWrapOrgTree', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9f84c0c4803a40af9624e58197906f78', '查询已关联的角色', 'domain/orguserauth/examineRoleRestService/queryRolesByUserId', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2e34a4c83bba440abf1e65c47a1f64c1', '异步查询组织权限树', 'domain/orguserauth/examineRoleRestService/queryOrgAuthTreeByAsync', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('99b13867dfcc45d4bacd7c6917f729dc', '查询人员列表', 'domain/orguserauth/examineRoleRestService/queryUserByCondition', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('80de460d98984d96952d99e39a0a4fab', '新增组织权限', 'domain/orguserauth/examineRoleRestService/addOrgAuth', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d44c860bee374ab69fb9e5e60516c28d', '启用审核角色', 'domain/orguserauth/examineRoleRestService/enableExaminer', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('afa0c8bace664c238d31ddde03c17d7a', '查询审核角色', 'domain/orguserauth/examineRoleRestService/queryExaminer', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('76ac5cd69dbe4a0c8ab844510679957c', '查询角色的组织权限', 'domain/orguserauth/examineRoleRestService/queryOrgAuth', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('195c1e0a469547f8a37befdabea14a5e', '获取页面(数据+url)', 'review/getPage', '583c6b33a74c467aac7f67f364181450', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d709efa9492e41c985be89583d026227', '禁用审核角色', 'domain/orguserauth/examineRoleRestService/unableExaminer', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('72901308421145c88a469675d425afad', '移除组织权限', 'domain/orguserauth/examineRoleRestService/removeOrgScope', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('323d4e5765604460b96311bfaac9fc01', '更新审核角色', 'domain/orguserauth/examineRoleRestService/updateAdmin', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ed3c1f3b34334c758ddafb3235cc1a74', '新增审核角色', 'domain/orguserauth/examineRoleRestService/addAdminRole', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('28341963475b40e2982f13375a7bed27', '增加角色人员关联关系', 'domain/orguserauth/examineRoleRestService/addBatchRoleUsers', '42f7383e22bd468e9aeec2e58d8dea5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fc810d0f2c9745da8ea370175ab3947a', '工作台管理', 'domain/orguserauth/workTableManageRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('18d5c9e17bbb496b80ccf6d587d2922e', '更新工作台组件的有效性', 'domain/orguserauth/workTableManageRestService/updateStatus', 'fc810d0f2c9745da8ea370175ab3947a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a19c95b79a694e2abad612a888d0baf7', '查询所有工作台资源', 'domain/orguserauth/workTableManageRestService/queryResource', 'fc810d0f2c9745da8ea370175ab3947a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ad0d709c33284be58a7232fa416d7bd3', '查询所有的角色', 'domain/orguserauth/workTableManageRestService/queryRole', 'fc810d0f2c9745da8ea370175ab3947a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5a41ab45c651433ba0a2623e0be9391c', '获取添加工作台组件的默认参数', 'domain/orguserauth/workTableManageRestService/queryDefaultValue', 'fc810d0f2c9745da8ea370175ab3947a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7acb6d48d51f47fc9f9491b1234c0428', '分布式任务ZK配置', 'jobmg/elasticjob/zookeeperAddressManagementRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2ec14b8ed9a04a9f85cc803d99ff0b18', '查询注册中心信息', 'jobmg/elasticjob/zookeeperAddressManagementRestService/getZookeeperRegistryCenter', '7acb6d48d51f47fc9f9491b1234c0428', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0d5c35c9b02340f2ad37397c76f36e4f', '新增注册中心', 'jobmg/elasticjob/zookeeperAddressManagementRestService/addZookeeperRegistryCenter', '7acb6d48d51f47fc9f9491b1234c0428', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d93202ddd70e4f1da4d4d70a37e1710c', '删除注册中心', 'jobmg/elasticjob/zookeeperAddressManagementRestService/deleteZookeeperRegistryCenter', '7acb6d48d51f47fc9f9491b1234c0428', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d0bc87eb27f3428686eef131314eb98e', '连接注册中心', 'jobmg/elasticjob/zookeeperAddressManagementRestService/connectZookeeperRegistryCenter', '7acb6d48d51f47fc9f9491b1234c0428', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('22392a8d036a4ad390cb754effb8183e', '分布式任务闲忙任务管理', 'jobmg/elasticjob/freeBusyJobManagerRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('67fe92e1f2f74a9ea1947dac0cf7447b', '查询非闲忙任务的任务', 'jobmg/elasticjob/freeBusyJobManagerRestService/getJobNameByZkId', '22392a8d036a4ad390cb754effb8183e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b1396cf02957493cb9dba1530863dbf6', '查询闲忙任务', 'jobmg/elasticjob/freeBusyJobManagerRestService/getFreeBusyJob', '22392a8d036a4ad390cb754effb8183e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3220d5b0942e432daf3c073ca360122b', '新增闲忙任务', 'jobmg/elasticjob/freeBusyJobManagerRestService/addFreeBusyJob', '22392a8d036a4ad390cb754effb8183e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6ad4d47081004f7598d4132264383268', '查询任务运行的服务器IP', 'jobmg/elasticjob/freeBusyJobManagerRestService/getServerIpsByJobName', '22392a8d036a4ad390cb754effb8183e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9d58b8eb62ea424287eb7562dfaaa837', '更新闲忙任务', 'jobmg/elasticjob/freeBusyJobManagerRestService/updateFreeBusyJob', '22392a8d036a4ad390cb754effb8183e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('58412f36f1c64e719d03d7bf5bba9ca6', '查询任务运行的所有服务器IP', 'jobmg/elasticjob/freeBusyJobManagerRestService/getAllServerIpsByJobName', '22392a8d036a4ad390cb754effb8183e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('795af47695b84dcd8c3bc5e6c4c8eccf', '删除闲忙任务', 'jobmg/elasticjob/freeBusyJobManagerRestService/deleteFreeBusyJob', '22392a8d036a4ad390cb754effb8183e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b88be2871149451a918264fd1ff468a3', '字典管理', 'dictmg/dictMgRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d1759dc661d640d2ad559ce4a6ece974', '保存字典类型', 'dictmg/dictMgRestService/saveType', 'b88be2871149451a918264fd1ff468a3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('4371f9f5b6f64f7490a11f5b0f46611b', '更新字典', 'dictmg/dictMgRestService/updateDict', 'b88be2871149451a918264fd1ff468a3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5b6eb06323f341ff90d735805d22e938', '批量删除字典', 'dictmg/dictMgRestService/deleteBatchDict', 'b88be2871149451a918264fd1ff468a3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6dc3fe02dcc549648eb2b5472c75b133', '根据类型禁用字典', 'dictmg/dictMgRestService/stopDictByType', 'b88be2871149451a918264fd1ff468a3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('159be0ed8e7e4107b085ab9d1020592d', '根据类型删除字典', 'dictmg/dictMgRestService/deleteDictByType', 'b88be2871149451a918264fd1ff468a3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('54cebfdf34c54e0191915d14f5e52ba0', '查询字典内容', 'dictmg/dictMgRestService/queryDictContent', 'b88be2871149451a918264fd1ff468a3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('45d43aee748d4ed2894b1f812f62360e', '查询字典信息', 'dictmg/dictMgRestService/queryDictInfo', 'b88be2871149451a918264fd1ff468a3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('dedea13dd9dd42b7aba71f1526ac7fe8', '批量启用字典', 'dictmg/dictMgRestService/startBatchDict', 'b88be2871149451a918264fd1ff468a3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d1471eda4afc45a4b915d2100fe18af9', '根据类型启用字典', 'dictmg/dictMgRestService/startDictByType', 'b88be2871149451a918264fd1ff468a3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7faf8e17e5e846e5b10e1fc42ecbe6c1', '根据字典类型查询字典', 'dictmg/dictMgRestService/queryDictByType', 'b88be2871149451a918264fd1ff468a3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2b9bb0e2179f43179d41f4fc7b22bf0e', '保存字典', 'dictmg/dictMgRestService/saveDict', 'b88be2871149451a918264fd1ff468a3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e9bc69a3bca94eccbe3e4286bf2863bf', '批量禁用字典', 'dictmg/dictMgRestService/stopBatchDict', 'b88be2871149451a918264fd1ff468a3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('08cd4ed593114680bfa8a9826936c3bd', '查询字典类型列表', 'dictmg/dictMgRestService/queryType', 'b88be2871149451a918264fd1ff468a3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a6e96e7ce7ca4b5f862e1df5b19d0a20', '查询字典权限标识列表', 'dictmg/dictMgRestService/queryAuthorityList', 'b88be2871149451a918264fd1ff468a3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('35a9fdc228314981b740a17ac3041863', '刷新字典缓存', 'dictmg/dictMgRestService/refreshDictByType', 'b88be2871149451a918264fd1ff468a3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('583c6b33a74c467aac7f67f364181450', '页面还原', 'review/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('bbcf4cf809324ffdbf9cbe95b649b652', '保存页面', 'review/savePage', '583c6b33a74c467aac7f67f364181450', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e53d524ae7884251b9551e5bd690ab20', '获取页面原Url', 'review/getPageUrl', '583c6b33a74c467aac7f67f364181450', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a7b3a24aa28d415891f67a7deae318d1', '获取已保存的页面', 'review/queryPageReview', '583c6b33a74c467aac7f67f364181450', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('af83691612354bb8933f8e3e9107df4e', '获取页面数据', 'review/getPageData', '583c6b33a74c467aac7f67f364181450', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('206adb9da5cf481f927097e4457a4381', '获取注册到zk的服务器信息', 'jobmg/elasticjob/serverOperateRestService/getServerInfo', '5855e399c9074b899850bb25ce1aaf76', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fe182cce9ff34370b62b0dacfc74679c', '失效注册到zk的服务器', 'jobmg/elasticjob/serverOperateRestService/disableServer', '5855e399c9074b899850bb25ce1aaf76', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1013f293306948acb3b0d600da8983ec', '生效注册到zk的服务器', 'jobmg/elasticjob/serverOperateRestService/enableServer', '5855e399c9074b899850bb25ce1aaf76', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('135ccd2cabaf4a6cac8c3ab5c4f49071', '终止注册到zk的服务器', 'jobmg/elasticjob/serverOperateRestService/shutdownServer', '5855e399c9074b899850bb25ce1aaf76', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e476368003a64ea49113e1f699ebf44a', '删除注册到zk的服务器', 'jobmg/elasticjob/serverOperateRestService/removeServer', '5855e399c9074b899850bb25ce1aaf76', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f5b7a9571ca640038ac5ae5f7fdfe242', '删除作业', 'jobmg/elasticjob/serverOperateRestService/removeServerJob', '5855e399c9074b899850bb25ce1aaf76', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7c08e928b3e146eea3729b1ac0222376', '使作业生效', 'jobmg/elasticjob/serverOperateRestService/enableServerJob', '5855e399c9074b899850bb25ce1aaf76', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3c72d73f18624014a3935c2d7f3e2a4c', '终止作业', 'jobmg/elasticjob/serverOperateRestService/shutdownServerJob', '5855e399c9074b899850bb25ce1aaf76', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a384ae7998c64307b98c3071747bab59', '作业失效', 'jobmg/elasticjob/serverOperateRestService/disabledServerJob', '5855e399c9074b899850bb25ce1aaf76', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9bf4ec13940041efbc48932c2a0ffe8e', '获取作业详情', 'jobmg/elasticjob/serverOperateRestService/getServerJobDetail', '5855e399c9074b899850bb25ce1aaf76', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b091f4af3eba4c5c946af8dacaf951b9', '自定义组织管理', 'domain/orguserauth/customOrgAggregateRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ca28dad434e54bf1a9b8f21e63f54b2b', '更新自定义组织', 'domain/orguserauth/customOrgAggregateRestService/updateCustomOrg', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('20800affa5404857aed051b36cec9717', '新增自定义组织', 'domain/orguserauth/customOrgAggregateRestService/addCustomOrg', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f617dc2ccb8a481b8e13b814f3447bf9', '根据条件查询自定义组织类别名称', 'domain/orguserauth/customOrgAggregateRestService/queryCustomOrgTypeNameByCondition', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7949676d97104f89bfd8fb9475977b07', '批量删除自定义组织', 'domain/orguserauth/customOrgAggregateRestService/deleteBatchCustomOrg', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9bc0b432d45c455392eb781a8f465f0d', '批量更新自定义组织有效状态', 'domain/orguserauth/customOrgAggregateRestService/updateBatchCustomOrgStatus', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('63daedc10cb9415da26c532ab9388ec8', '异常日志查询', 'audit/auditQueryRestService/exception', '90ef822f9e644448be5705a9994a263b', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d492053e1cd943a190d4e33c1d284890', '在线日志查询', 'audit/auditQueryRestService/online', '90ef822f9e644448be5705a9994a263b', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('986ee8c346f84f258658734af5ef494b', '登录日志查询', 'audit/auditQueryRestService/login', '90ef822f9e644448be5705a9994a263b', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a8806d67cc434c0382a170ef5ff5ded2', '审计日志查询', 'audit/auditQueryRestService/audit', '90ef822f9e644448be5705a9994a263b', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('611174d293ac4027b5f148fd677df4e3', '动态服务执行路径', 'rest/', '3cf89df828154febac4345a00ecd867b', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('93bb62a65b7b43eca20ea91b43d9ebe1', '查询下一级自定义组织', 'domain/orguserauth/customOrgAggregateRestService/queryCustomOrgByParentId', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2a06ec82aced450a9360f88083e7269d', '根据有效状态查询自定义组织', 'domain/orguserauth/customOrgAggregateRestService/queryCustomOrgByValidId', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9eb3d2c727814de5936c9b409de8835c', '根据自定义组织类别删除自定义组织', 'domain/orguserauth/customOrgAggregateRestService/deleteBatchCustomOrgTypeName', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1fc681db43df49caa2e1c0c03eca04a2', '新增润乾模板', 'tarunqianresource/taRunqianResourceRestService/addRunqianResource', '8545972435bb4abab8d2524fac510a04', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('dfc350b510624f79b12305e3fe7a8308', '修改润乾模板', 'tarunqianresource/taRunqianResourceRestService/editRunqianResource', '8545972435bb4abab8d2524fac510a04', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f70bb418d762405f8df422ddd05f4692', '下载润乾模板', 'tarunqianresource/taRunqianResourceRestService/downloadRunqianResource', '8545972435bb4abab8d2524fac510a04', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0ed2e37e987e47a9b995bab04dd00001', '删除润乾模板', 'tarunqianresource/taRunqianResourceRestService/delRunqianResource', '8545972435bb4abab8d2524fac510a04', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1f759706bf5548d3ae362cac46f9f39b', '查询润乾模板', 'tarunqianresource/taRunqianResourceRestService/queryRunqianResource', '8545972435bb4abab8d2524fac510a04', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('09961b00d2b34b748daa794515e3d8d6', '查询自定义组织类别', 'domain/orguserauth/customOrgAggregateRestService/queryCustomOrgTypeName', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2277168fd5a046829fe3405d49802e8e', 'URL资源管理', 'domain/orguserauth/url/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fa83267e5a984eb0804c042b8989bc57', '根据参数查询URL', 'domain/orguserauth/url/queryByParam', '2277168fd5a046829fe3405d49802e8e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('af7e74c0348947e691972de795c8f1aa', '查询URL信息', 'domain/orguserauth/url/queryUrl', '2277168fd5a046829fe3405d49802e8e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1a5380116d7b4068b8b149359e461e0f', '查询命名空间', 'domain/orguserauth/url/queryNamespace', '2277168fd5a046829fe3405d49802e8e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3381997ccaac4df68639008bd103a3c0', '更新URL', 'domain/orguserauth/url/updateUrl', '2277168fd5a046829fe3405d49802e8e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('13a1953a5f7e4527a25950b63c7bf3d4', '保存URL', 'domain/orguserauth/url/saveUrl', '2277168fd5a046829fe3405d49802e8e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2fd8146f001c4e7f979669bf36e4f1a2', '批量禁用URL', 'domain/orguserauth/url/disableBatchUrl', '2277168fd5a046829fe3405d49802e8e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2a23726d511b46c7b19a350706148868', '批量启用URL', 'domain/orguserauth/url/enableBatchUrl', '2277168fd5a046829fe3405d49802e8e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5e09b74efacf4862beda8a21bb8cbc9a', '批量删除URL', 'domain/orguserauth/url/deleteBatchUrl', '2277168fd5a046829fe3405d49802e8e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('83a48b7e93e94da78f768246aae6c84f', '根据命名空间查询URL', 'domain/orguserauth/url/queryUrlByNamespace', '2277168fd5a046829fe3405d49802e8e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ba186bfbfb1647ffbe7db23e89686e9f', '个人工作台服务', 'domain/orguserauth/workbenchRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3e09b57045fb4ea5b8b7c28b9714febf', '打开上次打开的工作台', 'domain/orguserauth/workbenchRestService/queryLastChooseWorkbench', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8949d73e53794d359ebcbf57f41359ee', '查询角色工作台信息根据角色ID', 'domain/orguserauth/workbenchRestService/queryUserWorkbenchByUserId', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f969b32f0f2640189237fb82687f2a71', '查询角色工作台通过角色ID', 'domain/orguserauth/workbenchRestService/queryRoleWorkbenchByRoleId', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('00a74d89066a47989407b208230b1709', '查询用户拥有权限的工作台组件', 'domain/orguserauth/workbenchRestService/queryResourceEffectiveByUserId', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('38a33deff4e044b999ca4bde56a53ea8', '查询角色拥有权限的工作台组件', 'domain/orguserauth/workbenchRestService/queryResourceEffectiveByRoleId', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d0e7351f05ce4b58a6fca29a19c70847', '保存角色工作台信息', 'domain/orguserauth/workbenchRestService/saveRoleWorkbench', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('417153883b114bc796c78f4420b14bb9', '保存上次打开的工作台信息标志', 'domain/orguserauth/workbenchRestService/saveLastChooseWorkbenchData', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('42bc8aea3378498e81c53334b06050ac', '保存用户工作台信息', 'domain/orguserauth/workbenchRestService/saveUserWorkbench', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('518fb0e03af64326ae3f6d1ea6da1fad', '查询用户拥有的角色', 'domain/orguserauth/workbenchRestService/queryRoleListByUserId', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3154d229a4244fda90903cb56f7da445', '动态配置管理', 'tasysconfig/taSysConfigRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('658ea1202bc843e99e0201aa5d079b00', '配置列表', 'tasysconfig/taSysConfigRestService/querySysConfig', '3154d229a4244fda90903cb56f7da445', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f91c291fd2f741f5b2eb52d3f882c153', '添加配置', 'tasysconfig/taSysConfigRestService/addSysConfig', '3154d229a4244fda90903cb56f7da445', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6426f3aad7c648f68377d1e346c02624', '更新配置', 'tasysconfig/taSysConfigRestService/updateSysConfig', '3154d229a4244fda90903cb56f7da445', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('15de371347e94563b28214db9cf37c02', '删除配置', 'tasysconfig/taSysConfigRestService/deleteSysConfig', '3154d229a4244fda90903cb56f7da445', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('aa60a746c0a24640a06dea49a4d42572', '组织管理', 'domain/orguserauth/orgRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a87e7b2776214a76ba31151c6c5cbbe5', '异步获取组织树', 'domain/orguserauth/orgRestService/getOrgByAsync', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a2d72ab0507241d19290cbb6fee6817e', '查询账户上次登录信息', 'domain/orguserauth/workbenchRestService/queryCurrentUserLastLoginLog', 'ba186bfbfb1647ffbe7db23e89686e9f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('cbcdd31ffb6c40499bb1af744851a783', '管理员角色权限管理', 'domain/orguserauth/adminRoleRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e83bb87d573246e5b6c2ed534c2cd775', '新增管理员角色', 'domain/orguserauth/adminRoleRestService/addAdminRole', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e14986416987402c9283b0dfabacb379', '批量删除角色', 'domain/orguserauth/adminRoleRestService/deleteBatchRole', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e62f249ce1af41b7bb286d0e745d1471', '保存组织管理范围信息', 'domain/orguserauth/adminRoleRestService/saveOrgScope', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('877de3c0984e456ea1bbf52541a1d0f4', '更新角色信息', 'domain/orguserauth/adminRoleRestService/updateAdmin', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8ff682c3168a4b1295ed1efd32467321', '移除组织管理范围', 'domain/orguserauth/adminRoleRestService/removeOrgScope', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a911f4d576124857a80ddc37873d90e8', '批量新增账户角色关联关系', 'domain/orguserauth/adminRoleRestService/addBatchUserRole', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('37e4e66df7de4a87b14a19e8c4f44ef6', '查询账户列表', 'domain/orguserauth/adminRoleRestService/queryUserByCondition', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b05f5e9f2bb048d9adf335ab62a78cfd', '查询下级功能资源再授权信息', 'domain/orguserauth/adminRoleRestService/queryChildResourceAuthorityByRoleId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3efc328fe5fa40e38ff29b7f97deb709', '异步查询自定义资源权限再授权信息', 'domain/orguserauth/adminRoleRestService/queryChildCustomResourceAuthorityByRoleId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0cd7696d40ad49ad91f6ce91911fa2aa', '异步查询下级自定义资源', 'domain/orguserauth/adminRoleRestService/queryChildCustomResourceAsync', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('cab374d4f2af4cadaf487532c29df3ff', '批量更新权限有效使用时间', 'domain/orguserauth/adminRoleRestService/updateBatchUsePermissionEffectiveTime', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1ce091c3b3604be2932c25df07a642cd', '查询角色下的关联账户', 'domain/orguserauth/adminRoleRestService/queryUsersByRoleId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('20232206695a4aaa851e5d30a08ce295', '更新角色有效状态', 'domain/orguserauth/adminRoleRestService/updateRoleEffectiveByRoleId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2cb89bd084f74184a15e8313ad5b5647', '批量删除角色账户', 'domain/orguserauth/adminRoleRestService/deleteBatchRoleUsers', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('93acb3693a4c4dc8a47c4d0240f00890', '查询再授权顶级资源', 'domain/orguserauth/adminRoleRestService/queryRootResourceAuthority', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('697ec7337fc44943872e5fd47a6efdea', '查询下级资源权限', 'domain/orguserauth/adminRoleRestService/queryChildResourceByRoleId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('59bc6ad7451f47e5929a42d98986be98', '查询组织树', 'domain/orguserauth/adminRoleRestService/queryCurrentAdminRoleWrapOrgTree', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7ef0b9f372fd44a69ef79cdb968ec994', '查询可分配的账户列表', 'domain/orguserauth/adminRoleRestService/queryUsersNoWraperByRoleId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('60509043f699448ab9f5812e2333a11d', '查询角色列表', 'domain/orguserauth/adminRoleRestService/queryRolesByOrgId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9fcef2129d1c47e58631bf196a003d3b', '查询顶级自定义资源再授权信息', 'domain/orguserauth/adminRoleRestService/queryRootCustomResourceAuthority', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('90ef822f9e644448be5705a9994a263b', '审计查询', 'audit/auditQueryRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('41f4b754edd14be79ee108d40c061a8a', '批量新增角色账户关联关系', 'domain/orguserauth/adminRoleRestService/addBatchRoleUsers', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8d54ec9a7cbb4f1a9c3f4c54e7d341c8', '查询管理员角色列表', 'domain/orguserauth/adminRoleRestService/queryAdminRoleByOrgId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c59aaa9e3f1f4dcb961a90be46ea9b72', '删除角色账户关联关系', 'domain/orguserauth/adminRoleRestService/deleteRoleUserByKey', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fcf51d71136d4c2897b736f11af9266f', '查询顶级资源', 'domain/orguserauth/adminRoleRestService/queryRootResource', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('213caa8621494ce98a55bef675dd39f2', '查询账户的角色列表', 'domain/orguserauth/adminRoleRestService/queryRolesByUserId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8cb4565415ce47bba1d7be36cbabf40e', '批量删除账户角色关联关系', 'domain/orguserauth/adminRoleRestService/deleteBatchUserRole', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e0338aeb38164edb893d21c9af0bee43', '更新角色再授权权限', 'domain/orguserauth/adminRoleRestService/changeResourceAuthority', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('77f6d7ab67654023a997ba31bcef5262', '更新角色使用权限', 'domain/orguserauth/adminRoleRestService/changeResourceUsePermission', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('22d46236ba834a1f93fe128dd9861f6b', '选择组织管理范围', 'domain/orguserauth/adminRoleRestService/selectPermissionOrgScope', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('bbaef041a14c49f8963f1b6589a28d69', '更新自定义资源再授权', 'domain/orguserauth/adminRoleRestService/changeCustomResourceAuthority', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9cf0537f574d472fb2df4211a2e83d88', '更新使用权限有效时间', 'domain/orguserauth/adminRoleRestService/updateUsePermissionEffectiveTime', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b1fd82d7707645869a4088703dd260f1', '查询可分配的角色列表', 'domain/orguserauth/adminRoleRestService/queryNoWrapperRolesByUserId', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9466add8d6e3449aab05fbbf0bda43cc', '批量更新自定义资源权限的有效时间', 'domain/orguserauth/adminRoleRestService/updateBatchCustomResourceUsePermissionEffectiveTime', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('03c3c8302674456f989739369d35fedc', '选择组织管理范围', 'domain/orguserauth/adminRoleRestService/selectOrgScope', 'cbcdd31ffb6c40499bb1af744851a783', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1e334b5580fb40588b41833ace89ece0', '禁用动态服务', 'dynamic/rest/disable', '47488be533b54ae99f0418ba3962e8b9', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('96d86e0b87264ee29424151636d50667', '启用动态服务', 'dynamic/rest/enable', '47488be533b54ae99f0418ba3962e8b9', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('84dc082ef9a94cb6a70955ded2229848', '根据服务ID获取信息', 'dynamic/rest/getByRestId', '47488be533b54ae99f0418ba3962e8b9', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a196f610e78645488b830ca21d5d73d2', '更新动态服务信息', 'dynamic/rest/update', '47488be533b54ae99f0418ba3962e8b9', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6cb53bacf6e84b2d89eb732cf29b683d', '新增动态服务', 'dynamic/rest/add', '47488be533b54ae99f0418ba3962e8b9', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('49a5ee26819f4dc9934ff23b905b249f', '查询动态服务列表', 'dynamic/rest/queryList', '47488be533b54ae99f0418ba3962e8b9', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('404773da30b843a4b70da215f73a9384', '删除动态服务', 'dynamic/rest/delete', '47488be533b54ae99f0418ba3962e8b9', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('feeec1113f5d49b2bb19eca44702b035', '查询数据源列表', 'dynamic/rest/queryDsList', '47488be533b54ae99f0418ba3962e8b9', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fb72be8cb70f40caacc63510afc9e8b3', '审计导出', 'audit/auditExportRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('72e8e1bdf40d4350b8addd7132952c5d', '在线日志导出', 'audit/auditExportRestService/online', 'fb72be8cb70f40caacc63510afc9e8b3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('993e777e680341cb86fe70437ca1150f', '权限限制日志导出', 'audit/auditExportRestService/accessDeny', 'fb72be8cb70f40caacc63510afc9e8b3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8a19872cf40e4078922ae712924102f7', '登录环境分析导出', 'audit/auditExportRestService/loginLogAnalysis', 'fb72be8cb70f40caacc63510afc9e8b3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0b0456f381374b278c804a848bceebcc', '操作日志导出', 'audit/auditExportRestService/orgOp', 'fb72be8cb70f40caacc63510afc9e8b3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('909e509a59ec430d9eb239d3265f03f5', '登录失败日志导出', 'audit/auditExportRestService/loginFail', 'fb72be8cb70f40caacc63510afc9e8b3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ff98da127f154939a0fdef59ec22f982', '系统异常日志导出', 'audit/auditExportRestService/exception', 'fb72be8cb70f40caacc63510afc9e8b3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7abfb7bd84994c0db08eb9976adfd7d8', '登录日志导出', 'audit/auditExportRestService/login', 'fb72be8cb70f40caacc63510afc9e8b3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('dec7f275ce6242b0a591ccd371474db7', '主页服务', 'indexRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('653ec11f00e8473fbad26ea69098e047', '查询当前人员信息', 'indexRestService/getCurUserAccount', 'dec7f275ce6242b0a591ccd371474db7', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('aa56d8e0833b4fffad76945a4133f4d5', '修改密码', 'indexRestService/changePassword', 'dec7f275ce6242b0a591ccd371474db7', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8c965431a61145fb98f7fa67b7271f76', '默认打开页面请求 用于判断是否登录', 'indexRestService/defaultOpen', 'dec7f275ce6242b0a591ccd371474db7', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3cf89df828154febac4345a00ecd867b', '动态服务执行服务', 'rest/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fc45635549b5487c87d0287490a79b5f', '角色权限管理', 'domain/orguserauth/roleRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e2273cbc1ffb47afb24c2c7ec24b3633', '批量删除角色', 'domain/orguserauth/roleRestService/deleteBatchRole', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('624bde4ff89e4630b220f9808b5f07e2', '批量新增账户角色关联关系', 'domain/orguserauth/roleRestService/addBatchUserRole', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a14c3e447ba044aaa5275e85ee026f0d', '查询账户列表', 'domain/orguserauth/roleRestService/queryUserByCondition', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('87566f8084c14e70a76f622abef88ee9', '异常日志图', 'logmg/exceptionlog/serverExceptionLogRestService/exceptionChart', 'f2e756c387524382a6fa4d9f3db9abf7', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f08165aeb751488cb3c42d8679d92ad1', '审计列表查询', 'audit/taAuditResService/audit', 'fe98234d9a0a4f2a9228743a58e86d54', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d2e57270fb64454b937915cead646bf5', '审计日志图', 'audit/taAuditResService/auditChart', 'fe98234d9a0a4f2a9228743a58e86d54', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2b147317777f4f728c659abfea82db57', '操作日志图', 'org/sysmg/orgOpLogRestService/opLogChart', 'badc42944eb74d38a3ce254536e997c1', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6d73b1f0ab7742e4b21caef223799261', '查询人员通知', 'queryUserNotice', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('95d6fa234ee848059a815b9c14287737', '根据日志id查询详细异常日志', 'logmg/exceptionlog/serverExceptionLogRestService/getDetalExceptionLog', 'f2e756c387524382a6fa4d9f3db9abf7', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2d69486d5fdc4336be2411e125a561c0', '根据时间查询异常日志', 'logmg/exceptionlog/serverExceptionLogRestService/getServerExceptionLog', 'f2e756c387524382a6fa4d9f3db9abf7', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('badc42944eb74d38a3ce254536e997c1', '组织人员操作日志', 'org/sysmg/orgOpLogRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('26010f3c6ec04bf59680ec7f11109e54', '操作日志列表', 'org/sysmg/orgOpLogRestService/getOrgOpLog', 'badc42944eb74d38a3ce254536e997c1', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('366d45447b244c2b8f3a6cefec338ae0', '获取操作日志操作内容', 'org/sysmg/orgOpLogRestService/getChangeContent', 'badc42944eb74d38a3ce254536e997c1', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fe98234d9a0a4f2a9228743a58e86d54', '审计日志', 'audit/taAuditResService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('31d3fcf575694d36b19d79488d10a719', '删除自定义资源使用权限', 'domain/orguserauth/roleRestService/deleteCustomResourceUsePermission', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('55f139c1352846f185d1115d6d38bb2b', '查询当前账户的组织树', 'domain/orguserauth/roleRestService/queryCurrentAdminRoleWrapeOrgTree', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8786b8c546af4a049e5c166872fb69fb', '批量更新使用权限有效期', 'domain/orguserauth/roleRestService/updateBatchUsePermissionEffectiveTime', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('598b36d8554649c19f7e88e087347c7e', '批量更新自定义资源权限有效期', 'domain/orguserauth/roleRestService/updateCustomResourceUsePermissionEffectiveTime', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('74a7d5a9739841809c3b0db1079a4ba8', '回收权限', 'domain/orguserauth/roleRestService/deleteBatchUsePermissionByMoreRole', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('081da1f5cc5a41fbbdb89e5423e2908f', '查询角色下的关联账户', 'domain/orguserauth/roleRestService/queryUsersByRoleId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('98396599f9e1494e9e99bca94de11a94', '更新角色有效状态', 'domain/orguserauth/roleRestService/updateRoleEffectiveByRoleId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('138ecb6ff95e4b58991a0f2f5d1deb3b', '批量删除角色账户关联关系', 'domain/orguserauth/roleRestService/deleteBatchRoleUser', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('19b678a580b94d5d9e38d8fa1ab32124', '查询可分配的账户列表', 'domain/orguserauth/roleRestService/queryUsersNoWraperByRoleId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('bbc17b9c19374ae0b050f77da4e23636', '查询角色列表', 'domain/orguserauth/roleRestService/queryRolesByOrgId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9a749a3305844739a424feeb0962f0e9', '查询角色的授权', 'domain/orguserauth/roleRestService/queryRePermissionByRoleId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9e367251a5fd4c658648b2b0bc177b03', '查询账户关联的角色', 'domain/orguserauth/roleRestService/queryRolesByUserId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c3aac3c6d27844aaa36831f0b05199a9', '批量删除账户角色关联关系', 'domain/orguserauth/roleRestService/deleteBatchUserRole', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5165c4afb9a546f69555b139f194ba36', '更新使用权限使用时间', 'domain/orguserauth/roleRestService/updateUsePermissionEffectiveTime', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('01fa335e75e8405b9161c14be68bdfba', '查询账户棵关联的角色列表', 'domain/orguserauth/roleRestService/queryNoWrapperRolesByUserId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('501b8e57bfac491eab3ea302bd0d1577', '批量授权', 'domain/orguserauth/roleRestService/addBatchUsePermissionByMoreRole', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0f450ebdc9de4e8897402109ee1faa6d', '更新角色信息', 'domain/orguserauth/roleRestService/updateRoleByRoleId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ce3009ef4f61445bb2ba1fe5a747e8be', '查询角色的功能资源使用权限', 'domain/orguserauth/roleRestService/queryUsePermissionByRoleId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c335de0f745049439eea4ed4ef5e8220', '查询角色的自定义资源使用权限', 'domain/orguserauth/roleRestService/queryCustomUsePermissionByRoleId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('fc5a759500054f00a5016f68ba687ab2', '新增自定义使用权限', 'domain/orguserauth/roleRestService/addCustomResourceUsePermission', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1186646d85324d318e02899ca0f9508b', '更新Rest权限信息', 'domain/orguserauth/roleRestService/changeRestAuthority', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('155d58691b514a79824a95968dda38e6', '查询授权权限', 'domain/orguserauth/roleRestService/queryRePermission', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c4a5ece4fa5b414e98958972572c88b9', '异步加载自定义权限', 'domain/orguserauth/roleRestService/queryCustomRePermissionAsync', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('690b9acefd6e45f6bc5214646bb7d434', '删除使用权限', 'domain/orguserauth/roleRestService/deleteUsePermission', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('832d3322479949d8a36da9458fa3bcbb', '通过角色Id查询授权权限信息', 'domain/orguserauth/roleRestService/queryCustomRePermissionByRoleId', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e65ee0ad6e4e4e29a37d8d1f6d952ede', '查询自定义资源授权权限', 'domain/orguserauth/roleRestService/queryCustomRePermission', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a5ff0d74a0084769b8e767cc51491c75', '更新自定义资源权限的使用有效时间', 'domain/orguserauth/roleRestService/updateBatchCustomResourceUsePermissionEffectiveTime', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7f5017d989ad4f1e828bced19467ee2a', '新增使用权限', 'domain/orguserauth/roleRestService/addUsePermission', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c65e391b3372485aa45fddd997cab064', '新增角色', 'domain/orguserauth/roleRestService/addRole', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d3a3b173b8c04b5a9cef0e82e2920310', '权限复制', 'domain/orguserauth/roleRestService/copyResource', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9a520b63f24c437c8861080a5526f23b', '角色复制', 'domain/orguserauth/roleRestService/copyRole', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('43900d1b828d4c8b950612fcae10a786', '登录服务', 'loginRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('52c52f7e65c84c6da2c1e7da75824182', '获取配置信息', 'loginRestService/getConfig', '43900d1b828d4c8b950612fcae10a786', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ed2bf752425d4ac580d73f99615990ab', '登录页修改密码', 'loginRestService/changePassword', '43900d1b828d4c8b950612fcae10a786', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5f77fb7579294f02908bde7aaec43ece', '账户检查', 'loginRestService/checkUser', '43900d1b828d4c8b950612fcae10a786', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c4360fdef9ad46278a677508ee8b290a', '权限代理', 'domain/orguserauth/roleAgentRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('16e2788f54794c1d9fda4036b0ebe85f', '异步查询组织', 'domain/orguserauth/roleAgentRestService/getOrgByAsync', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c6428dc1dc144d4c8efb9b0e44dc17d5', '修改代理角色有效期', 'domain/orguserauth/roleAgentRestService/updateAgentRoleAuthority', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5b43aa7145734290803796c1b9070d49', '查询当前登录管理员能管理的代理角色列表', 'domain/orguserauth/roleAgentRestService/queryAllAgentRole', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6ec810ccee18431bb058d6440c4476f7', '根据组织id查询当前管理员所能管理的代理人员', 'domain/orguserauth/roleAgentRestService/queryReAgentUsersByOrgId', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1272585f9ed944eab717939300cadbb8', '检查电话号码', 'loginRestService/checkMobile', '43900d1b828d4c8b950612fcae10a786', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3a0b491b01674d9fab78498550320cd0', '根据userId获取当前账户可授权的自定义资源权限', 'domain/orguserauth/roleAgentRestService/queryCustomUsePermissionByUserId', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b7ea5b5a2bdc462aa14e8aca7961d61f', '新增代理角色', 'domain/orguserauth/roleAgentRestService/addAgent', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('aef954306f5d4c6d9b5e507aa3f175c6', '首页功能资源服务', 'domain/orguserauth/menuAction/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('9d4b47c0490640008c31e892314520fc', '查询功能资源资源列表', 'domain/orguserauth/menuAction/queryRootChildrenMenus', 'aef954306f5d4c6d9b5e507aa3f175c6', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('58606a643d29437c9afcd69e8dd6482e', '接入系统管理', 'domain/orguserauth/accessSystemManagementRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6b844095d25d44daba36670ed5d31bd7', '查询有效的接入系统列表', 'domain/orguserauth/accessSystemManagementRestService/queryEffectiveAccessSystem', '58606a643d29437c9afcd69e8dd6482e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3bc7226a89454115b2c09d10aff96749', '批量删除接入系统', 'domain/orguserauth/accessSystemManagementRestService/deleteBatchTaAccessSystemById', '58606a643d29437c9afcd69e8dd6482e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c9b473e0bd044a579aef3a73d4ffc3cf', '新增接入系统', 'domain/orguserauth/accessSystemManagementRestService/addTaAccessSystem', '58606a643d29437c9afcd69e8dd6482e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3974a2411df2404ab1e0afa08653da95', '删除接入系统', 'domain/orguserauth/accessSystemManagementRestService/deleteTaAccessSystemById', '58606a643d29437c9afcd69e8dd6482e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f9de9a6d93e94f31ac446d213c5782fa', '更新接入系统列表', 'domain/orguserauth/accessSystemManagementRestService/updateTaAccessSystemById', '58606a643d29437c9afcd69e8dd6482e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('807ef17eaa8446188acbf92929171c56', '接入系统列表', 'domain/orguserauth/accessSystemManagementRestService/queryAccessSystemByParam', '58606a643d29437c9afcd69e8dd6482e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2c3c1591444842e1aa1588a0aab998a8', '相似权限', 'domain/orguserauth/similarAuthorityRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8cea2eccc40c4c418e43f4321ee970c3', '根据资源Ids查找所有拥有这些资源使用权的角色', 'domain/orguserauth/similarAuthorityRestService/queryUsePermissionRoleByResourceId', '2c3c1591444842e1aa1588a0aab998a8', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('83ca67228517485fba1f73bffc17b8a6', '查询当前角色可授权资源', 'domain/orguserauth/similarAuthorityRestService/queryCurrentUserAllRePermissionResource', '2c3c1591444842e1aa1588a0aab998a8', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('83a2c0425f3d469d92f24e7043a5d0f7', '查询当前角色可授权资源-小异步', 'domain/orguserauth/similarAuthorityRestService/queryCurrentUserSomeRePermission', '2c3c1591444842e1aa1588a0aab998a8', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('09ec9781ec834b079889bcf60fcd7388', '批量授予相似权限', 'domain/orguserauth/similarAuthorityRestService/addBatchSimilarAuthority', '2c3c1591444842e1aa1588a0aab998a8', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0bd90473a1b64ae1ab705302bb6881fc', '分布式任务之任务操作', 'jobmg/elasticjob/jobOperateRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b20b7169663b49d6b548a34e0d128b25', '清除已完成作业缓存', 'jobmg/elasticjob/jobOperateRestService/clearCache', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8d9b9595a4f84d28b5cad358335a37f7', '使作业生效', 'jobmg/elasticjob/jobOperateRestService/enableJob', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6e135682e38140a3bc423ff6406119d3', '终止作业', 'jobmg/elasticjob/jobOperateRestService/shutdownJob', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('80ee0be75f024dee80412a61c3284256', '使分片项生效', 'jobmg/elasticjob/jobOperateRestService/effectSharding', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d105c3c113bf4188afd27fe2a2347a08', '获取作业进度', 'jobmg/elasticjob/jobOperateRestService/getJobProgress', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('302d500716e34c3490523c3093201174', '删除作业', 'jobmg/elasticjob/jobOperateRestService/removeJob', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('4b9d98dd01dd479d942b7d9fee2b8eae', '刷新作业进度', 'jobmg/elasticjob/jobOperateRestService/refreshProgress', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('db1104eba4874533b3042af4651e9c75', '触发作业', 'jobmg/elasticjob/jobOperateRestService/triggerJob', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('97615539a3fe4dceb66a01ddaa5b4b4b', '获取作业详细信息', 'jobmg/elasticjob/jobOperateRestService/getJobDetailInfo', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('80018c73459347e0bf7cb9f089179e05', '获取作业信息', 'jobmg/elasticjob/jobOperateRestService/getJobInfo', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('3568ca57a1bf4600a38b716982600625', '使分片项失效', 'jobmg/elasticjob/jobOperateRestService/disabledSharding', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d9d0f34456c24b44a6efa5634a6f115c', '使作业失效', 'jobmg/elasticjob/jobOperateRestService/disableJob', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d23f6f951ce5490dbe54dce47b1acfb1', '查看作业详情', 'jobmg/elasticjob/jobOperateRestService/getJobServerDetail', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('59064e87d0294f5097e8bb192ab4e98c', '更新作业信息', 'jobmg/elasticjob/jobOperateRestService/saveJobDetailInfo', '0bd90473a1b64ae1ab705302bb6881fc', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b32b96d0142d4fea8d5fd8a58011c0c2', '人员账号管理', 'domain/orguserauth/TaUserManagementRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('72fc1e15bee64231beee40b2d482aa43', '修改账户密码', 'domain/orguserauth/TaUserManagementRestService/updateUserPwdByUserId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b94fc48142fe4130b29757a07f385aeb', '异步查询组织', 'domain/orguserauth/TaUserManagementRestService/getOrgByAsync', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('634743c9057749748b82045ef69dd214', '条件查询人员信息', 'domain/orguserauth/TaUserManagementRestService/queryUserByConditon', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5861ad2003b94d8ab0d730112a098fae', '根据账户ID获取该账户的关联组织', 'domain/orguserauth/TaUserManagementRestService/getOrgUserByUserId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('02484f5c350544b5b16ead7293c3558e', '通过userId查询账户头像', 'domain/orguserauth/TaUserManagementRestService/queryAvatar', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('bdbe704910d8436fa2fe57985ff20431', '根据账户Id更新组织人员关联关系', 'domain/orguserauth/TaUserManagementRestService/updateOrgUserByUserId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b4bfe4f72bc54f5fb6d431053323ff7d', '查询组织树', 'domain/orguserauth/TaUserManagementRestService/queryOrgTree', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a8d04425958c452eb9461a93867884ac', '通过自定义组织id查询user', 'domain/orguserauth/TaUserManagementRestService/queryBatchUserByCustomOrgId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ab213f6723aa4e37bd2ce5597928fd53', '修改人员组织机构', 'domain/orguserauth/TaUserManagementRestService/updateUserOrgByUserId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('06cd55b18e1f44adbdeded3fca1355f7', '查询账户的组织信息', 'domain/orguserauth/TaUserManagementRestService/queryOrgInfoByUserId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('90829994ecaa4a0ca78c8ff564f2753b', '批量解锁', 'domain/orguserauth/TaUserManagementRestService/updateBatchUserUnLockByUserIds', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b39bb0d6720d4764b2cd0224f3659a80', '通过组织id查询组织人员信息', 'domain/orguserauth/TaUserManagementRestService/queryBatchUserByOrgId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('24cee81a613f416a8649d159931cede2', '批量禁用', 'domain/orguserauth/TaUserManagementRestService/updateBatchUserDisabledByUserIds', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5f575442b1f84595a1c05aef61f85765', '重置账户密码', 'domain/orguserauth/TaUserManagementRestService/resetUserPwByUserId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('25a2a2f205114fab819203012b99fa68', '通过userId修改该人员信息', 'domain/orguserauth/TaUserManagementRestService/updateUserByUserId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('203d2e7109ec4853997e2cfb462f5501', '获取组织模式配置', 'domain/orguserauth/TaUserManagementRestService/getUserOrgMultiConfig', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('1e9ea424827e475c89f036ab1f85137e', '批量删除人员信息', 'domain/orguserauth/TaUserManagementRestService/deleteBatchUserByUserIds', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a1fa23f246904da9a3198966bbdd2122', '批量启用', 'domain/orguserauth/TaUserManagementRestService/updateBatchUserAbleByUserIds', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f2df6f27fcb945b39f263962a387aa16', '查询可发送人员', 'message/queryUser', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a50c6b036e164d5daaba6a33881d5370', '查询人员消息', 'message/queryUserMessage', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a272cfdac31c4af593d644e951b80a80', '查询通知附件', 'message/queryNoticeFiles', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('eb848308e574462e8d3bc9acb9d5defd', '发送通知', 'message/sendNotice2', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('02bbbed032b44a52ae6c5e82ec45d83a', '发送通知', 'message/sendNotice', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0aae6ab603b442429a9ca0b29396da80', '通知标记已读', 'message/readNotice', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('4b19cb3a440e4e3f9f6f34de34e6d695', '通知标记已读(批量)', 'message/readNotices', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f157f212127b49659461b34a0c23eb95', '上传通知附件', 'message/uploadNoticeFile', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('45fc94a23742400d9a681ac08695c246', '删除通知(批量)', 'message/removeNotices', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a213d1db5b6b4e08b204b701c0476024', '下载通知附件', 'message/downloadNoticeFile', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('09024f31af1341a7b6c19c38359fcca1', '查询人员私信', 'message/queryUserLetter', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('4bf5eb1024ea41e09fdd8e714ea57492', '发送私信', 'message/sendLetter2', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e8ac1a55c9db46d8a38d0c43f424a0bf', '发送私信', 'message/sendLetter', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2a2eb9bfce6d42168eaa271fa3a001b8', '查询人员对话', 'message/queryUserChat', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('448d31215eba49ecb975f1800ed00f79', '私信标记已读', 'message/readLetter', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8b11d3374b344f6292b2e13794b14fd7', '新建对话', 'message/createChat', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7ffbbd9552954860b058a29f27dc55f0', '查询人员私信(未读)', 'message/queryUserLetterNoRead', '415a0c59d499478da3b40fc896470569', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('af0be155c6934ecd83d8cf62b36437e2', '自定义资源', 'domain/orguserauth/customResourceRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('98c8e94f284e4cc19a66fb60ba04056d', '更新自定义资源', 'domain/orguserauth/customResourceRestService/updateTaCustomResourceByCustomResourceId', 'af0be155c6934ecd83d8cf62b36437e2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('91a7ffff45334096b124645f4f29974f', '删除自定义资源', 'domain/orguserauth/customResourceRestService/deleteTaCustomResourceByCustomResourceId', 'af0be155c6934ecd83d8cf62b36437e2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2e457c6060bd411eb65bae7068d3d5d9', '新增自定义资源', 'domain/orguserauth/customResourceRestService/addTaCustomResource', 'af0be155c6934ecd83d8cf62b36437e2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('89d993cfaedb4c5db40fcf1bbe4a96ae', '查询自定义资源', 'domain/orguserauth/customResourceRestService/queryTaCustomResourceByCustomResourceId', 'af0be155c6934ecd83d8cf62b36437e2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7859157a0cd94c89995b0dde9fc40bc6', '行政区划管理', 'domain/orguserauth/areaAggregateRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6cbf93b5f4b346b890f104756c55d078', '更新行政区划数据', 'domain/orguserauth/areaAggregateRestService/updateArea', '7859157a0cd94c89995b0dde9fc40bc6', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e0819670288c4a8991872a398894488e', '新增行政区划数据', 'domain/orguserauth/areaAggregateRestService/addArea', '7859157a0cd94c89995b0dde9fc40bc6', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0cdb0e7d42b941e3bc99399aa69a0f89', '异步加载行政区划数据', 'domain/orguserauth/areaAggregateRestService/queryAreaByAsync', '7859157a0cd94c89995b0dde9fc40bc6', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6a73d3aa6f5949caa1f4c536ffe01400', '异步加载下级功能资源', 'domain/orguserauth/resourceManagementRestService/queryChildResource', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f2e756c387524382a6fa4d9f3db9abf7', '服务异常日志', 'logmg/exceptionlog/serverExceptionLogRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('676b9aca9d054d65aac3830436d2322e', '用户可配置字段', 'domain/orguserauth/configurableFieldsRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b771b6243ac04ddc8f9e496c175f8f52', '更新配置字段详细信息', 'domain/orguserauth/configurableFieldsRestService/modifyConfigurableField', '676b9aca9d054d65aac3830436d2322e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d3dd51b3f42141b490aab2a4b76dba44', '重置默认字段配置', 'domain/orguserauth/configurableFieldsRestService/reloadDefaultManageableFields', '676b9aca9d054d65aac3830436d2322e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('bbde0232aa2c41b59593666c95186ab6', '更新配置字段', 'domain/orguserauth/configurableFieldsRestService/modifyConfigurableFields', '676b9aca9d054d65aac3830436d2322e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('47a91cf250b8446c89cc98628adbcd31', '获取配置字段', 'domain/orguserauth/configurableFieldsRestService/queryManageableFields', '676b9aca9d054d65aac3830436d2322e', '1', '1', now(), '1');
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000658', '扩展字段', 'domain/orguserauth/configurableFieldsRestService/addConfigurableField', '676b9aca9d054d65aac3830436d2322e', '1', '1', now(), '1', NULL);
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8545972435bb4abab8d2524fac510a04', '润乾页面请求', 'tarunqianresource/taRunqianResourceRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('5855e399c9074b899850bb25ce1aaf76', '分布式任务之服务器操作', 'jobmg/elasticjob/serverOperateRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c7ae73b1a4cd4ad8a4b4e2f199bad322', '新增自定义组织类别', 'domain/orguserauth/customOrgAggregateRestService/addCustomOrgTypeName', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ef46133fe6b04b04b0a5e7481d92b2ea', '更新自定义组织类别', 'domain/orguserauth/customOrgAggregateRestService/updateCustomOrgTypeName', 'b091f4af3eba4c5c946af8dacaf951b9', '1', '1', now(), '1');
-- INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('47488be533b54ae99f0418ba3962e8b9', '动态服务', 'dynamic/rest/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7ac1c71173f2421ea634f0e274303521', '新增批量的角色账户关联关系', 'domain/orguserauth/roleRestService/addBatchRoleUsers', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('670804d52f2c473bb58ad6c87ceefbf9', '删除角色账户关联关系', 'domain/orguserauth/roleRestService/deleteRoleUserByKey', 'fc45635549b5487c87d0287490a79b5f', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('072db1f4c4a848cd938d95b4057ff4c0', '根据userId获取当前账户可授权的菜单权限', 'domain/orguserauth/roleAgentRestService/queryUsePermissionByUserId', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7ff6d8f6a9994e7cb6710b4edd1d109c', '批量删除代理角色', 'domain/orguserauth/roleAgentRestService/deleteBatchAgentRole', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e546d678a75740c0be0e9a0914d3fac4', '查询代理角色权限', 'domain/orguserauth/roleAgentRestService/queryAgentRoleAuthority', 'c4360fdef9ad46278a677508ee8b290a', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('4b3512dec0204cd19b7c5fbdd75a97fa', '获取人员列表', 'domain/orguserauth/TaUserManagementRestService/queryEffectiveUser', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('416f405ee4dc43d5b5c9f4bbbf6414ef', '获取标签', 'domain/orguserauth/TaUserManagementRestService/queryTagByUserId', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d3db2fb5724d4839b04ff4260174a70d', '添加人员信息', 'domain/orguserauth/TaUserManagementRestService/addUser', 'b32b96d0142d4fea8d5fd8a58011c0c2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('415a0c59d499478da3b40fc896470569', '消息通知', 'message/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('efaecd1281c64c89ba87363bf1c18900', '根据资源类别查询自定义资源树', 'domain/orguserauth/customResourceRestService/queryALLTaCustomResourceTreeByCategoryId', 'af0be155c6934ecd83d8cf62b36437e2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7af7009339d145c1967e8d46a4b194d1', '查询资源类别', 'domain/orguserauth/customResourceRestService/queryALLTaCustomResourceParent', 'af0be155c6934ecd83d8cf62b36437e2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('321ac2aade66460dbea294f434dc4c9d', '日志动态配置', 'logmg/logconfig/logConfigRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('e34479764c66437e993c27072a13dfca', '新增日志配置', 'logmg/logconfig/logConfigRestService/addLogConfig', '321ac2aade66460dbea294f434dc4c9d', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('72fb390446eb475cbefd28e0a26041c0', '获取日志配置', 'logmg/logconfig/logConfigRestService/getLogConfig', '321ac2aade66460dbea294f434dc4c9d', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('89872c1e30e543e1ae0352c0b1ecfb72', '删除日志配置', 'logmg/logconfig/logConfigRestService/deleteLogConfigByName', '321ac2aade66460dbea294f434dc4c9d', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('0e4c73815f2e43deaadbb0646cc9ae7c', '修改文件名类型', 'logmg/logconfig/logConfigRestService/configFileNamePattern', '321ac2aade66460dbea294f434dc4c9d', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('ffffdb7e67b74234a3a230abd06e4d32', '修改日志级别和日志输出类型', 'logmg/logconfig/logConfigRestService/configLevelAndAppenderType', '321ac2aade66460dbea294f434dc4c9d', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('7f621426782342faa371319c677f066e', '自定义资源类别', 'domain/orguserauth/resourceCategoryRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('f757b23bbfbe461b8478e5ad456ec64f', '删除自定义资源类别', 'domain/orguserauth/resourceCategoryRestService/deleteTaResourceCategoryByCategoryId', '7f621426782342faa371319c677f066e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b94b3fd3ce4f4e89860a3b0b00f985b4', '更新自定义资源类别', 'domain/orguserauth/resourceCategoryRestService/updateTaResourceCategoryByCategoryId', '7f621426782342faa371319c677f066e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('80d490da38044ad58c96da08e6e58efd', '查询所有的自定义资源类别列表', 'domain/orguserauth/resourceCategoryRestService/queryALLTaResourceCategory', '7f621426782342faa371319c677f066e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8c54473a278248628dcad204a57ebd9a', '新增自定义资源类别', 'domain/orguserauth/resourceCategoryRestService/addTaResourceCategory', '7f621426782342faa371319c677f066e', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('4e90eb395b2e41ed81285b7ba9362b84', '登录失败日志', 'logMg/loginFailLog/taLoginFailLogRestService/**', null, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('498a9c21ba1d4042b85b2d0b8d4ec61f', '登录失败列表', 'logMg/loginFailLog/taLoginFailLogRestService/queryLoginFailLog', '4e90eb395b2e41ed81285b7ba9362b84', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('20cf287cd36448d4ba6b6930ea472824', '查询所有的自定义资源树', 'domain/orguserauth/customResourceRestService/queryALLTaCustomResourceTree', 'af0be155c6934ecd83d8cf62b36437e2', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('314759f094014f24b991d467378d1755', '登录失败图', 'logMg/loginFailLog/taLoginFailLogRestService/loginFailChart', '4e90eb395b2e41ed81285b7ba9362b84', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('253c2eae3e0046c2ba4f53f4ffa89ba3', '在线表单模板管理', 'onlineForm/templateMg/templateMgRestService/**', NULL, '0', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('d9a5e31b83b141279b387bc4dfac0713', '表单模板条件分页查询', 'onlineForm/templateMg/templateMgRestService/queryTemplate', '253c2eae3e0046c2ba4f53f4ffa89ba3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('66ee423b80f84407a952e4edf2d27901', '通过ID查询模板JSON字符串', 'onlineForm/templateMg/templateMgRestService/queryTemplateContentById', '253c2eae3e0046c2ba4f53f4ffa89ba3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('2133136c1fca4fa79ed4c5b960bf914c', '新增表单模板', 'onlineForm/templateMg/templateMgRestService/insertTemplate', '253c2eae3e0046c2ba4f53f4ffa89ba3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('8d50ff41b6aa42c383f119cc4dd9abf6', '更新表单模板', 'onlineForm/templateMg/templateMgRestService/updateTemplate', '253c2eae3e0046c2ba4f53f4ffa89ba3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('59ea5a521dd1439f839466603c76302a', '更新模板有效性', 'onlineForm/templateMg/templateMgRestService/updateTemplateEffective', '253c2eae3e0046c2ba4f53f4ffa89ba3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('c433ed5a2a1340f6a0063637861de279', '删除表单模板', 'onlineForm/templateMg/templateMgRestService/deleteTemplates', '253c2eae3e0046c2ba4f53f4ffa89ba3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('632895cc5cd14dbdb76d9c10fffd0786', '获取表结构', 'onlineForm/templateMg/templateMgRestService/queryTableColumns', '253c2eae3e0046c2ba4f53f4ffa89ba3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('60bd58fe73d0472881349ecbfdd0bd29', '获取数据库表', 'onlineForm/templateMg/templateMgRestService/queryTable', '253c2eae3e0046c2ba4f53f4ffa89ba3', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('6b8628679fd84bf989030e328248bd0a', '下载组织导入模板', 'domain/orguserauth/orgRestService/downloadOrgFIle', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('b117d8dfb50946bfb8714fcb235845d2', '下载功能资源导入模板', 'domain/orguserauth/resourceManagementRestService/downloadResourceFIle', '821a8ca012154ba2a4c451918c99cfc0', '1', '1', now(), '1');
INSERT INTO taurl (urlid, name, url, namespace, type, effective, createtime, createuser) VALUES ('a2fe720ce245464ea4cf8997645d8ae4', '导入导出功能', 'org/orguser/importAndExportService/**',NULL, '0', '1', now(), '1');
INSERT INTO taurl (urlid, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER) VALUES('60f05ed2d53c4676978971a62ab5b885', '会话管理', 'session/**', NULL, '0', '1', now(), '1');
INSERT INTO taurl (urlid, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER) VALUES('65062ea24d9f40e884314ad23429f717', '在线人员信息', 'session/onlineUser', '60f05ed2d53c4676978971a62ab5b885', '1', '1', now(), '1');
INSERT INTO taurl (urlid, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER) VALUES('9b061fa118ec4954b6db899e1332d3ac', '在线人员下线', 'session/offline', '60f05ed2d53c4676978971a62ab5b885', '1', '1', now(), '1');
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('**********', '账号申请', 'domain/applyexamine/account/apply/**', '', '0', '1', now(), '1', '');
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('**********', '账号申请审核', 'domain/applyexamine/account/examine/**', NULL, '0', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('**********', '注册通道管理', 'domain/orguserauth/taregisterchannel/**', '', '0', '1', now(), '1', '');
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('**********', '顶级组织管理', 'domain/orguserauth/orgRootRestService/**', NULL, '0', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('**********', '人员信息管理', 'domain/orguserauth/userInfoAggregateRestService/**', NULL, '0', '1', now(), '**********', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('**********', '人员账号导入导出', 'domain/orguserauth/userImportAndExportRestService/**', NULL, '0', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000601', '公共组织查询', 'domain/orguserauth/orgCommonRestService/**', NULL, '0', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000643', '限流管理', 'rateLimit/**', NULL, '0', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000642', '第三方访问权限管理', 'accessAuthority/**', NULL, '0', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000724', '查询组织树', 'domain/orguserauth/orgRestService/queryOrgTree', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000725', '查询所有组织标签', 'domain/orguserauth/orgRestService/queryTags', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000726', '查询组织的标签ID', 'domain/orguserauth/orgRestService/queryOrgTags', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000727', '查询组织树(排除自身-变更上级用)', 'domain/orguserauth/orgRestService/queryOrgTreeNoSelf', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000728', '查询组织树节点', 'domain/orguserauth/orgRestService/queryOrgTreeNode', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000729', '分页查询组织下人员', 'domain/orguserauth/orgRestService/queryOrgUserPage', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000730', '查询组织下权限数据', 'domain/orguserauth/orgRestService/queryOrgAuthority', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000731', '查询组织下权限数据(根据角色标记数据)', 'domain/orguserauth/orgRestService/queryOrgAuthorityWithRole', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000732', '查询组织下角色(分页)', 'domain/orguserauth/orgRestService/queryOrgRole', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000733', '查询当前登录人员可管理的角色(分页)', 'domain/orguserauth/orgRestService/queryManageableRole', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000734', '根据组织ID查询该组织可以被分配的角色列表', 'domain/orguserauth/orgRestService/queryNoWrapperRolesByOrgId', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000735', '查询当前登录人员可管理的人员(分页)', 'domain/orguserauth/orgRestService/queryManageableUser', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000736', '新增组织', 'domain/orguserauth/orgRestService/addOrg', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000737', '编辑组织', 'domain/orguserauth/orgRestService/editOrg', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000738', '异步组织树查询(排除自己)', 'domain/orguserauth/orgRestService/queryOrgTreeNodeWithNoSelf', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000739', '变更上级组织', 'domain/orguserauth/orgRestService/editParentOrg', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000740', '启用/禁用组织', 'domain/orguserauth/orgRestService/enableOrg', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000741', '删除组织', 'domain/orguserauth/orgRestService/removeOrg', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000742', '组织绑定角色', 'domain/orguserauth/orgRestService/bindOrgRole', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000743', '组织删除绑定角色', 'domain/orguserauth/orgRestService/removeOrgRole', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000744', '组织绑定人员', 'domain/orguserauth/orgRestService/bindOrgUser', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000745', '组织删除绑定人员', 'domain/orguserauth/orgRestService/removeOrgUser', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000746', '批量导入组织', 'domain/orguserauth/orgRestService/importBatchOrgExcel', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000747', '导出组织', 'domain/orguserauth/orgRestService/exportOrg', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000748', '查询行政区划', 'domain/orguserauth/orgRestService/queryAreaByAsync', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000749', '条件查询人员', 'domain/orguserauth/orgRestService/queryEffectiveUser', 'aa60a746c0a24640a06dea49a4d42572', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('1000000750', '获取全部的有效界面元素信息', 'domain/orguserauth/menuAction/queryAllElement', 'aef954306f5d4c6d9b5e507aa3f175c6', '1', '1', now(), '1', NULL);
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('0170b4dc35c34023a838c5f09aa68bd0', '行为日志', 'oplogRestService/**', '', '0', '1', now(), '1', '');
INSERT INTO taurl (URLID, NAME, URL, NAMESPACE, TYPE, EFFECTIVE, CREATETIME, CREATEUSER, FIELDKEY) VALUES('0d8cabb5ed624eed9686a87e3dbb5620', '行为日志分页查询', 'oplogrestservice/page', '0170b4dc35c34023a838c5f09aa68bd0', '1', '1', now(), '1', NULL);



-- 5.3.3.RELEASE 会话管理
INSERT INTO taresource (resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, isfiledscontrol, CREATETIME, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image) VALUES('8b00ee078064440cbd1ee34118ed2c52', '1d4e283ad5584e02811f6b188d3592bc', '会话管理', '', 'sysmg', 'sysmg.html#/sessionManagement', 130.000000000000000000000000000000, '40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/8b00ee078064440cbd1ee34118ed2c52', '银海软件/管理系统/系统管理/会话管理', '3', 'qq', NULL, '2', 0.000000000000000000000000000000, '1', '1', '1', NULL, '2023-01-12 00:00:00', '1', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL);
INSERT INTO taresourceurl (RESOURCEID, URLID, AUTHORITYPOLICY) VALUES('8b00ee078064440cbd1ee34118ed2c52', '60f05ed2d53c4676978971a62ab5b885', '0');
INSERT INTO taresourceurl (RESOURCEID, URLID, AUTHORITYPOLICY) VALUES('8b00ee078064440cbd1ee34118ed2c52', '65062ea24d9f40e884314ad23429f717', '0');
INSERT INTO taresourceurl (RESOURCEID, URLID, AUTHORITYPOLICY) VALUES('8b00ee078064440cbd1ee34118ed2c52', '9b061fa118ec4954b6db899e1332d3ac', '0');


-- 5.3.3.RELEASE 行为日志resource
INSERT INTO taresource
(resourceid, presourceid, name, code, syscode, url, orderno, idpath, namepath, resourcelevel, icon, iconcolor, securitypolicy, securitylevel, resourcetype, effective, isdisplay, isfiledscontrol, CREATETIME, createuser, uiauthoritypolicy, field01, field02, field03, field04, field05, field06, field07, field08, field09, field10, workbench, image)
VALUES('bdcd5003137943299f69b36e55f2dc66', '1d4e283ad5584e02811f6b188d3592bc', '行为日志', NULL, 'sysmg', 'logmg.html#/opLog', 140.000000000000000000000000000000, '40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/bdcd5003137943299f69b36e55f2dc66', '银海软件/管理系统/系统管理/行为日志', '3', NULL, NULL, '2', 0.000000000000000000000000000000, '1', '1', '1', NULL, '2023-03-20 00:00:00', '1', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL);
INSERT INTO taresourceurl
(RESOURCEID, URLID, AUTHORITYPOLICY)
VALUES('bdcd5003137943299f69b36e55f2dc66', '0170b4dc35c34023a838c5f09aa68bd0', '0');
INSERT INTO taresourceurl
(RESOURCEID, URLID, AUTHORITYPOLICY)
VALUES('bdcd5003137943299f69b36e55f2dc66', '0d8cabb5ed624eed9686a87e3dbb5620', '0');


-- 5.4.0 人员管理resourceurl
UPDATE taresource
SET PRESOURCEID='48afedddc8f04c668b3c1572c30a7745', NAME='人员账号管理', CODE='', SYSCODE='sysmg', URL='orguser.html#/userInfoManagement', orderno=10, IDPATH='40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/48afedddc8f04c668b3c1572c30a7745/525a8cb155444e7f823cd070802f753f', NAMEPATH='银海软件/管理系统/组织人员管理/人员账号管理', RESOURCELEVEL='3', ICON='usergroup-add', ICONCOLOR=NULL, SECURITYPOLICY='2', securitylevel=0, RESOURCETYPE='1', EFFECTIVE='1', ISDISPLAY='1', OPENMODE='1', ISFILEDSCONTROL=NULL, CREATETIME='2023-12-04 12:02:16', CREATEUSER='1', UIAUTHORITYPOLICY='0', FIELD01=NULL, FIELD02=NULL, FIELD03=NULL, FIELD04=NULL, FIELD05=NULL, FIELD06=NULL, FIELD07=NULL, FIELD08=NULL, FIELD09=NULL, FIELD10=NULL, workbench='0', IMAGE=NULL
WHERE RESOURCEID='daceeff8a97b46cb9573b93ba3a5a792';
INSERT INTO taresourceurl
(RESOURCEID, URLID, AUTHORITYPOLICY)
VALUES('daceeff8a97b46cb9573b93ba3a5a792', '**********', '0');
INSERT INTO taresourceurl
(RESOURCEID, URLID, AUTHORITYPOLICY)
VALUES('daceeff8a97b46cb9573b93ba3a5a792', '**********', '0');
INSERT INTO taresourceurl
(RESOURCEID, URLID, AUTHORITYPOLICY)
VALUES('daceeff8a97b46cb9573b93ba3a5a792', '9a749a3305844739a424feeb0962f0e9', '0');


-- 5.4.0 自定义组织管理
INSERT INTO taresourceurl
(RESOURCEID, URLID, AUTHORITYPOLICY)
VALUES('3dbde33722154503a7d22ac60f6a0e4e', '1000000601', '0');


-- 新增第三方访问权限管理菜单
INSERT INTO taresource (RESOURCEID, PRESOURCEID, NAME, CODE, SYSCODE, URL, orderno, IDPATH, NAMEPATH, RESOURCELEVEL, ICON, ICONCOLOR, SECURITYPOLICY, securitylevel, RESOURCETYPE, EFFECTIVE, ISDISPLAY, OPENMODE, ISFILEDSCONTROL, CREATETIME, CREATEUSER, UIAUTHORITYPOLICY, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, FIELD06, FIELD07, FIELD08, FIELD09, FIELD10, workbench, IMAGE) VALUES('b4e12d46211443069618302072200e71', '1d4e283ad5584e02811f6b188d3592bc', '第三方访问权限管理', '', 'sysmg', 'sysmg.html#/accessAuthority', 150, '40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/b4e12d46211443069618302072200e71', '银海软件/管理系统/系统管理/第三方访问权限管理', '3', 'setting', NULL, '2', 0, '1', '1', '1', '1', NULL, '2023-12-04 12:02:16', '1', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL);
INSERT INTO taresourceurl (RESOURCEID, URLID, AUTHORITYPOLICY) VALUES('b4e12d46211443069618302072200e71', '1000000642', '0');

-- 新增限流管理菜单
INSERT INTO taresource (RESOURCEID, PRESOURCEID, NAME, CODE, SYSCODE, URL, orderno, IDPATH, NAMEPATH, RESOURCELEVEL, ICON, ICONCOLOR, SECURITYPOLICY, securitylevel, RESOURCETYPE, EFFECTIVE, ISDISPLAY, OPENMODE, ISFILEDSCONTROL, CREATETIME, CREATEUSER, UIAUTHORITYPOLICY, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, FIELD06, FIELD07, FIELD08, FIELD09, FIELD10, workbench, IMAGE) VALUES('f2ce62655c724394a877518690469391', '1d4e283ad5584e02811f6b188d3592bc', '限流管理', '', 'sysmg', 'sysmg.html#/rateLimit', 160, '40337bdecb19484ebeb39d6c21aaca26/0415d44401b24605a21b589b6aaa349e/1d4e283ad5584e02811f6b188d3592bc/f2ce62655c724394a877518690469391', '银海软件/管理系统/系统管理/限流管理', '3', 'setting', NULL, '2', 0, '1', '1', '1', '1', NULL, '2023-12-04 12:02:16', '1', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL);
INSERT INTO taresourceurl (RESOURCEID, URLID, AUTHORITYPOLICY) VALUES('f2ce62655c724394a877518690469391', '1000000643', '0');



-- 删除了tataggroup表, 内容添加为字典码值, 新增角色类型标签
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, CREATETIME, createuser, version, status, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('标签类型', 'TAGTYPE', '用户', '001', NULL, 1, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, CREATETIME, createuser, version, status, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('标签类型', 'TAGTYPE', '组织', '002', NULL, 2, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, CREATETIME, createuser, version, status, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('标签类型', 'TAGTYPE', '角色', '003', NULL, 3, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');

-- 新增账号来源字典
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, CREATETIME, createuser, version, status, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('账户来源', 'ACCOUNTSOURCE', '系统管理录入', '1', NULL, 1, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, CREATETIME, createuser, version, status, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('账户来源', 'ACCOUNTSOURCE', '账号申请审核', '2', NULL, 2, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, CREATETIME, createuser, version, status, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('账户来源', 'ACCOUNTSOURCE', '注册通道注册', '3', NULL, 3, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');

-- 修改账号类型字典
DELETE FROM tadict WHERE type = 'ACCOUNTTYPE';
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, CREATETIME, createuser, version, status, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('账户类型', 'ACCOUNTTYPE', '经办管理账号', '1', NULL, 1, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, CREATETIME, createuser, version, status, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('账户类型', 'ACCOUNTTYPE', '自然人账号', '2', NULL, 2, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');

-- 新增账号申请类型字典
INSERT INTO tadict (NAME,`TYPE`,LABEL,VALUE,PARENTVALUE,sort,AUTHORITY,cssclass,CSSSTYLE,REMARKS,CREATETIME,CREATEUSER,VERSION,STATUS,FIELD01,FIELD02,FIELD03,FIELD04,FIELD05,`SYSTEM`,NEWTYPE) VALUES ('申请类型','APPLY_TYPE','账号申请','1',NULL,10,'0',NULL,NULL,NULL,'2023-07-31 00:00:00','1','0','1',NULL,NULL,NULL,NULL,NULL,'1','0');

-- 新增账号申请状态字典
INSERT INTO tadict (NAME,`TYPE`,LABEL,VALUE,PARENTVALUE,sort,AUTHORITY,cssclass,CSSSTYLE,REMARKS,CREATETIME,CREATEUSER,VERSION,STATUS,FIELD01,FIELD02,FIELD03,FIELD04,FIELD05,`SYSTEM`,NEWTYPE) VALUES ('申请状态','APPLY_STATUS','待提交','1',NULL,10,'0',NULL,NULL,NULL,'2023-07-31 00:00:00','1','0','1',NULL,NULL,NULL,NULL,NULL,'1','0');
INSERT INTO tadict (NAME,`TYPE`,LABEL,VALUE,PARENTVALUE,sort,AUTHORITY,cssclass,CSSSTYLE,REMARKS,CREATETIME,CREATEUSER,VERSION,STATUS,FIELD01,FIELD02,FIELD03,FIELD04,FIELD05,`SYSTEM`,NEWTYPE) VALUES ('申请状态','APPLY_STATUS','待审核','2',NULL,20,'0',NULL,NULL,NULL,'2023-07-31 00:00:00','1','0','1',NULL,NULL,NULL,NULL,NULL,'1','0');
INSERT INTO tadict (NAME,`TYPE`,LABEL,VALUE,PARENTVALUE,sort,AUTHORITY,cssclass,CSSSTYLE,REMARKS,CREATETIME,CREATEUSER,VERSION,STATUS,FIELD01,FIELD02,FIELD03,FIELD04,FIELD05,`SYSTEM`,NEWTYPE) VALUES ('申请状态','APPLY_STATUS','审核通过','3',NULL,30,'0',NULL,NULL,NULL,'2023-07-31 00:00:00','1','0','1',NULL,NULL,NULL,NULL,NULL,'1','0');
INSERT INTO tadict (NAME,`TYPE`,LABEL,VALUE,PARENTVALUE,sort,AUTHORITY,cssclass,CSSSTYLE,REMARKS,CREATETIME,CREATEUSER,VERSION,STATUS,FIELD01,FIELD02,FIELD03,FIELD04,FIELD05,`SYSTEM`,NEWTYPE) VALUES ('申请状态','APPLY_STATUS','审核不通过','4',NULL,40,'0',NULL,NULL,NULL,'2023-07-31 00:00:00','1','0','1',NULL,NULL,NULL,NULL,NULL,'1','0');
INSERT INTO tadict (NAME,`TYPE`,LABEL,VALUE,PARENTVALUE,sort,AUTHORITY,cssclass,CSSSTYLE,REMARKS,CREATETIME,CREATEUSER,VERSION,STATUS,FIELD01,FIELD02,FIELD03,FIELD04,FIELD05,`SYSTEM`,NEWTYPE) VALUES ('申请状态','APPLY_STATUS','作废','5',NULL,50,'0',NULL,NULL,NULL,'2023-07-31 00:00:00','1','0','1',NULL,NULL,NULL,NULL,NULL,'1','0');


-- 新增权限标识字典类型
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, CREATETIME, createuser, version, status, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('权限标识类型', 'AUTHORITY', '默认', '0', NULL, 1, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');

-

-- 更新dict视图
create OR REPLACE view v_dict (name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks,
                               createtime, createuser, version, status, field01, field02, field03, field04, field05,
                               `SYSTEM`, newtype) as
select x0.name,
       x0.type,
       x0.label,
       x0.value,
       x0.parentvalue,
       x0.sort,
       x0.authority,
       x0.cssclass,
       x0.cssstyle,
       x0.remarks,
       x0.createtime,
       x0.createuser,
       x0.version,
       x0.status,
       x0.field01,
       x0.field02,
       x0.field03,
       x0.field04,
       x0.field05,
       x0.`SYSTEM`,
       x0.newtype
from tadict x0;

-- 新增行政区划、自定义组织操作日志字典

INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作对象类型', 'OPOBJTYPE', '行政区划', '09', NULL, 90, '0', NULL, NULL, NULL, NOW(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作对象类型', 'OPOBJTYPE', '自定义组织', '10', NULL, 100, '0', NULL, NULL, NULL, NOW(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');

INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '新增行政区划', '51', NULL, 510, '0', NULL, NULL, NULL, NOW(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '编辑行政区划', '52', NULL, 520, '0', NULL, NULL, NULL, NOW(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '禁用行政区划', '53', NULL, 530, '0', NULL, NULL, NULL, NOW(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '启用行政区划', '54', NULL, 540, '0', NULL, NULL, NULL, NOW(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '删除行政区划', '55', NULL, 550, '0', NULL, NULL, NULL, NOW(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '新增自定义组织', '56', NULL, 560, '0', NULL, NULL, NULL, NOW(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '编辑自定义组织', '57', NULL, 570, '0', NULL, NULL, NULL, NOW(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '禁用自定义组织', '58', NULL, 580, '0', NULL, NULL, NULL, NOW(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '启用自定义组织', '59', NULL, 590, '0', NULL, NULL, NULL, NOW(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '删除自定义组织', '60', NULL, 600, '0', NULL, NULL, NULL, NOW(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '相似权限授权', '67', NULL, 670, '0', NULL, NULL, NULL, NOW(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('菜单打开方式','RESOURCEOPENMODE','工作页','1',NULL,10,'0',NULL,NULL,NULL,NOW(),'1','0','1',NULL,NULL,NULL,NULL,NULL,'1','0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('菜单打开方式','RESOURCEOPENMODE','浏览器页','2',NULL,20,'0',NULL,NULL,NULL,NOW(),'1','0','1',NULL,NULL,NULL,NULL,NULL,'1','0');
INSERT INTO tadict(NAME, TYPE, label, VALUE, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, VERSION, STATUS, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('菜单打开方式','RESOURCEOPENMODE','弹窗','3',NULL,30,'0',NULL,NULL,NULL,NOW(),'1','0','1',NULL,NULL,NULL,NULL,NULL,'1','0');

-- 添加审核管理员权限菜单参数
INSERT INTO taparam(PARAMID, PARAMNAME, VALUESCOPE, CODE, PARAMDESC, VALUE, RESOURCEID) VALUES('1000000442', 'isExamine', '1,0', '', '是否', '1', 'e7542892ef424e809c3bb8cfa8c0051b');

-- 用户可管理字段标签类型字典
INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('组织页面标签类型', 'EXTENDORGTAG', '组织信息', '1', NULL, 10, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('组织页面标签类型', 'EXTENDORGTAG', '其他信息', '2', NULL, 20, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('组织页面标签类型', 'EXTENDORGTAG', '扩展信息', '3', NULL, 30, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('功能资源页面标签类型', 'EXTENDRESOURCETAG', '基本信息', '1', NULL, 10, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('功能资源页面标签类型', 'EXTENDRESOURCETAG', '更多信息', '2', NULL, 20, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('用户页面标签类型', 'EXTENDUSERTAG', '账号信息', '1', NULL, 10, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('用户页面标签类型', 'EXTENDUSERTAG', '人员信息', '2', NULL, 20, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('用户页面标签类型', 'EXTENDUSERTAG', '其他信息', '3', NULL, 30, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');



INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '更改使用权限有效时间', '90', NULL, 870, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '账号删除和角色的关联关系', '89', NULL, 860, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '账号新增和角色的关联关系', '88', NULL, 850, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '组织删除和角色的关联关系', '87', NULL, 840, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '组织新增和角色的关联关系', '86', NULL, 830, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '角色删除和组织的关联关系', '85', NULL, 820, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '角色新增和组织的关联关系', '84', NULL, 810, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '修改角色子组织可见性', '83', NULL, 800, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '更改账号自定义组织权限（收回）', '82', NULL, 790, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '更改账号自定义组织权限（授予）', '81', NULL, 780, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict
(NAME, `TYPE`, LABEL, VALUE, PARENTVALUE, sort, AUTHORITY, cssclass, CSSSTYLE, REMARKS, CREATETIME, CREATEUSER, VERSION, STATUS, FIELD01, FIELD02, FIELD03, FIELD04, FIELD05, `SYSTEM`, NEWTYPE)
VALUES('操作类型', 'OPTYPE', '管理员角色组织管理范围类型修改', '80', NULL, 770, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');

insert into TAREDISSEQUENCE(BIZ_TAG, START_INDEX, MAX_ID, STEP) values ('HIBERNATE_SEQUENCE',0,0,100);

INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '彻底删除', '62', NULL, 750, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '回收站还原', '65', NULL, 760, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '加入回收站', '64', NULL, 680, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '人员导入', '68', NULL, 690, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '导入组织', '69', NULL, 700, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '资源菜单导入', '70', NULL, 710, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '导出人员', '71', NULL, 720, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '导出组织', '72', NULL, 730, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
INSERT INTO tadict(name, type, label, value, parentvalue, sort, authority, cssclass, cssstyle, remarks, createtime, createuser, version, status, field01, field02, field03, field04, field05, `SYSTEM`, newtype) VALUES ('操作类型', 'OPTYPE', '导出资源菜单', '73', NULL, 740, '0', NULL, NULL, NULL, now(), '1', '0', '1', NULL, NULL, NULL, NULL, NULL, '1', '0');
