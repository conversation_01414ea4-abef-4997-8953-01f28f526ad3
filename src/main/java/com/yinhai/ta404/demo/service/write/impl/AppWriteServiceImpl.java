package com.yinhai.ta404.demo.service.write.impl;


import com.yinhai.ta404.demo.entity.AppPo;
import com.yinhai.ta404.demo.mapper.read.AppReadMapper;
import com.yinhai.ta404.demo.mapper.write.AppWriteMapper;
import com.yinhai.ta404.demo.service.write.AppWriteService;
import com.yinhai.ta404.demo.vo.AppVo;
import com.yinhai.ta404.core.exception.AppException;
import com.yinhai.ta404.core.service.BaseService;
import com.yinhai.ta404.core.transaction.annotation.TaTransactional;
import com.yinhai.ta404.core.utils.UUIDUtils;
import com.yinhai.ta404.core.utils.ValidateUtil;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * (CloudApp)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-16 10:14:08
 */
@Service
@TaTransactional
public class AppWriteServiceImpl extends BaseService implements AppWriteService {
    @Resource
    private AppWriteMapper applicationPoWriteMapper;
    @Resource
    private AppReadMapper appReadMapper;


    @Override
    public AppPo addCloudAppInfo(AppVo cloudAppInfoVo) {
        if (ValidateUtil.isEmpty(cloudAppInfoVo)) {
            throw new AppException("CloudAppInfoVo cannot be null or empty");
        }
        AppPo cloudApp = new AppPo();
        BeanUtils.copyProperties(cloudAppInfoVo, cloudApp);
//        cloudApp.setId(UUIDUtils.getUUID());
        int result = applicationPoWriteMapper.insertApp(cloudApp);
        if (result > 0) {
            return cloudApp;
        } else {
            throw new AppException("Failed to insert CloudApp");
        }
    }

    @Override
    public AppPo updateCloudAppInfo(AppVo cloudAppInfoVo) {
        if (ValidateUtil.isEmpty(cloudAppInfoVo)) {
            throw new AppException("CloudAppInfoVo cannot be null or empty");
        }
        AppPo cloudApp = new AppPo();
        BeanUtils.copyProperties(cloudAppInfoVo, cloudApp);
        int result = applicationPoWriteMapper.updateApp(cloudApp);
        if (result > 0) {
            return cloudApp;
        } else {
            throw new AppException("Failed to update CloudApp");
        }
    }


    @Override
    public void deleteApp(String appId) {
        if (ValidateUtil.isEmpty(appId)) {
            throw new AppException("App ID cannot be null or empty");
        }
        int result = applicationPoWriteMapper.deleteAppById(appId);
        if (result <= 0) {
            throw new AppException("Failed to delete CloudApp");
        }
    }




}
